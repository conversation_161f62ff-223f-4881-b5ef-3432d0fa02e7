# FraudShield Environment Configuration
# Copy this file to .env and update the values

# Application Environment
ENVIRONMENT=development
DEBUG=true
VERSION=1.0.0

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# API Configuration
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/fraudshield
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_TRANSACTIONS=transactions
KAFKA_TOPIC_FRAUD_ALERTS=fraud-alerts

# ML Service Configuration
ML_SERVICE_URL=http://localhost:8001
ML_MODEL_CACHE_TTL=86400
ML_PREDICTION_TIMEOUT=5

# Feature Store Configuration (Optional)
FEATURE_STORE_URL=http://localhost:8002

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Fraud Detection Thresholds
FRAUD_THRESHOLD_LOW=0.3
FRAUD_THRESHOLD_HIGH=0.7

# Transaction Limits
MAX_TRANSACTION_AMOUNT=1000000.0
MIN_TRANSACTION_AMOUNT=0.01

# Email Configuration (for alerts)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Alert Configuration
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>,<EMAIL>

# AWS Configuration (for production)
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ML_SERVICE_URL=http://localhost:8001
REACT_APP_WEBSOCKET_URL=ws://localhost:8000/ws
