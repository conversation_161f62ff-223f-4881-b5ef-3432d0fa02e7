name: MLOps CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - 'ml-service/**'
      - 'frontend/**'
      - 'stream-processor/**'
      - 'feature-store/**'
      - 'data-quality-monitor/**'
  pull_request:
    branches: [ main ]
  schedule:
    # Trigger model retraining weekly
    - cron: '0 2 * * 0'
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      retrain_model:
        description: 'Force model retraining'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Detect changes to determine which services to build
  changes:
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.changes.outputs.backend }}
      ml-service: ${{ steps.changes.outputs.ml-service }}
      frontend: ${{ steps.changes.outputs.frontend }}
      stream-processor: ${{ steps.changes.outputs.stream-processor }}
      feature-store: ${{ steps.changes.outputs.feature-store }}
      data-quality-monitor: ${{ steps.changes.outputs.data-quality-monitor }}
      infrastructure: ${{ steps.changes.outputs.infrastructure }}
    steps:
    - uses: actions/checkout@v4
    - uses: dorny/paths-filter@v2
      id: changes
      with:
        filters: |
          backend:
            - 'backend/**'
          ml-service:
            - 'ml-service/**'
          frontend:
            - 'frontend/**'
          stream-processor:
            - 'stream-processor/**'
          feature-store:
            - 'feature-store/**'
          data-quality-monitor:
            - 'data-quality-monitor/**'
          infrastructure:
            - 'infrastructure/**'
            - '.github/workflows/**'

  # Security and compliance scanning
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run Semgrep security scan
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/python
          p/javascript

    - name: Run dependency check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'fraudshield'
        path: '.'
        format: 'ALL'

    - name: Upload dependency check results
      uses: actions/upload-artifact@v3
      with:
        name: dependency-check-report
        path: reports/

  # Code quality and testing for Python services
  test-python:
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.backend == 'true' || needs.changes.outputs.ml-service == 'true' || needs.changes.outputs.stream-processor == 'true' || needs.changes.outputs.feature-store == 'true' || needs.changes.outputs.data-quality-monitor == 'true'

    strategy:
      matrix:
        service: [backend, ml-service, stream-processor, feature-store, data-quality-monitor]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: ${{ matrix.service }}/requirements.txt

    - name: Install dependencies
      run: |
        cd ${{ matrix.service }}
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio black isort flake8 mypy bandit safety

    - name: Run code formatting checks
      run: |
        cd ${{ matrix.service }}
        black --check --diff .
        isort --check-only --diff .

    - name: Run linting
      run: |
        cd ${{ matrix.service }}
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Run type checking
      run: |
        cd ${{ matrix.service }}
        mypy . --ignore-missing-imports

    - name: Run security checks
      run: |
        cd ${{ matrix.service }}
        bandit -r . -f json -o bandit-report.json || true
        safety check --json --output safety-report.json || true

    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        ENVIRONMENT: test
      run: |
        cd ${{ matrix.service }}
        pytest tests/ -v --cov=. --cov-report=xml --cov-report=html --cov-fail-under=80

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./${{ matrix.service }}/coverage.xml
        flags: ${{ matrix.service }}
        name: ${{ matrix.service }}-coverage

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports-${{ matrix.service }}
        path: |
          ${{ matrix.service }}/bandit-report.json
          ${{ matrix.service }}/safety-report.json

  # Frontend testing
  test-frontend:
    runs-on: ubuntu-latest
    needs: changes
    if: needs.changes.outputs.frontend == 'true'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd frontend
        npm ci

    - name: Run linting
      run: |
        cd frontend
        npm run lint
        npm run type-check

    - name: Run tests
      run: |
        cd frontend
        npm run test:coverage

    - name: Run E2E tests
      run: |
        cd frontend
        npm run test:e2e:headless

    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # Build and push Docker images
  build:
    runs-on: ubuntu-latest
    needs: [changes, security-scan, test-python, test-frontend]
    if: always() && (needs.test-python.result == 'success' || needs.test-python.result == 'skipped') && (needs.test-frontend.result == 'success' || needs.test-frontend.result == 'skipped')

    strategy:
      matrix:
        service: [backend, ml-service, frontend, stream-processor, feature-store, data-quality-monitor]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.service }}
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Run container security scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.service }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-${{ matrix.service }}.sarif'

    - name: Upload container scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-${{ matrix.service }}.sarif'

  # ML Model Training and Validation
  model-training:
    runs-on: ubuntu-latest
    needs: [changes, test-python]
    if: github.event.inputs.retrain_model == 'true' || github.event_name == 'schedule' || needs.changes.outputs.ml-service == 'true'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install ML dependencies
      run: |
        cd ml-service
        pip install -r requirements.txt
        pip install mlflow dvc[s3] great-expectations

    - name: Configure MLflow
      env:
        MLFLOW_TRACKING_URI: ${{ secrets.MLFLOW_TRACKING_URI }}
        MLFLOW_S3_ENDPOINT_URL: ${{ secrets.MLFLOW_S3_ENDPOINT_URL }}
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: |
        mlflow server --host 0.0.0.0 --port 5000 &
        sleep 10

    - name: Data validation
      run: |
        cd ml-service
        python scripts/validate_data.py

    - name: Feature engineering
      run: |
        cd ml-service
        python scripts/feature_engineering.py

    - name: Model training
      env:
        MLFLOW_EXPERIMENT_NAME: fraud-detection-${{ github.sha }}
      run: |
        cd ml-service
        python scripts/train_model.py --experiment-name $MLFLOW_EXPERIMENT_NAME

    - name: Model validation
      run: |
        cd ml-service
        python scripts/validate_model.py

    - name: Model comparison
      run: |
        cd ml-service
        python scripts/compare_models.py --challenger-run-id ${{ env.CHALLENGER_RUN_ID }}

    - name: Register model
      if: success()
      run: |
        cd ml-service
        python scripts/register_model.py --model-name fraud-detection --stage Staging

  # Deploy to staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build, model-training]
    if: github.ref == 'refs/heads/develop' || github.event.inputs.deploy_environment == 'staging'
    environment: staging

    steps:
    - uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

    - name: Deploy to staging
      run: |
        cd infrastructure/k8s
        kubectl apply -f namespace.yaml
        kubectl apply -f staging/
        kubectl set image deployment/fraudshield-backend backend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:${{ github.sha }} -n fraudshield-staging
        kubectl set image deployment/fraudshield-ml-service ml-service=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/ml-service:${{ github.sha }} -n fraudshield-staging
        kubectl set image deployment/fraudshield-frontend frontend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }} -n fraudshield-staging

    - name: Wait for deployment
      run: |
        kubectl rollout status deployment/fraudshield-backend -n fraudshield-staging --timeout=300s
        kubectl rollout status deployment/fraudshield-ml-service -n fraudshield-staging --timeout=300s
        kubectl rollout status deployment/fraudshield-frontend -n fraudshield-staging --timeout=300s

    - name: Run smoke tests
      run: |
        cd tests/e2e
        python smoke_tests.py --environment staging

    - name: Run integration tests
      run: |
        cd tests/integration
        pytest test_api_integration.py --environment staging

    - name: Performance tests
      run: |
        cd tests/performance
        python load_test.py --environment staging --duration 300

  # Deploy to production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/main' || github.event.inputs.deploy_environment == 'production'
    environment: production

    steps:
    - uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

    - name: Blue/Green deployment
      run: |
        cd infrastructure/k8s
        # Deploy to green environment
        kubectl apply -f production/green/
        kubectl set image deployment/fraudshield-backend-green backend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/backend:${{ github.sha }} -n fraudshield-production
        kubectl set image deployment/fraudshield-ml-service-green ml-service=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/ml-service:${{ github.sha }} -n fraudshield-production
        kubectl set image deployment/fraudshield-frontend-green frontend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }} -n fraudshield-production

    - name: Wait for green deployment
      run: |
        kubectl rollout status deployment/fraudshield-backend-green -n fraudshield-production --timeout=600s
        kubectl rollout status deployment/fraudshield-ml-service-green -n fraudshield-production --timeout=600s
        kubectl rollout status deployment/fraudshield-frontend-green -n fraudshield-production --timeout=600s

    - name: Health checks
      run: |
        cd tests/health
        python health_check.py --environment production-green

    - name: Switch traffic to green
      run: |
        cd infrastructure/k8s
        kubectl patch service fraudshield-backend -p '{"spec":{"selector":{"version":"green"}}}' -n fraudshield-production
        kubectl patch service fraudshield-ml-service -p '{"spec":{"selector":{"version":"green"}}}' -n fraudshield-production
        kubectl patch service fraudshield-frontend -p '{"spec":{"selector":{"version":"green"}}}' -n fraudshield-production

    - name: Monitor deployment
      run: |
        cd monitoring
        python monitor_deployment.py --duration 600 --environment production

    - name: Cleanup blue environment
      if: success()
      run: |
        kubectl delete deployment fraudshield-backend-blue -n fraudshield-production
        kubectl delete deployment fraudshield-ml-service-blue -n fraudshield-production
        kubectl delete deployment fraudshield-frontend-blue -n fraudshield-production

  # Model deployment to production
  deploy-model-production:
    runs-on: ubuntu-latest
    needs: [model-training, deploy-production]
    if: success() && github.ref == 'refs/heads/main'
    environment: production

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install MLflow
      run: pip install mlflow boto3

    - name: Promote model to production
      env:
        MLFLOW_TRACKING_URI: ${{ secrets.MLFLOW_TRACKING_URI }}
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: |
        cd ml-service
        python scripts/promote_model.py --model-name fraud-detection --stage Production

    - name: Update model serving
      run: |
        kubectl patch configmap ml-service-config -p '{"data":{"MODEL_VERSION":"${{ env.NEW_MODEL_VERSION }}"}}' -n fraudshield-production
        kubectl rollout restart deployment/fraudshield-ml-service -n fraudshield-production

  # Cleanup and notifications
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy-production, deploy-model-production]
    if: always()

    steps:
    - name: Clean up artifacts
      run: |
        echo "Cleaning up temporary artifacts..."

    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
