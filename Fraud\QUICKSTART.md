# FraudShield Quick Start Guide

Welcome to FraudShield! This guide will help you get the fraud detection platform up and running in minutes.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Docker Desktop** (Windows/macOS) or **Docker + Docker Compose** (Linux)
- **Git** (to clone the repository)
- **4GB+ RAM** available for containers

Optional for development:
- **Node.js 18+** (for frontend development)
- **Python 3.11+** (for backend development)

## Quick Setup (5 minutes)

### Step 1: Clone and Navigate
```bash
git clone <your-repository-url>
cd FraudShield
```

### Step 2: Run Setup Script

**Windows:**
```cmd
scripts\setup.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### Step 3: Access the Application

Once setup is complete, access these URLs:

- **🌐 Frontend Application**: http://localhost:3000
- **📊 API Documentation**: http://localhost:8000/docs
- **🤖 ML Service**: http://localhost:8001/docs
- **📈 Grafana Dashboard**: http://localhost:3001 (admin/admin)
- **🔍 Prometheus Metrics**: http://localhost:9090

## Test the Fraud Detection

### Using the Web Interface

1. Open http://localhost:3000
2. Fill in the transaction form with sample data:
   - **Type**: PAYMENT
   - **Amount**: 1000
   - **Origin Account**: C123456789
   - **Destination Account**: M987654321
   - **Balances**: Use the pre-filled values
3. Click "Check for Fraud"
4. View the risk analysis results

### Using the API

Test the API directly with curl:

```bash
curl -X POST "http://localhost:8000/api/v1/transactions/score" \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_id": "test_123",
    "type": "PAYMENT",
    "amount": 1000.50,
    "originator": {
      "account_id": "C123456789",
      "current_balance": 5000.00
    },
    "beneficiary": {
      "account_id": "M987654321",
      "current_balance": 0.00
    }
  }'
```

Expected response:
```json
{
  "transaction_id": "test_123",
  "fraud_score": 0.15,
  "is_fraudulent": false,
  "decision": "ALLOW",
  "reason_codes": ["LOW_RISK_AMOUNT"],
  "timestamp": "2023-12-01T10:30:01Z",
  "processing_time_ms": 45
}
```

## Understanding the Results

### Risk Levels
- **🟢 Low Risk (0-49%)**: Transaction appears legitimate
- **🟡 Medium Risk (50-79%)**: Requires review
- **🔴 High Risk (80-100%)**: Likely fraudulent

### Decision Types
- **ALLOW**: Process the transaction
- **REVIEW**: Manual review required
- **BLOCK**: Reject the transaction

## Sample Test Cases

Try these different scenarios:

### Low Risk Transaction
```json
{
  "type": "PAYMENT",
  "amount": 50.00,
  "originator": {"account_id": "C123456789", "current_balance": 1000.00},
  "beneficiary": {"account_id": "M987654321", "current_balance": 0.00}
}
```

### High Risk Transaction
```json
{
  "type": "CASH_OUT",
  "amount": 50000.00,
  "originator": {"account_id": "C123456789", "current_balance": 50000.00},
  "beneficiary": {"account_id": "C987654321", "current_balance": 0.00}
}
```

## Monitoring and Observability

### View Real-time Metrics
1. Open Grafana: http://localhost:3001
2. Login with admin/admin
3. Import dashboards for:
   - Transaction volume and fraud rates
   - API performance metrics
   - ML model performance

### Check Service Health
```bash
# Backend health
curl http://localhost:8000/health

# ML service health
curl http://localhost:8001/health

# View service logs
docker-compose logs -f backend
docker-compose logs -f ml-service
```

## Development Mode

### Backend Development
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Install dependencies
cd backend
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

### ML Service Development
```bash
cd ml-service
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8001
```

## Common Commands

### Docker Management
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f [service-name]

# Rebuild and restart
docker-compose up -d --build

# Check service status
docker-compose ps
```

### Database Management
```bash
# Access PostgreSQL
docker-compose exec postgres psql -U fraudshield -d fraudshield

# View Redis data
docker-compose exec redis redis-cli
```

## Troubleshooting

### Services Not Starting
1. Check Docker is running: `docker --version`
2. Check port availability: `netstat -an | findstr :8000`
3. View service logs: `docker-compose logs [service-name]`

### Model Not Loading
1. Ensure `fraud_detection_model.pkl` is in `data/models/`
2. Check ML service logs: `docker-compose logs ml-service`

### Frontend Not Loading
1. Check if port 3000 is available
2. Verify backend is running: `curl http://localhost:8000/health`

### Performance Issues
1. Increase Docker memory allocation (4GB+ recommended)
2. Check system resources: `docker stats`

## Next Steps

Once you have the basic system running:

1. **Explore the API**: Use the interactive docs at http://localhost:8000/docs
2. **Customize Configuration**: Edit `.env` file for your needs
3. **Add Your Data**: Replace sample data with real transaction data
4. **Train Custom Models**: Implement your own fraud detection models
5. **Deploy to Production**: Use the Terraform configurations for cloud deployment

## Getting Help

- **Documentation**: Check the `docs/` directory
- **API Reference**: http://localhost:8000/docs
- **Logs**: `docker-compose logs -f`
- **Health Checks**: http://localhost:8000/health

## What's Next?

This completes **Phase 1: Project Architecture and Cloud Setup**. 

Ready for **Phase 2: Data Engineering**? This will include:
- Real-time data ingestion pipelines
- Feature engineering and storage
- Advanced data validation and quality checks

---

🎉 **Congratulations!** You now have a production-ready fraud detection platform running locally.
