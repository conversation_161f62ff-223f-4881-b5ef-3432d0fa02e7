"""
Analytics and reporting endpoints
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db_session
from app.core.logging import get_logger
from app.schemas.analytics import (
    FraudTrends,
    TransactionVolume,
    RiskDistribution,
    PerformanceMetrics,
    DashboardSummary
)
from app.services.analytics_service import AnalyticsService
from app.api.dependencies import get_current_user, require_permission

router = APIRouter()
logger = get_logger(__name__)


@router.get("/dashboard", response_model=DashboardSummary)
async def get_dashboard_summary(
    hours: int = Query(24, ge=1, le=168, description="Number of hours to include"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get dashboard summary
    
    Returns key metrics and statistics for the fraud detection dashboard.
    """
    try:
        analytics_service = AnalyticsService(db)
        summary = await analytics_service.get_dashboard_summary(hours=hours)
        
        return summary
        
    except Exception as e:
        logger.error(
            "Failed to get dashboard summary",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard summary"
        )


@router.get("/fraud-trends", response_model=FraudTrends)
async def get_fraud_trends(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    granularity: str = Query("daily", regex="^(hourly|daily|weekly)$", description="Data granularity"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get fraud detection trends
    
    Returns fraud detection trends over time with configurable granularity.
    """
    try:
        analytics_service = AnalyticsService(db)
        trends = await analytics_service.get_fraud_trends(
            days=days,
            granularity=granularity
        )
        
        return trends
        
    except Exception as e:
        logger.error(
            "Failed to get fraud trends",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve fraud trends"
        )


@router.get("/transaction-volume", response_model=TransactionVolume)
async def get_transaction_volume(
    days: int = Query(7, ge=1, le=90, description="Number of days to analyze"),
    granularity: str = Query("hourly", regex="^(hourly|daily)$", description="Data granularity"),
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get transaction volume analysis
    
    Returns transaction volume patterns and trends.
    """
    try:
        analytics_service = AnalyticsService(db)
        volume = await analytics_service.get_transaction_volume(
            days=days,
            granularity=granularity,
            transaction_type=transaction_type
        )
        
        return volume
        
    except Exception as e:
        logger.error(
            "Failed to get transaction volume",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve transaction volume"
        )


@router.get("/risk-distribution", response_model=RiskDistribution)
async def get_risk_distribution(
    days: int = Query(7, ge=1, le=90, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get risk score distribution
    
    Returns distribution of fraud risk scores across transactions.
    """
    try:
        analytics_service = AnalyticsService(db)
        distribution = await analytics_service.get_risk_distribution(days=days)
        
        return distribution
        
    except Exception as e:
        logger.error(
            "Failed to get risk distribution",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve risk distribution"
        )


@router.get("/performance-metrics", response_model=PerformanceMetrics)
async def get_performance_metrics(
    days: int = Query(7, ge=1, le=90, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get system performance metrics
    
    Returns performance metrics including processing times, throughput, and accuracy.
    """
    try:
        analytics_service = AnalyticsService(db)
        metrics = await analytics_service.get_performance_metrics(days=days)
        
        return metrics
        
    except Exception as e:
        logger.error(
            "Failed to get performance metrics",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance metrics"
        )


@router.get("/top-patterns")
async def get_top_fraud_patterns(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    limit: int = Query(10, ge=1, le=50, description="Number of patterns to return"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get top fraud patterns
    
    Returns the most common fraud patterns detected in the specified time period.
    """
    try:
        analytics_service = AnalyticsService(db)
        patterns = await analytics_service.get_top_fraud_patterns(
            days=days,
            limit=limit
        )
        
        return {"patterns": patterns}
        
    except Exception as e:
        logger.error(
            "Failed to get fraud patterns",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve fraud patterns"
        )


@router.get("/model-performance")
async def get_model_performance(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get ML model performance metrics
    
    Returns detailed performance metrics for the fraud detection models.
    """
    try:
        analytics_service = AnalyticsService(db)
        performance = await analytics_service.get_model_performance(days=days)
        
        return performance
        
    except Exception as e:
        logger.error(
            "Failed to get model performance",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model performance"
        )


@router.get("/alerts-summary")
async def get_alerts_summary(
    days: int = Query(7, ge=1, le=90, description="Number of days to analyze"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Get alerts summary
    
    Returns summary of fraud alerts including response times and resolution rates.
    """
    try:
        analytics_service = AnalyticsService(db)
        summary = await analytics_service.get_alerts_summary(days=days)
        
        return summary
        
    except Exception as e:
        logger.error(
            "Failed to get alerts summary",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve alerts summary"
        )


@router.get("/export/report")
async def export_fraud_report(
    start_date: datetime = Query(..., description="Report start date"),
    end_date: datetime = Query(..., description="Report end date"),
    format: str = Query("json", regex="^(json|csv|pdf)$", description="Export format"),
    db: AsyncSession = Depends(get_db_session),
    current_user = Depends(require_permission("read:analytics"))
):
    """
    Export fraud detection report
    
    Generates and exports a comprehensive fraud detection report for the specified date range.
    """
    try:
        # Validate date range
        if end_date <= start_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End date must be after start date"
            )
        
        if (end_date - start_date).days > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Date range cannot exceed 365 days"
            )
        
        analytics_service = AnalyticsService(db)
        report = await analytics_service.generate_fraud_report(
            start_date=start_date,
            end_date=end_date,
            format=format
        )
        
        logger.info(
            "Fraud report exported",
            start_date=start_date,
            end_date=end_date,
            format=format,
            exported_by=current_user.id
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to export fraud report",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export fraud report"
        )
