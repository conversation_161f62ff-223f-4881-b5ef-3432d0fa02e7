"""
Authentication endpoints
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db_session
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    verify_password,
    get_password_hash
)
from app.core.exceptions import AuthenticationException
from app.core.logging import get_logger
from app.schemas.auth import (
    Token,
    TokenRefresh,
    UserLogin,
    UserRegister,
    UserResponse
)
from app.services.user_service import UserService

router = APIRouter()
logger = get_logger(__name__)


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db_session)
):
    """
    User login endpoint
    
    Authenticates user credentials and returns access and refresh tokens.
    """
    try:
        user_service = UserService(db)
        
        # Authenticate user
        user = await user_service.authenticate_user(
            email=form_data.username,
            password=form_data.password
        )
        
        if not user:
            logger.warning(
                "Login attempt failed",
                email=form_data.username,
                reason="invalid_credentials"
            )
            raise AuthenticationException("Incorrect email or password")
        
        if not user.is_active:
            logger.warning(
                "Login attempt failed",
                email=form_data.username,
                reason="inactive_user"
            )
            raise AuthenticationException("User account is inactive")
        
        # Create tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=user.id,
            expires_delta=access_token_expires,
            additional_claims={
                "email": user.email,
                "role": user.role,
                "permissions": user.permissions
            }
        )
        
        refresh_token = create_refresh_token(subject=user.id)
        
        # Update last login
        await user_service.update_last_login(user.id)
        
        logger.info(
            "User logged in successfully",
            user_id=user.id,
            email=user.email
        )
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except AuthenticationException:
        raise
    except Exception as e:
        logger.error(
            "Login failed",
            email=form_data.username,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db_session)
):
    """
    User registration endpoint
    
    Creates a new user account with the provided information.
    """
    try:
        user_service = UserService(db)
        
        # Check if user already exists
        existing_user = await user_service.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create new user
        user = await user_service.create_user(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            role=user_data.role or "user"
        )
        
        logger.info(
            "User registered successfully",
            user_id=user.id,
            email=user.email
        )
        
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Registration failed",
            email=user_data.email,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Refresh access token
    
    Uses a valid refresh token to generate a new access token.
    """
    try:
        # Verify refresh token
        payload = verify_token(token_data.refresh_token)
        
        if payload.get("type") != "refresh":
            raise AuthenticationException("Invalid token type")
        
        user_id = payload.get("sub")
        if not user_id:
            raise AuthenticationException("Invalid token")
        
        # Get user
        user_service = UserService(db)
        user = await user_service.get_user(user_id)
        
        if not user or not user.is_active:
            raise AuthenticationException("User not found or inactive")
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=user.id,
            expires_delta=access_token_expires,
            additional_claims={
                "email": user.email,
                "role": user.role,
                "permissions": user.permissions
            }
        )
        
        logger.info(
            "Token refreshed successfully",
            user_id=user.id
        )
        
        return Token(
            access_token=access_token,
            refresh_token=token_data.refresh_token,  # Keep same refresh token
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except AuthenticationException:
        raise
    except Exception as e:
        logger.error(
            "Token refresh failed",
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout():
    """
    User logout endpoint
    
    In a stateless JWT system, logout is handled client-side by discarding tokens.
    This endpoint can be used for logging purposes or token blacklisting if implemented.
    """
    logger.info("User logout requested")
    
    return {"message": "Logged out successfully"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user = Depends(get_current_user)
):
    """
    Get current user information
    
    Returns the authenticated user's profile information.
    """
    return UserResponse.from_orm(current_user)
