"""
Redis cache implementation for FraudShield
"""

import json
import pickle
from typing import Any, Optional, Union
from datetime import timedel<PERSON>

import redis.asyncio as redis
from redis.asyncio import Redis

from .config import settings
from .logging import get_logger

logger = get_logger(__name__)

# Global Redis connection
_redis_client: Optional[Redis] = None


async def get_redis() -> Redis:
    """Get Redis client instance"""
    global _redis_client
    
    if _redis_client is None:
        _redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=False,  # We'll handle encoding ourselves
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # Test connection
        try:
            await _redis_client.ping()
            logger.info("Redis connection established")
        except Exception as e:
            logger.error("Failed to connect to Redis", error=str(e))
            raise
    
    return _redis_client


async def close_redis():
    """Close Redis connection"""
    global _redis_client
    
    if _redis_client:
        await _redis_client.close()
        _redis_client = None
        logger.info("Redis connection closed")


class CacheService:
    """Redis cache service for FraudShield"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
    
    async def initialize(self):
        """Initialize cache service"""
        self.redis = await get_redis()
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache"""
        if not self.redis:
            await self.initialize()
        
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            
            # Try to deserialize as JSON first, then pickle
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return pickle.loads(value)
                
        except Exception as e:
            logger.warning("Cache get failed", key=key, error=str(e))
            return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set value in cache"""
        if not self.redis:
            await self.initialize()
        
        try:
            # Serialize value
            if isinstance(value, (dict, list, str, int, float, bool)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = pickle.dumps(value)
            
            # Set TTL
            if ttl is None:
                ttl = settings.REDIS_CACHE_TTL
            
            await self.redis.set(key, serialized_value, ex=ttl)
            return True
            
        except Exception as e:
            logger.error("Cache set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self.redis:
            await self.initialize()
        
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error("Cache delete failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self.redis:
            await self.initialize()
        
        try:
            result = await self.redis.exists(key)
            return result > 0
        except Exception as e:
            logger.error("Cache exists check failed", key=key, error=str(e))
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter in cache"""
        if not self.redis:
            await self.initialize()
        
        try:
            return await self.redis.incrby(key, amount)
        except Exception as e:
            logger.error("Cache increment failed", key=key, error=str(e))
            return None
    
    async def expire(self, key: str, ttl: Union[int, timedelta]) -> bool:
        """Set expiration for key"""
        if not self.redis:
            await self.initialize()
        
        try:
            return await self.redis.expire(key, ttl)
        except Exception as e:
            logger.error("Cache expire failed", key=key, error=str(e))
            return False


# Global cache service instance
cache_service = CacheService()


# Convenience functions
async def cache_get(key: str, default: Any = None) -> Any:
    """Get value from cache"""
    return await cache_service.get(key, default)


async def cache_set(
    key: str,
    value: Any,
    ttl: Optional[Union[int, timedelta]] = None
) -> bool:
    """Set value in cache"""
    return await cache_service.set(key, value, ttl)


async def cache_delete(key: str) -> bool:
    """Delete key from cache"""
    return await cache_service.delete(key)


async def cache_exists(key: str) -> bool:
    """Check if key exists in cache"""
    return await cache_service.exists(key)
