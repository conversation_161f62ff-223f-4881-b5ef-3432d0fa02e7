"""
Application configuration settings
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """Application settings"""

    # Application
    APP_NAME: str = "FraudShield"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")

    # API Configuration
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = Field(env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Security
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    CORS_ORIGINS: List[str] = Field(default=["*"], env="CORS_ORIGINS")

    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=10, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")

    # Redis Cache
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_CACHE_TTL: int = Field(default=3600, env="REDIS_CACHE_TTL")

    # Message Queue (Kafka/RabbitMQ)
    KAFKA_BOOTSTRAP_SERVERS: str = Field(default="localhost:9092", env="KAFKA_BOOTSTRAP_SERVERS")
    KAFKA_TOPIC_TRANSACTIONS: str = Field(default="transactions", env="KAFKA_TOPIC_TRANSACTIONS")
    KAFKA_TOPIC_ALERTS: str = Field(default="fraud_alerts", env="KAFKA_TOPIC_ALERTS")

    # ML Service
    ML_SERVICE_URL: str = Field(default="http://localhost:8001", env="ML_SERVICE_URL")
    ML_SERVICE_TIMEOUT: int = Field(default=30, env="ML_SERVICE_TIMEOUT")

    # Feature Store
    FEATURE_STORE_URL: str = Field(default="http://localhost:8002", env="FEATURE_STORE_URL")

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")

    # Fraud Detection Thresholds
    FRAUD_SCORE_THRESHOLD_HIGH: float = Field(default=0.8, env="FRAUD_SCORE_THRESHOLD_HIGH")
    FRAUD_SCORE_THRESHOLD_MEDIUM: float = Field(default=0.5, env="FRAUD_SCORE_THRESHOLD_MEDIUM")

    # Notifications
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SLACK_WEBHOOK_URL: Optional[str] = Field(default=None, env="SLACK_WEBHOOK_URL")

    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9090, env="METRICS_PORT")

    # Redis
    REDIS_URL: str = Field(env="REDIS_URL")
    REDIS_CACHE_TTL: int = Field(default=3600, env="REDIS_CACHE_TTL")  # 1 hour

    # Kafka
    KAFKA_BOOTSTRAP_SERVERS: str = Field(env="KAFKA_BOOTSTRAP_SERVERS")
    KAFKA_TOPIC_TRANSACTIONS: str = Field(default="transactions", env="KAFKA_TOPIC_TRANSACTIONS")
    KAFKA_TOPIC_FRAUD_ALERTS: str = Field(default="fraud-alerts", env="KAFKA_TOPIC_FRAUD_ALERTS")

    # ML Service
    ML_SERVICE_URL: str = Field(env="ML_SERVICE_URL")
    ML_MODEL_CACHE_TTL: int = Field(default=86400, env="ML_MODEL_CACHE_TTL")  # 24 hours
    ML_PREDICTION_TIMEOUT: int = Field(default=5, env="ML_PREDICTION_TIMEOUT")  # 5 seconds

    # Feature Store
    FEATURE_STORE_URL: Optional[str] = Field(default=None, env="FEATURE_STORE_URL")

    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9090, env="METRICS_PORT")

    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")  # json or text

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds

    # Fraud Detection Thresholds
    FRAUD_THRESHOLD_LOW: float = Field(default=0.3, env="FRAUD_THRESHOLD_LOW")
    FRAUD_THRESHOLD_HIGH: float = Field(default=0.7, env="FRAUD_THRESHOLD_HIGH")

    # Transaction Limits
    MAX_TRANSACTION_AMOUNT: float = Field(default=1000000.0, env="MAX_TRANSACTION_AMOUNT")
    MIN_TRANSACTION_AMOUNT: float = Field(default=0.01, env="MIN_TRANSACTION_AMOUNT")

    # Email Configuration (for alerts)
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_USE_TLS: bool = Field(default=True, env="SMTP_USE_TLS")

    # Alert Configuration
    ALERT_EMAIL_FROM: Optional[str] = Field(default=None, env="ALERT_EMAIL_FROM")
    ALERT_EMAIL_TO: List[str] = Field(default=[], env="ALERT_EMAIL_TO")

    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v

    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @validator("ALERT_EMAIL_TO", pre=True)
    def parse_alert_emails(cls, v):
        if isinstance(v, str):
            return [email.strip() for email in v.split(",")]
        return v

    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        return self.ENVIRONMENT.lower() == "development"

    @property
    def is_testing(self) -> bool:
        return self.ENVIRONMENT.lower() == "testing"

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# Environment-specific configurations
class DevelopmentSettings(Settings):
    """Development environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"


class ProductionSettings(Settings):
    """Production environment settings"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    ALLOWED_HOSTS: List[str] = ["api.fraudshield.com", "fraudshield.com"]


class TestingSettings(Settings):
    """Testing environment settings"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    DATABASE_URL: str = "sqlite:///./test.db"
    REDIS_URL: str = "redis://localhost:6379/1"


def get_settings() -> Settings:
    """Get settings based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()

    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()
