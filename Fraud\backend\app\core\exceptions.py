"""
Custom exceptions for FraudShield application
"""

from typing import Any, Dict, Optional


class FraudShieldException(Exception):
    """Base exception for FraudShield application"""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(FraudShieldException):
    """Exception for validation errors"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=400,
            details=details
        )


class AuthenticationException(FraudShieldException):
    """Exception for authentication errors"""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401
        )


class AuthorizationException(FraudShieldException):
    """Exception for authorization errors"""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403
        )


class TransactionNotFoundException(FraudShieldException):
    """Exception for transaction not found"""
    
    def __init__(self, transaction_id: str):
        super().__init__(
            message=f"Transaction {transaction_id} not found",
            error_code="TRANSACTION_NOT_FOUND",
            status_code=404,
            details={"transaction_id": transaction_id}
        )


class MLServiceException(FraudShieldException):
    """Exception for ML service errors"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="ML_SERVICE_ERROR",
            status_code=503,
            details=details
        )


class FeatureStoreException(FraudShieldException):
    """Exception for feature store errors"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="FEATURE_STORE_ERROR",
            status_code=503,
            details=details
        )


class RateLimitException(FraudShieldException):
    """Exception for rate limiting"""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429
        )


class ProcessingException(FraudShieldException):
    """Exception for transaction processing errors"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="PROCESSING_ERROR",
            status_code=500,
            details=details
        )
