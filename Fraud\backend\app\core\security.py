"""
Security utilities for FraudShield
"""

import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

import bcrypt
import jwt
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import Crypt<PERSON>ontext

from .config import settings
from .exceptions import AuthenticationException, AuthorizationException

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Security
security = HTTPBearer()


def create_access_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None,
    additional_claims: Optional[Dict[str, Any]] = None
) -> str:
    """Create JWT access token"""
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": "access"
    }
    
    if additional_claims:
        to_encode.update(additional_claims)
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm="HS256"
    )
    return encoded_jwt


def create_refresh_token(subject: Union[str, Any]) -> str:
    """Create JWT refresh token"""
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": "refresh"
    }
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm="HS256"
    )
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise AuthenticationException("Token has expired")
    except jwt.JWTError:
        raise AuthenticationException("Invalid token")


def get_password_hash(password: str) -> str:
    """Hash password using bcrypt"""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)


def generate_api_key() -> str:
    """Generate secure API key"""
    return secrets.token_urlsafe(32)


class RateLimiter:
    """Simple in-memory rate limiter"""
    
    def __init__(self):
        self.requests = {}
    
    async def is_allowed(
        self,
        identifier: str,
        max_requests: int = None,
        window_seconds: int = None
    ) -> bool:
        """Check if request is allowed based on rate limits"""
        if max_requests is None:
            max_requests = settings.RATE_LIMIT_REQUESTS
        if window_seconds is None:
            window_seconds = settings.RATE_LIMIT_WINDOW
        
        now = datetime.utcnow()
        window_start = now - timedelta(seconds=window_seconds)
        
        # Clean old entries
        if identifier in self.requests:
            self.requests[identifier] = [
                req_time for req_time in self.requests[identifier]
                if req_time > window_start
            ]
        else:
            self.requests[identifier] = []
        
        # Check if limit exceeded
        if len(self.requests[identifier]) >= max_requests:
            return False
        
        # Add current request
        self.requests[identifier].append(now)
        return True


# Global rate limiter instance
rate_limiter = RateLimiter()


class SecurityHeaders:
    """Security headers middleware"""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """Get security headers"""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }


def validate_api_key(api_key: str) -> bool:
    """Validate API key (implement your logic here)"""
    # This is a simple example - implement proper API key validation
    # You might want to store API keys in database with associated permissions
    return len(api_key) >= 32


def check_permissions(user_role: str, required_permission: str) -> bool:
    """Check if user has required permission"""
    # Define role-based permissions
    permissions = {
        "admin": [
            "read:transactions",
            "write:transactions",
            "read:users",
            "write:users",
            "read:alerts",
            "write:alerts",
            "read:analytics",
            "manage:system"
        ],
        "analyst": [
            "read:transactions",
            "read:alerts",
            "write:alerts",
            "read:analytics"
        ],
        "operator": [
            "read:transactions",
            "read:alerts",
            "write:alerts"
        ],
        "user": [
            "read:transactions"
        ]
    }
    
    user_permissions = permissions.get(user_role, [])
    return required_permission in user_permissions


def mask_sensitive_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Mask sensitive data in logs/responses"""
    sensitive_fields = [
        "password",
        "secret",
        "token",
        "api_key",
        "account_number",
        "ssn",
        "credit_card"
    ]
    
    masked_data = data.copy()
    
    for field in sensitive_fields:
        if field in masked_data:
            if isinstance(masked_data[field], str) and len(masked_data[field]) > 4:
                masked_data[field] = "*" * (len(masked_data[field]) - 4) + masked_data[field][-4:]
            else:
                masked_data[field] = "***"
    
    return masked_data
