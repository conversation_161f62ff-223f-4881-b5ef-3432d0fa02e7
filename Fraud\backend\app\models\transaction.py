"""
Transaction data models
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, Numeric, DateTime, Boolean, Text, Integer, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func

from app.core.database import Base


class TransactionType(str, Enum):
    """Transaction type enumeration"""
    PAYMENT = "PAYMENT"
    TRANSFER = "TRANSFER"
    CASH_OUT = "CASH_OUT"
    DEBIT = "DEBIT"
    CASH_IN = "CASH_IN"


class TransactionStatus(str, Enum):
    """Transaction status enumeration"""
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    FLAGGED = "FLAGGED"


class FraudDecision(str, Enum):
    """Fraud detection decision enumeration"""
    ALLOW = "ALLOW"
    REVIEW = "REVIEW"
    BLOCK = "BLOCK"


class Transaction(Base):
    """Transaction model"""
    __tablename__ = "transactions"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Transaction identifiers
    transaction_id = Column(String(100), unique=True, nullable=False, index=True)
    external_id = Column(String(100), nullable=True, index=True)
    
    # Transaction details
    type = Column(String(20), nullable=False, index=True)
    amount = Column(Numeric(15, 2), nullable=False, index=True)
    currency = Column(String(3), nullable=False, default="USD")
    
    # Account information
    originator_account_id = Column(String(50), nullable=False, index=True)
    originator_balance_before = Column(Numeric(15, 2), nullable=False)
    originator_balance_after = Column(Numeric(15, 2), nullable=False)
    
    beneficiary_account_id = Column(String(50), nullable=False, index=True)
    beneficiary_balance_before = Column(Numeric(15, 2), nullable=False)
    beneficiary_balance_after = Column(Numeric(15, 2), nullable=False)
    
    # Fraud detection results
    fraud_score = Column(Numeric(5, 4), nullable=True, index=True)  # 0.0000 to 1.0000
    is_fraudulent = Column(Boolean, nullable=True, index=True)
    fraud_decision = Column(String(10), nullable=True, index=True)
    fraud_reason_codes = Column(JSONB, nullable=True)
    
    # Status and processing
    status = Column(String(20), nullable=False, default=TransactionStatus.PENDING, index=True)
    processing_time_ms = Column(Integer, nullable=True)
    
    # Metadata
    metadata = Column(JSONB, nullable=True)
    device_id = Column(String(100), nullable=True, index=True)
    ip_address = Column(String(45), nullable=True, index=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    location_data = Column(JSONB, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    processed_at = Column(DateTime(timezone=True), nullable=True, index=True)
    
    # Audit fields
    created_by = Column(String(100), nullable=True)
    updated_by = Column(String(100), nullable=True)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_transaction_created_at_desc', created_at.desc()),
        Index('idx_transaction_amount_type', amount, type),
        Index('idx_transaction_fraud_score_desc', fraud_score.desc()),
        Index('idx_transaction_originator_created', originator_account_id, created_at.desc()),
        Index('idx_transaction_beneficiary_created', beneficiary_account_id, created_at.desc()),
        Index('idx_transaction_status_created', status, created_at.desc()),
    )
    
    def __repr__(self):
        return f"<Transaction(id={self.id}, transaction_id={self.transaction_id}, type={self.type}, amount={self.amount})>"
    
    @property
    def is_high_risk(self) -> bool:
        """Check if transaction is high risk"""
        return self.fraud_score is not None and self.fraud_score >= 0.7
    
    @property
    def is_low_risk(self) -> bool:
        """Check if transaction is low risk"""
        return self.fraud_score is not None and self.fraud_score < 0.3
    
    @property
    def risk_level(self) -> str:
        """Get risk level as string"""
        if self.fraud_score is None:
            return "UNKNOWN"
        elif self.fraud_score >= 0.7:
            return "HIGH"
        elif self.fraud_score >= 0.3:
            return "MEDIUM"
        else:
            return "LOW"
    
    def to_dict(self) -> dict:
        """Convert transaction to dictionary"""
        return {
            "id": str(self.id),
            "transaction_id": self.transaction_id,
            "external_id": self.external_id,
            "type": self.type,
            "amount": float(self.amount) if self.amount else None,
            "currency": self.currency,
            "originator_account_id": self.originator_account_id,
            "originator_balance_before": float(self.originator_balance_before) if self.originator_balance_before else None,
            "originator_balance_after": float(self.originator_balance_after) if self.originator_balance_after else None,
            "beneficiary_account_id": self.beneficiary_account_id,
            "beneficiary_balance_before": float(self.beneficiary_balance_before) if self.beneficiary_balance_before else None,
            "beneficiary_balance_after": float(self.beneficiary_balance_after) if self.beneficiary_balance_after else None,
            "fraud_score": float(self.fraud_score) if self.fraud_score else None,
            "is_fraudulent": self.is_fraudulent,
            "fraud_decision": self.fraud_decision,
            "fraud_reason_codes": self.fraud_reason_codes,
            "status": self.status,
            "processing_time_ms": self.processing_time_ms,
            "metadata": self.metadata,
            "device_id": self.device_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "location_data": self.location_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "created_by": self.created_by,
            "updated_by": self.updated_by,
            "risk_level": self.risk_level
        }


class TransactionFeature(Base):
    """Transaction features for ML model"""
    __tablename__ = "transaction_features"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    
    # Foreign key to transaction
    transaction_id = Column(String(100), nullable=False, index=True)
    
    # Feature values
    features = Column(JSONB, nullable=False)
    feature_version = Column(String(20), nullable=False, default="v1")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<TransactionFeature(transaction_id={self.transaction_id}, feature_version={self.feature_version})>"
