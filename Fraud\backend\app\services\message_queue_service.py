"""
Message Queue Service for real-time transaction processing
"""

import asyncio
import json
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class MessageQueueService:
    """Service for handling message queue operations"""
    
    def __init__(self):
        self.kafka_servers = settings.KAFKA_BOOTSTRAP_SERVERS
        self.transaction_topic = settings.KAFKA_TOPIC_TRANSACTIONS
        self.alert_topic = settings.KAFKA_TOPIC_ALERTS
        self.producer = None
        self.consumer = None
    
    async def initialize(self):
        """Initialize message queue connections"""
        try:
            # Try to import aiokafka
            try:
                from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
                
                # Initialize producer
                self.producer = AIOKafkaProducer(
                    bootstrap_servers=self.kafka_servers,
                    value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                    key_serializer=lambda k: k.encode('utf-8') if k else None
                )
                await self.producer.start()
                
                logger.info("Kafka producer initialized successfully")
                
            except ImportError:
                logger.warning("aiokafka not available, using mock message queue")
                self.producer = MockProducer()
                
        except Exception as e:
            logger.error("Failed to initialize message queue", error=str(e))
            # Use mock producer as fallback
            self.producer = MockProducer()
    
    async def close(self):
        """Close message queue connections"""
        try:
            if self.producer and hasattr(self.producer, 'stop'):
                await self.producer.stop()
            
            if self.consumer and hasattr(self.consumer, 'stop'):
                await self.consumer.stop()
                
            logger.info("Message queue connections closed")
            
        except Exception as e:
            logger.error("Error closing message queue connections", error=str(e))
    
    async def publish_transaction(self, transaction_data: Dict[str, Any]) -> bool:
        """
        Publish transaction to message queue for async processing
        
        Args:
            transaction_data: Transaction data to publish
            
        Returns:
            True if successfully published
        """
        try:
            if not self.producer:
                await self.initialize()
            
            # Add timestamp
            message = {
                **transaction_data,
                "published_at": datetime.utcnow().isoformat(),
                "message_type": "transaction_processed"
            }
            
            # Use transaction_id as key for partitioning
            key = transaction_data.get("transaction_id")
            
            await self.producer.send(
                self.transaction_topic,
                value=message,
                key=key
            )
            
            logger.debug(
                "Transaction published to queue",
                transaction_id=transaction_data.get("transaction_id"),
                topic=self.transaction_topic
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to publish transaction",
                transaction_id=transaction_data.get("transaction_id"),
                error=str(e)
            )
            return False
    
    async def publish_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Publish fraud alert to message queue
        
        Args:
            alert_data: Alert data to publish
            
        Returns:
            True if successfully published
        """
        try:
            if not self.producer:
                await self.initialize()
            
            # Add timestamp
            message = {
                **alert_data,
                "published_at": datetime.utcnow().isoformat(),
                "message_type": "fraud_alert"
            }
            
            # Use alert_id or transaction_id as key
            key = alert_data.get("alert_id") or alert_data.get("transaction_id")
            
            await self.producer.send(
                self.alert_topic,
                value=message,
                key=key
            )
            
            logger.info(
                "Alert published to queue",
                alert_id=alert_data.get("alert_id"),
                transaction_id=alert_data.get("transaction_id"),
                severity=alert_data.get("severity"),
                topic=self.alert_topic
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to publish alert",
                alert_id=alert_data.get("alert_id"),
                error=str(e)
            )
            return False
    
    async def consume_transactions(
        self,
        handler: Callable[[Dict[str, Any]], None],
        group_id: str = "fraud_detection_backend"
    ):
        """
        Consume transactions from message queue
        
        Args:
            handler: Function to handle received messages
            group_id: Consumer group ID
        """
        try:
            from aiokafka import AIOKafkaConsumer
            
            consumer = AIOKafkaConsumer(
                self.transaction_topic,
                bootstrap_servers=self.kafka_servers,
                group_id=group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                auto_offset_reset='latest'
            )
            
            await consumer.start()
            self.consumer = consumer
            
            logger.info(
                "Started consuming transactions",
                topic=self.transaction_topic,
                group_id=group_id
            )
            
            try:
                async for message in consumer:
                    try:
                        await handler(message.value)
                    except Exception as e:
                        logger.error(
                            "Error handling transaction message",
                            error=str(e),
                            message_key=message.key
                        )
                        
            finally:
                await consumer.stop()
                
        except ImportError:
            logger.warning("aiokafka not available, skipping message consumption")
        except Exception as e:
            logger.error("Error consuming transactions", error=str(e))
    
    async def consume_alerts(
        self,
        handler: Callable[[Dict[str, Any]], None],
        group_id: str = "alert_processor"
    ):
        """
        Consume alerts from message queue
        
        Args:
            handler: Function to handle received messages
            group_id: Consumer group ID
        """
        try:
            from aiokafka import AIOKafkaConsumer
            
            consumer = AIOKafkaConsumer(
                self.alert_topic,
                bootstrap_servers=self.kafka_servers,
                group_id=group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                auto_offset_reset='latest'
            )
            
            await consumer.start()
            
            logger.info(
                "Started consuming alerts",
                topic=self.alert_topic,
                group_id=group_id
            )
            
            try:
                async for message in consumer:
                    try:
                        await handler(message.value)
                    except Exception as e:
                        logger.error(
                            "Error handling alert message",
                            error=str(e),
                            message_key=message.key
                        )
                        
            finally:
                await consumer.stop()
                
        except ImportError:
            logger.warning("aiokafka not available, skipping alert consumption")
        except Exception as e:
            logger.error("Error consuming alerts", error=str(e))


class MockProducer:
    """Mock producer for development/testing when Kafka is not available"""
    
    async def send(self, topic: str, value: Dict[str, Any], key: str = None):
        """Mock send method"""
        logger.debug(
            "Mock message sent",
            topic=topic,
            key=key,
            message_type=value.get("message_type")
        )
    
    async def stop(self):
        """Mock stop method"""
        pass


# Global message queue service instance
message_queue_service = MessageQueueService()
