"""
ML Service integration for fraud detection
"""

import asyncio
import aiohttp
from typing import Dict, Any, Optional
import json

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import MLServiceException
from app.core.cache import cache_service

logger = get_logger(__name__)


class MLService:
    """Service for integrating with ML fraud detection models"""
    
    def __init__(self):
        self.base_url = settings.ML_SERVICE_URL
        self.timeout = settings.ML_SERVICE_TIMEOUT
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self):
        """Initialize ML service connection"""
        try:
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test connection
            await self.health_check()
            logger.info("ML service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize ML service", error=str(e))
            raise MLServiceException(f"ML service initialization failed: {str(e)}")
    
    async def close(self):
        """Close ML service connection"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def health_check(self) -> bool:
        """Check ML service health"""
        try:
            if not self.session:
                await self.initialize()
            
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    return True
                else:
                    raise MLServiceException(f"ML service health check failed: {response.status}")
                    
        except Exception as e:
            logger.error("ML service health check failed", error=str(e))
            raise MLServiceException(f"ML service health check failed: {str(e)}")
    
    async def predict_fraud(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Predict fraud for a transaction
        
        Args:
            transaction_data: Transaction data for prediction
            
        Returns:
            Dict containing fraud_score, is_fraudulent, risk_level, etc.
        """
        try:
            # Check cache first
            cache_key = f"fraud_prediction:{self._generate_cache_key(transaction_data)}"
            cached_result = await cache_service.get(cache_key)
            
            if cached_result:
                logger.debug("Using cached fraud prediction", transaction_id=transaction_data.get("transaction_id"))
                return cached_result
            
            if not self.session:
                await self.initialize()
            
            # Prepare request payload
            payload = {
                "transaction": transaction_data,
                "include_explanations": True,
                "model_version": "latest"
            }
            
            # Make prediction request
            async with self.session.post(
                f"{self.base_url}/predict",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Cache result for a short time
                    await cache_service.set(cache_key, result, ttl=300)  # 5 minutes
                    
                    logger.info(
                        "Fraud prediction completed",
                        transaction_id=transaction_data.get("transaction_id"),
                        fraud_score=result.get("fraud_score"),
                        is_fraudulent=result.get("is_fraudulent")
                    )
                    
                    return result
                    
                else:
                    error_text = await response.text()
                    raise MLServiceException(f"ML prediction failed: {response.status} - {error_text}")
                    
        except MLServiceException:
            raise
        except Exception as e:
            logger.error(
                "ML prediction failed",
                transaction_id=transaction_data.get("transaction_id"),
                error=str(e)
            )
            raise MLServiceException(f"ML prediction failed: {str(e)}")
    
    async def predict_batch(self, transactions: list[Dict[str, Any]]) -> list[Dict[str, Any]]:
        """
        Predict fraud for multiple transactions
        
        Args:
            transactions: List of transaction data
            
        Returns:
            List of prediction results
        """
        try:
            if not self.session:
                await self.initialize()
            
            payload = {
                "transactions": transactions,
                "include_explanations": True,
                "model_version": "latest"
            }
            
            async with self.session.post(
                f"{self.base_url}/predict/batch",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    results = await response.json()
                    
                    logger.info(
                        "Batch fraud prediction completed",
                        batch_size=len(transactions),
                        results_count=len(results.get("predictions", []))
                    )
                    
                    return results.get("predictions", [])
                    
                else:
                    error_text = await response.text()
                    raise MLServiceException(f"Batch ML prediction failed: {response.status} - {error_text}")
                    
        except MLServiceException:
            raise
        except Exception as e:
            logger.error(
                "Batch ML prediction failed",
                batch_size=len(transactions),
                error=str(e)
            )
            raise MLServiceException(f"Batch ML prediction failed: {str(e)}")
    
    async def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current ML model
        
        Returns:
            Dict containing model version, performance metrics, etc.
        """
        try:
            if not self.session:
                await self.initialize()
            
            async with self.session.get(f"{self.base_url}/model/info") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise MLServiceException(f"Failed to get model info: {response.status} - {error_text}")
                    
        except MLServiceException:
            raise
        except Exception as e:
            logger.error("Failed to get model info", error=str(e))
            raise MLServiceException(f"Failed to get model info: {str(e)}")
    
    async def get_feature_importance(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get feature importance for a specific transaction
        
        Args:
            transaction_data: Transaction data
            
        Returns:
            Dict containing feature importance scores
        """
        try:
            if not self.session:
                await self.initialize()
            
            payload = {"transaction": transaction_data}
            
            async with self.session.post(
                f"{self.base_url}/explain",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise MLServiceException(f"Failed to get feature importance: {response.status} - {error_text}")
                    
        except MLServiceException:
            raise
        except Exception as e:
            logger.error(
                "Failed to get feature importance",
                transaction_id=transaction_data.get("transaction_id"),
                error=str(e)
            )
            raise MLServiceException(f"Failed to get feature importance: {str(e)}")
    
    def _generate_cache_key(self, transaction_data: Dict[str, Any]) -> str:
        """
        Generate cache key for transaction data
        
        Args:
            transaction_data: Transaction data
            
        Returns:
            Cache key string
        """
        # Create a deterministic key based on transaction features
        key_data = {
            "type": transaction_data.get("type"),
            "amount": transaction_data.get("amount"),
            "originator": transaction_data.get("originator_account_id"),
            "beneficiary": transaction_data.get("beneficiary_account_id"),
            "currency": transaction_data.get("currency")
        }
        
        # Sort keys for consistency
        key_string = json.dumps(key_data, sort_keys=True)
        
        # Use hash for shorter key
        import hashlib
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def update_model_feedback(
        self,
        transaction_id: str,
        actual_fraud: bool,
        predicted_score: float
    ) -> bool:
        """
        Send feedback to ML service for model improvement
        
        Args:
            transaction_id: Transaction identifier
            actual_fraud: Whether transaction was actually fraudulent
            predicted_score: The score that was predicted
            
        Returns:
            True if feedback was successfully sent
        """
        try:
            if not self.session:
                await self.initialize()
            
            payload = {
                "transaction_id": transaction_id,
                "actual_fraud": actual_fraud,
                "predicted_score": predicted_score,
                "feedback_type": "ground_truth"
            }
            
            async with self.session.post(
                f"{self.base_url}/feedback",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    logger.info(
                        "Model feedback sent",
                        transaction_id=transaction_id,
                        actual_fraud=actual_fraud,
                        predicted_score=predicted_score
                    )
                    return True
                else:
                    logger.warning(
                        "Failed to send model feedback",
                        transaction_id=transaction_id,
                        status=response.status
                    )
                    return False
                    
        except Exception as e:
            logger.error(
                "Failed to send model feedback",
                transaction_id=transaction_id,
                error=str(e)
            )
            return False
