"""
Transaction processing service
"""

import asyncio
import time
from typing import List, Optional, Dict, Any
from uuid import uuid4
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.core.logging import get_logger, log_transaction_event
from app.core.exceptions import ProcessingException, MLServiceException
from app.models.transaction import Transaction, TransactionStatus, FraudDecision
from app.schemas.transaction import (
    TransactionRequest,
    TransactionResponse,
    TransactionStats,
    FraudResult
)
from app.services.ml_service import MLService
from app.services.message_queue_service import MessageQueueService

logger = get_logger(__name__)


class TransactionService:
    """Service for processing transactions and fraud detection"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.ml_service = MLService()
        self.message_queue = MessageQueueService()

    async def process_transaction(self, request: TransactionRequest) -> TransactionResponse:
        """
        Process a single transaction for fraud detection
        """
        start_time = time.time()
        transaction_id = request.transaction_id or str(uuid4())

        try:
            # Create transaction record
            transaction = Transaction(
                transaction_id=transaction_id,
                external_id=request.transaction_id,
                type=request.type.value,
                amount=Decimal(str(request.amount)),
                currency=request.currency,
                originator_account_id=request.originator.account_id,
                originator_balance_before=Decimal(str(request.originator.current_balance)),
                originator_balance_after=Decimal(str(request.originator.current_balance - request.amount)),
                beneficiary_account_id=request.beneficiary.account_id,
                beneficiary_balance_before=Decimal(str(request.beneficiary.current_balance)),
                beneficiary_balance_after=Decimal(str(request.beneficiary.current_balance + request.amount)),
                status=TransactionStatus.PENDING.value,
                metadata=request.metadata.dict() if request.metadata else {}
            )

            self.db.add(transaction)
            await self.db.flush()  # Get the ID without committing

            log_transaction_event(
                logger,
                "Transaction created",
                transaction_id,
                amount=float(request.amount),
                type=request.type.value
            )

            # Perform fraud detection
            fraud_result = await self._perform_fraud_detection(request, transaction)

            # Update transaction with fraud results
            transaction.fraud_score = fraud_result.fraud_score
            transaction.risk_level = fraud_result.risk_level
            transaction.decision = fraud_result.decision
            transaction.fraud_reasons = fraud_result.reasons
            transaction.model_version = fraud_result.model_version

            # Determine final status based on decision
            if fraud_result.decision == FraudDecision.ALLOW:
                transaction.status = TransactionStatus.APPROVED.value
            elif fraud_result.decision == FraudDecision.BLOCK:
                transaction.status = TransactionStatus.BLOCKED.value
            else:  # REVIEW
                transaction.status = TransactionStatus.UNDER_REVIEW.value

            transaction.processed_at = func.now()

            # Commit transaction
            await self.db.commit()

            # Send to message queue for further processing
            await self._send_to_message_queue(transaction, fraud_result)

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)

            log_transaction_event(
                logger,
                "Transaction processed",
                transaction_id,
                fraud_score=fraud_result.fraud_score,
                decision=fraud_result.decision.value,
                processing_time_ms=processing_time_ms
            )

            # Create response
            return TransactionResponse(
                id=transaction.id,
                transaction_id=transaction.transaction_id,
                type=request.type,
                amount=request.amount,
                currency=request.currency,
                originator=request.originator,
                beneficiary=request.beneficiary,
                status=TransactionStatus(transaction.status),
                fraud_result=fraud_result,
                processing_time_ms=processing_time_ms,
                created_at=transaction.created_at,
                processed_at=transaction.processed_at,
                metadata=request.metadata
            )

        except Exception as e:
            await self.db.rollback()
            logger.error(
                "Transaction processing failed",
                transaction_id=transaction_id,
                error=str(e)
            )
            raise ProcessingException(f"Failed to process transaction: {str(e)}")

    async def _perform_fraud_detection(
        self,
        request: TransactionRequest,
        transaction: Transaction
    ) -> FraudResult:
        """
        Perform fraud detection using ML service
        """
        try:
            # Prepare data for ML service
            ml_request = {
                "transaction_id": transaction.transaction_id,
                "type": request.type.value,
                "amount": float(request.amount),
                "currency": request.currency,
                "originator_account_id": request.originator.account_id,
                "originator_balance": float(request.originator.current_balance),
                "beneficiary_account_id": request.beneficiary.account_id,
                "beneficiary_balance": float(request.beneficiary.current_balance),
                "metadata": request.metadata.dict() if request.metadata else {}
            }

            # Call ML service
            ml_response = await self.ml_service.predict_fraud(ml_request)

            # Determine decision based on score and thresholds
            fraud_score = ml_response["fraud_score"]

            if fraud_score >= settings.FRAUD_SCORE_THRESHOLD_HIGH:
                decision = FraudDecision.BLOCK
                risk_level = "HIGH"
            elif fraud_score >= settings.FRAUD_SCORE_THRESHOLD_MEDIUM:
                decision = FraudDecision.REVIEW
                risk_level = "MEDIUM"
            else:
                decision = FraudDecision.ALLOW
                risk_level = "LOW"

            return FraudResult(
                fraud_score=fraud_score,
                risk_level=risk_level,
                decision=decision,
                reasons=ml_response.get("reasons", []),
                model_version=ml_response.get("model_version", "unknown"),
                confidence=ml_response.get("confidence", 0.0)
            )

        except Exception as e:
            logger.error(
                "Fraud detection failed",
                transaction_id=transaction.transaction_id,
                error=str(e)
            )
            # Return safe default in case of ML service failure
            return FraudResult(
                fraud_score=0.5,
                risk_level="MEDIUM",
                decision=FraudDecision.REVIEW,
                reasons=["ML service unavailable"],
                model_version="fallback",
                confidence=0.0
            )

    async def _send_to_message_queue(
        self,
        transaction: Transaction,
        fraud_result: FraudResult
    ):
        """
        Send transaction to message queue for async processing
        """
        try:
            message = {
                "transaction_id": transaction.transaction_id,
                "type": transaction.type,
                "amount": float(transaction.amount),
                "fraud_score": fraud_result.fraud_score,
                "decision": fraud_result.decision.value,
                "risk_level": fraud_result.risk_level,
                "timestamp": transaction.created_at.isoformat()
            }

            await self.message_queue.publish_transaction(message)

        except Exception as e:
            logger.warning(
                "Failed to send transaction to message queue",
                transaction_id=transaction.transaction_id,
                error=str(e)
            )
            # Don't fail the transaction if message queue is down

    async def process_transaction_batch(
        self,
        requests: List[TransactionRequest]
    ) -> List[TransactionResponse]:
        """
        Process multiple transactions in batch
        """
        results = []

        # Process transactions concurrently (with limit)
        semaphore = asyncio.Semaphore(10)  # Limit concurrent processing

        async def process_single(request):
            async with semaphore:
                return await self.process_transaction(request)

        tasks = [process_single(request) for request in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(
                    "Batch transaction failed",
                    index=i,
                    error=str(result)
                )
                # Create error response
                request = requests[i]
                error_response = TransactionResponse(
                    id=uuid4(),
                    transaction_id=request.transaction_id or str(uuid4()),
                    type=request.type,
                    amount=request.amount,
                    currency=request.currency,
                    originator=request.originator,
                    beneficiary=request.beneficiary,
                    status=TransactionStatus.FAILED,
                    fraud_result=None,
                    processing_time_ms=0,
                    created_at=func.now(),
                    processed_at=None,
                    metadata=request.metadata
                )
                processed_results.append(error_response)
            else:
                processed_results.append(result)

        return processed_results

    async def get_transaction(self, transaction_id: str) -> Optional[TransactionResponse]:
        """
        Get transaction by ID
        """
        try:
            query = select(Transaction).where(
                Transaction.transaction_id == transaction_id
            )
            result = await self.db.execute(query)
            transaction = result.scalar_one_or_none()

            if not transaction:
                return None

            # Convert to response format
            fraud_result = None
            if transaction.fraud_score is not None:
                fraud_result = FraudResult(
                    fraud_score=float(transaction.fraud_score),
                    risk_level=transaction.risk_level,
                    decision=FraudDecision(transaction.decision),
                    reasons=transaction.fraud_reasons or [],
                    model_version=transaction.model_version or "unknown",
                    confidence=0.0  # Not stored in current schema
                )

            return TransactionResponse(
                id=transaction.id,
                transaction_id=transaction.transaction_id,
                type=transaction.type,
                amount=float(transaction.amount),
                currency=transaction.currency,
                originator={"account_id": transaction.originator_account_id, "current_balance": float(transaction.originator_balance_before)},
                beneficiary={"account_id": transaction.beneficiary_account_id, "current_balance": float(transaction.beneficiary_balance_before)},
                status=TransactionStatus(transaction.status),
                fraud_result=fraud_result,
                processing_time_ms=None,  # Not stored
                created_at=transaction.created_at,
                processed_at=transaction.processed_at,
                metadata=transaction.metadata
            )

        except Exception as e:
            logger.error(
                "Failed to get transaction",
                transaction_id=transaction_id,
                error=str(e)
            )
            return None

    async def list_transactions(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        fraud_only: bool = False
    ) -> List[TransactionResponse]:
        """
        List transactions with optional filtering
        """
        try:
            query = select(Transaction)

            # Apply filters
            if status:
                query = query.where(Transaction.status == status)

            if fraud_only:
                query = query.where(Transaction.decision == FraudDecision.BLOCK.value)

            # Apply pagination and ordering
            query = query.order_by(desc(Transaction.created_at)).offset(skip).limit(limit)

            result = await self.db.execute(query)
            transactions = result.scalars().all()

            # Convert to response format
            responses = []
            for transaction in transactions:
                fraud_result = None
                if transaction.fraud_score is not None:
                    fraud_result = FraudResult(
                        fraud_score=float(transaction.fraud_score),
                        risk_level=transaction.risk_level,
                        decision=FraudDecision(transaction.decision),
                        reasons=transaction.fraud_reasons or [],
                        model_version=transaction.model_version or "unknown",
                        confidence=0.0
                    )

                response = TransactionResponse(
                    id=transaction.id,
                    transaction_id=transaction.transaction_id,
                    type=transaction.type,
                    amount=float(transaction.amount),
                    currency=transaction.currency,
                    originator={"account_id": transaction.originator_account_id, "current_balance": float(transaction.originator_balance_before)},
                    beneficiary={"account_id": transaction.beneficiary_account_id, "current_balance": float(transaction.beneficiary_balance_before)},
                    status=TransactionStatus(transaction.status),
                    fraud_result=fraud_result,
                    processing_time_ms=None,
                    created_at=transaction.created_at,
                    processed_at=transaction.processed_at,
                    metadata=transaction.metadata
                )
                responses.append(response)

            return responses

        except Exception as e:
            logger.error(
                "Failed to list transactions",
                error=str(e)
            )
            return []

    async def get_transaction_stats(self, days: int = 7) -> TransactionStats:
        """
        Get transaction statistics
        """
        try:
            from datetime import datetime, timedelta

            start_date = datetime.utcnow() - timedelta(days=days)

            # Basic stats
            total_query = select(func.count(Transaction.id), func.sum(Transaction.amount)).where(
                Transaction.created_at >= start_date
            )
            total_result = await self.db.execute(total_query)
            total_count, total_amount = total_result.first()

            # Fraud stats
            fraud_query = select(func.count(Transaction.id), func.avg(Transaction.fraud_score)).where(
                and_(
                    Transaction.created_at >= start_date,
                    Transaction.decision == FraudDecision.BLOCK.value
                )
            )
            fraud_result = await self.db.execute(fraud_query)
            fraud_count, avg_fraud_score = fraud_result.first()

            # Stats by type
            type_query = select(Transaction.type, func.count(Transaction.id)).where(
                Transaction.created_at >= start_date
            ).group_by(Transaction.type)
            type_result = await self.db.execute(type_query)
            transactions_by_type = dict(type_result.all())

            # Stats by status
            status_query = select(Transaction.status, func.count(Transaction.id)).where(
                Transaction.created_at >= start_date
            ).group_by(Transaction.status)
            status_result = await self.db.execute(status_query)
            transactions_by_status = dict(status_result.all())

            return TransactionStats(
                total_transactions=total_count or 0,
                total_amount=Decimal(str(total_amount or 0)),
                fraud_count=fraud_count or 0,
                fraud_rate=(fraud_count / total_count * 100) if total_count > 0 else 0.0,
                avg_fraud_score=float(avg_fraud_score or 0),
                avg_processing_time_ms=0.0,  # Would need to store this
                transactions_by_type=transactions_by_type,
                transactions_by_status=transactions_by_status
            )

        except Exception as e:
            logger.error(
                "Failed to get transaction stats",
                error=str(e)
            )
            # Return empty stats
            return TransactionStats(
                total_transactions=0,
                total_amount=Decimal("0"),
                fraud_count=0,
                fraud_rate=0.0,
                avg_fraud_score=0.0,
                avg_processing_time_ms=0.0,
                transactions_by_type={},
                transactions_by_status={}
            )
