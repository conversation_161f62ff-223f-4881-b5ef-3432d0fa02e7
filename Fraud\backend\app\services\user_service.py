"""
User management service
"""

from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

from app.core.logging import get_logger
from app.core.security import get_password_hash, verify_password, check_permissions
from app.schemas.auth import UserCreate, UserUpdate

logger = get_logger(__name__)


# Mock User model for now - would be replaced with actual SQLAlchemy model
class User:
    """Mock User model"""
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', uuid4())
        self.email = kwargs.get('email')
        self.full_name = kwargs.get('full_name')
        self.role = kwargs.get('role', 'user')
        self.is_active = kwargs.get('is_active', True)
        self.password_hash = kwargs.get('password_hash')
        self.created_at = kwargs.get('created_at', datetime.utcnow())
        self.last_login = kwargs.get('last_login')
        self.permissions = self._get_permissions_for_role(self.role)
    
    def _get_permissions_for_role(self, role: str) -> List[str]:
        """Get permissions for user role"""
        permissions_map = {
            "admin": [
                "read:transactions", "write:transactions",
                "read:users", "write:users",
                "read:alerts", "write:alerts",
                "read:analytics", "manage:system"
            ],
            "analyst": [
                "read:transactions", "read:alerts", "write:alerts", "read:analytics"
            ],
            "operator": [
                "read:transactions", "read:alerts", "write:alerts"
            ],
            "user": ["read:transactions"]
        }
        return permissions_map.get(role, [])


class UserService:
    """Service for user management operations"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # Mock user storage - would be replaced with database operations
        self._users = {}
    
    async def create_user(
        self,
        email: str,
        password: str,
        full_name: str,
        role: str = "user"
    ) -> User:
        """
        Create a new user
        
        Args:
            email: User email
            password: User password
            full_name: User full name
            role: User role
            
        Returns:
            Created user
        """
        try:
            # Hash password
            password_hash = get_password_hash(password)
            
            # Create user
            user = User(
                email=email,
                full_name=full_name,
                role=role,
                password_hash=password_hash,
                is_active=True,
                created_at=datetime.utcnow()
            )
            
            # Store user (mock storage)
            self._users[str(user.id)] = user
            
            logger.info(
                "User created successfully",
                user_id=user.id,
                email=email,
                role=role
            )
            
            return user
            
        except Exception as e:
            logger.error(
                "Failed to create user",
                email=email,
                error=str(e)
            )
            raise
    
    async def get_user(self, user_id: UUID) -> Optional[User]:
        """
        Get user by ID
        
        Args:
            user_id: User ID
            
        Returns:
            User if found, None otherwise
        """
        try:
            # Mock retrieval
            user = self._users.get(str(user_id))
            return user
            
        except Exception as e:
            logger.error(
                "Failed to get user",
                user_id=user_id,
                error=str(e)
            )
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email
        
        Args:
            email: User email
            
        Returns:
            User if found, None otherwise
        """
        try:
            # Mock search
            for user in self._users.values():
                if user.email == email:
                    return user
            return None
            
        except Exception as e:
            logger.error(
                "Failed to get user by email",
                email=email,
                error=str(e)
            )
            return None
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password
        
        Args:
            email: User email
            password: User password
            
        Returns:
            User if authentication successful, None otherwise
        """
        try:
            user = await self.get_user_by_email(email)
            
            if not user:
                return None
            
            if not verify_password(password, user.password_hash):
                return None
            
            return user
            
        except Exception as e:
            logger.error(
                "Authentication failed",
                email=email,
                error=str(e)
            )
            return None
    
    async def update_user(self, user_id: UUID, user_data: UserUpdate) -> Optional[User]:
        """
        Update user information
        
        Args:
            user_id: User ID
            user_data: Updated user data
            
        Returns:
            Updated user if successful, None otherwise
        """
        try:
            user = await self.get_user(user_id)
            
            if not user:
                return None
            
            # Update fields
            if user_data.full_name is not None:
                user.full_name = user_data.full_name
            
            if user_data.role is not None:
                user.role = user_data.role
                user.permissions = user._get_permissions_for_role(user.role)
            
            if user_data.is_active is not None:
                user.is_active = user_data.is_active
            
            logger.info(
                "User updated successfully",
                user_id=user_id
            )
            
            return user
            
        except Exception as e:
            logger.error(
                "Failed to update user",
                user_id=user_id,
                error=str(e)
            )
            return None
    
    async def change_password(
        self,
        user_id: UUID,
        current_password: str,
        new_password: str
    ) -> bool:
        """
        Change user password
        
        Args:
            user_id: User ID
            current_password: Current password
            new_password: New password
            
        Returns:
            True if successful, False otherwise
        """
        try:
            user = await self.get_user(user_id)
            
            if not user:
                return False
            
            # Verify current password
            if not verify_password(current_password, user.password_hash):
                return False
            
            # Update password
            user.password_hash = get_password_hash(new_password)
            
            logger.info(
                "Password changed successfully",
                user_id=user_id
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to change password",
                user_id=user_id,
                error=str(e)
            )
            return False
    
    async def update_last_login(self, user_id: UUID) -> bool:
        """
        Update user's last login timestamp
        
        Args:
            user_id: User ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            user = await self.get_user(user_id)
            
            if not user:
                return False
            
            user.last_login = datetime.utcnow()
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to update last login",
                user_id=user_id,
                error=str(e)
            )
            return False
    
    async def list_users(
        self,
        skip: int = 0,
        limit: int = 100,
        role: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[User]:
        """
        List users with optional filtering
        
        Args:
            skip: Number of users to skip
            limit: Maximum number of users to return
            role: Filter by role
            is_active: Filter by active status
            
        Returns:
            List of users
        """
        try:
            users = list(self._users.values())
            
            # Apply filters
            if role is not None:
                users = [u for u in users if u.role == role]
            
            if is_active is not None:
                users = [u for u in users if u.is_active == is_active]
            
            # Apply pagination
            users = users[skip:skip + limit]
            
            return users
            
        except Exception as e:
            logger.error(
                "Failed to list users",
                error=str(e)
            )
            return []
    
    async def deactivate_user(self, user_id: UUID) -> bool:
        """
        Deactivate user (soft delete)
        
        Args:
            user_id: User ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            user = await self.get_user(user_id)
            
            if not user:
                return False
            
            user.is_active = False
            
            logger.info(
                "User deactivated",
                user_id=user_id
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "Failed to deactivate user",
                user_id=user_id,
                error=str(e)
            )
            return False
