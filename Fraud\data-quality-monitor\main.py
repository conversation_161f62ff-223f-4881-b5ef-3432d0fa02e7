"""
Data Quality Monitor for FraudShield
Monitors data quality metrics and alerts on anomalies
"""

import asyncio
import json
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

import structlog
from kafka import KafkaConsumer
from kafka.errors import KafkaError

from core.config import settings
from core.logging import setup_logging
from services.quality_monitor import QualityMonitor
from services.alert_service import AlertService
from services.metrics_collector import MetricsCollector

# Setup logging
setup_logging()
logger = structlog.get_logger()


class DataQualityMonitor:
    """Main data quality monitoring service"""
    
    def __init__(self):
        self.consumer: Optional[KafkaConsumer] = None
        self.quality_monitor: Optional[QualityMonitor] = None
        self.alert_service: Optional[AlertService] = None
        self.metrics_collector: Optional[MetricsCollector] = None
        self.running = False
    
    async def initialize(self):
        """Initialize data quality monitor"""
        try:
            # Initialize Kafka consumer for multiple topics
            topics = [
                settings.KAFKA_TOPIC_RAW_TRANSACTIONS,
                settings.KAFKA_TOPIC_ENRICHED_TRANSACTIONS,
                settings.KAFKA_TOPIC_DLQ
            ]
            
            self.consumer = KafkaConsumer(
                *topics,
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS.split(','),
                auto_offset_reset='latest',
                enable_auto_commit=True,
                group_id='data-quality-monitor',
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                consumer_timeout_ms=1000
            )
            
            # Initialize services
            self.metrics_collector = MetricsCollector()
            await self.metrics_collector.initialize()
            
            self.alert_service = AlertService()
            await self.alert_service.initialize()
            
            self.quality_monitor = QualityMonitor(
                self.metrics_collector,
                self.alert_service
            )
            await self.quality_monitor.initialize()
            
            logger.info("Data quality monitor initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize data quality monitor", error=str(e))
            raise
    
    async def start(self):
        """Start data quality monitoring"""
        self.running = True
        logger.info("Starting data quality monitor")
        
        try:
            # Start background tasks
            tasks = [
                asyncio.create_task(self._monitor_stream()),
                asyncio.create_task(self._periodic_quality_checks()),
                asyncio.create_task(self._generate_reports())
            ]
            
            # Wait for all tasks
            await asyncio.gather(*tasks)
            
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        except Exception as e:
            logger.error("Data quality monitoring error", error=str(e))
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """Stop data quality monitoring"""
        self.running = False
        logger.info("Stopping data quality monitor")
        
        if self.consumer:
            self.consumer.close()
        
        if self.quality_monitor:
            await self.quality_monitor.close()
        
        if self.alert_service:
            await self.alert_service.close()
        
        if self.metrics_collector:
            await self.metrics_collector.close()
        
        logger.info("Data quality monitor stopped")
    
    async def _monitor_stream(self):
        """Monitor streaming data quality"""
        while self.running:
            try:
                # Poll for messages
                message_batch = self.consumer.poll(timeout_ms=1000)
                
                if message_batch:
                    await self._process_message_batch(message_batch)
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error("Stream monitoring error", error=str(e))
                await asyncio.sleep(1)
    
    async def _process_message_batch(self, message_batch: Dict):
        """Process a batch of messages for quality monitoring"""
        for topic_partition, messages in message_batch.items():
            topic = topic_partition.topic
            
            for message in messages:
                try:
                    await self.quality_monitor.check_message_quality(
                        topic, message.value, message.timestamp
                    )
                except Exception as e:
                    logger.error(
                        "Failed to check message quality",
                        topic=topic,
                        offset=message.offset,
                        error=str(e)
                    )
    
    async def _periodic_quality_checks(self):
        """Run periodic quality checks"""
        while self.running:
            try:
                # Run comprehensive quality checks every 5 minutes
                await asyncio.sleep(300)  # 5 minutes
                
                if self.running:
                    await self.quality_monitor.run_periodic_checks()
                
            except Exception as e:
                logger.error("Periodic quality check error", error=str(e))
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _generate_reports(self):
        """Generate periodic quality reports"""
        while self.running:
            try:
                # Generate reports every hour
                await asyncio.sleep(3600)  # 1 hour
                
                if self.running:
                    await self.quality_monitor.generate_quality_report()
                
            except Exception as e:
                logger.error("Report generation error", error=str(e))
                await asyncio.sleep(300)  # Wait 5 minutes before retrying


async def main():
    """Main entry point"""
    monitor = DataQualityMonitor()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        asyncio.create_task(monitor.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await monitor.initialize()
        await monitor.start()
    except Exception as e:
        logger.error("Data quality monitor failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
