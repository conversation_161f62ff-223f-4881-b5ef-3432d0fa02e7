# Phase 2: Data Engineering and Real-Time ML Pipeline - COMPLETED

## Overview

Phase 2 of FraudShield implements a comprehensive data engineering and real-time ML pipeline that processes transaction streams, extracts features, and maintains data quality. This phase transforms raw transaction data into enriched, feature-rich data ready for real-time fraud detection.

## 🏗️ Architecture Components

### 1. Feature Store Service (`feature-store/`)
**Purpose**: Centralized feature computation and storage for real-time and batch ML workflows.

**Key Features**:
- **Online Feature Store**: Redis-based low-latency feature serving
- **Offline Feature Store**: InfluxDB-based historical feature storage
- **Feature Groups**: Account, Transaction, Relationship, and Velocity features
- **Caching**: Intelligent caching with TTL management
- **API**: RESTful API for feature extraction and retrieval

**Endpoints**:
- `POST /features` - Extract features for single transaction
- `POST /features/batch` - Batch feature extraction
- `POST /features/store` - Store computed features
- `GET /features/stats` - Feature store statistics

### 2. Stream Processing Service (`stream-processor/`)
**Purpose**: Real-time processing of transaction streams with validation, enrichment, and feature extraction.

**Key Features**:
- **Kafka Integration**: Consumes from raw-transactions, produces to enriched-transactions
- **Data Validation**: Comprehensive validation rules for data quality
- **Data Enrichment**: Adds derived fields, metadata, and risk indicators
- **Error Handling**: Dead letter queue for failed transactions
- **Performance Monitoring**: Processing time and throughput metrics

### 3. Data Quality Monitor (`data-quality-monitor/`)
**Purpose**: Continuous monitoring of data quality with automated alerting.

**Key Features**:
- **Real-time Monitoring**: Monitors all Kafka topics for quality issues
- **Quality Metrics**: Null rates, duplicates, schema violations, outliers
- **Alerting**: Email and system alerts for quality degradation
- **Reporting**: Periodic quality reports and trend analysis
- **Thresholds**: Configurable quality thresholds and SLAs

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.11+ (for data ingestion scripts)
- 8GB+ RAM recommended

### Setup and Launch
```bash
# Make setup script executable
chmod +x scripts/phase2-setup.sh

# Run Phase 2 setup
./scripts/phase2-setup.sh
```

This script will:
1. Create necessary directories and configuration files
2. Build and start all Docker services
3. Initialize Kafka topics
4. Set up monitoring dashboards
5. Create sample data ingestion scripts

### Verify Installation
```bash
# Check all services are running
docker-compose ps

# Check service logs
docker-compose logs -f feature-store
docker-compose logs -f stream-processor
docker-compose logs -f data-quality-monitor
```

## 📊 Data Flow

```
Raw Transactions (Kafka) 
    ↓
Stream Processor
    ├── Validation
    ├── Enrichment  
    └── Basic Feature Extraction
    ↓
Enriched Transactions (Kafka)
    ↓
Feature Store
    ├── Advanced Feature Computation
    ├── Historical Feature Lookup
    └── Feature Caching (Redis)
    ↓
ML Model Serving
```

## 🔧 Feature Engineering

### Feature Groups

#### 1. Account Features
- Transaction frequency (1h, 24h, 7d, 30d)
- Average transaction amounts
- Unique counterparties
- Account age and risk scores
- Balance trends

#### 2. Transaction Features  
- Temporal features (hour, day, weekend)
- Amount ratios and percentiles
- Balance change analysis
- Transaction type patterns

#### 3. Relationship Features
- Transaction history between accounts
- Frequency of interactions
- Amount patterns between parties
- New vs. existing relationships

#### 4. Velocity Features
- Transaction velocity per account
- Amount velocity tracking
- Frequency change detection
- High-velocity risk indicators

### Feature Computation
Features are computed in real-time using:
- **Redis**: For velocity counters and recent transaction history
- **InfluxDB**: For historical aggregations and time-series analysis
- **In-memory**: For stateless derived features

## 📈 Monitoring and Observability

### Metrics Collection
- **Prometheus**: Application metrics and performance indicators
- **InfluxDB**: Time-series data for transactions and features
- **Grafana**: Dashboards for monitoring and alerting

### Key Metrics
- **Throughput**: Transactions processed per second
- **Latency**: Feature extraction and processing times
- **Quality**: Data quality scores and violation rates
- **Cache Performance**: Hit rates and response times

### Access Points
- **Grafana**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090
- **InfluxDB**: http://localhost:8086

## 🧪 Testing and Validation

### Sample Data Ingestion
```bash
# Install required Python packages
pip install kafka-python pandas

# Run sample data ingestion
python scripts/ingest-sample-data.py
```

### API Testing
```bash
# Test Feature Store API
curl -X POST http://localhost:8002/features \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_id": "test-123",
    "transaction_data": {
      "transaction_id": "test-123",
      "step": 1,
      "type": "PAYMENT",
      "amount": 1000.0,
      "name_orig": "C123",
      "oldbalance_org": 5000.0,
      "newbalance_orig": 4000.0,
      "name_dest": "M456",
      "oldbalance_dest": 0.0,
      "newbalance_dest": 0.0
    }
  }'

# Check Feature Store stats
curl http://localhost:8002/features/stats
```

### Data Quality Verification
Monitor data quality through:
1. Grafana dashboards showing quality metrics
2. Application logs for validation errors
3. Dead letter queue for failed transactions

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```bash
# Data Quality Thresholds
NULL_RATE_THRESHOLD=0.05          # 5% null rate threshold
DUPLICATE_RATE_THRESHOLD=0.01     # 1% duplicate rate threshold
SCHEMA_VIOLATION_THRESHOLD=0.02   # 2% schema violation threshold

# Performance Settings
MAX_CONCURRENT_TRANSACTIONS=100   # Concurrent processing limit
PROCESSING_TIMEOUT_SECONDS=30     # Processing timeout

# Feature Store Settings
REDIS_FEATURE_TTL=86400          # Feature cache TTL (24 hours)
FEATURE_COMPUTATION_TIMEOUT=30   # Feature computation timeout
```

### Kafka Topics
- `raw-transactions`: Incoming raw transaction data
- `enriched-transactions`: Processed and enriched transactions
- `dead-letter-queue`: Failed transactions for investigation
- `fraud-alerts`: Real-time fraud alerts

## 🚨 Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check Docker resources
docker system df
docker system prune  # If needed

# Restart specific service
docker-compose restart feature-store
```

#### Kafka Connection Issues
```bash
# Check Kafka topics
docker-compose exec kafka kafka-topics --list --bootstrap-server localhost:9092

# Check consumer groups
docker-compose exec kafka kafka-consumer-groups --bootstrap-server localhost:9092 --list
```

#### Feature Store Issues
```bash
# Check Redis connection
docker-compose exec redis redis-cli ping

# Check InfluxDB health
curl http://localhost:8086/health
```

### Performance Tuning

#### For High Throughput
1. Increase Kafka partitions
2. Scale stream processor instances
3. Optimize Redis memory settings
4. Tune InfluxDB batch sizes

#### For Low Latency
1. Increase Redis memory allocation
2. Optimize feature computation algorithms
3. Use feature caching aggressively
4. Minimize external service calls

## 📚 Next Steps

Phase 2 provides the foundation for:

1. **Phase 3**: Advanced ML model training and deployment
2. **Phase 4**: Real-time fraud detection and alerting
3. **Phase 5**: Model monitoring and continuous learning

### Integration Points
- **Backend API**: Consumes enriched transactions for fraud scoring
- **ML Service**: Uses features for real-time predictions
- **Monitoring**: Provides observability across the pipeline

## 🎯 Success Criteria

Phase 2 is considered successful when:

✅ **Data Pipeline**: Processes transactions end-to-end with <100ms latency  
✅ **Feature Store**: Serves features with >99% availability and <10ms response time  
✅ **Data Quality**: Maintains >95% data quality scores across all metrics  
✅ **Monitoring**: Provides comprehensive observability and alerting  
✅ **Scalability**: Handles 1000+ transactions per second  

## 📞 Support

For issues or questions:
1. Check service logs: `docker-compose logs [service-name]`
2. Review monitoring dashboards in Grafana
3. Consult API documentation at service endpoints `/docs`
4. Check this documentation and troubleshooting guides
