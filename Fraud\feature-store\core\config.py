"""
Feature Store Configuration
"""

import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Feature Store settings"""
    
    # Application
    APP_NAME: str = "FraudShield Feature Store"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    
    # Security
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    
    # Redis
    REDIS_URL: str = Field(env="REDIS_URL")
    REDIS_CACHE_TTL: int = Field(default=3600, env="REDIS_CACHE_TTL")  # 1 hour
    REDIS_FEATURE_TTL: int = Field(default=86400, env="REDIS_FEATURE_TTL")  # 24 hours
    
    # InfluxDB
    INFLUXDB_URL: str = Field(env="INFLUXDB_URL")
    INFLUXDB_TOKEN: str = Field(env="INFLUXDB_TOKEN")
    INFLUXDB_ORG: str = Field(env="INFLUXDB_ORG")
    INFLUXDB_BUCKET: str = Field(env="INFLUXDB_BUCKET")
    
    # Feature Store Configuration
    FEATURE_CACHE_SIZE: int = Field(default=10000, env="FEATURE_CACHE_SIZE")
    FEATURE_BATCH_SIZE: int = Field(default=100, env="FEATURE_BATCH_SIZE")
    FEATURE_COMPUTATION_TIMEOUT: int = Field(default=30, env="FEATURE_COMPUTATION_TIMEOUT")
    
    # Historical Data Windows
    TRANSACTION_HISTORY_DAYS: int = Field(default=30, env="TRANSACTION_HISTORY_DAYS")
    ACCOUNT_HISTORY_DAYS: int = Field(default=90, env="ACCOUNT_HISTORY_DAYS")
    
    # Feature Engineering
    ENABLE_REAL_TIME_FEATURES: bool = Field(default=True, env="ENABLE_REAL_TIME_FEATURES")
    ENABLE_HISTORICAL_FEATURES: bool = Field(default=True, env="ENABLE_HISTORICAL_FEATURES")
    ENABLE_DERIVED_FEATURES: bool = Field(default=True, env="ENABLE_DERIVED_FEATURES")
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
