"""
FraudShield Feature Store Service
Manages online and offline feature storage for real-time fraud detection
"""

import asyncio
import json
import time
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

import structlog
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from starlette.responses import Response

from core.config import settings
from core.database import init_db
from core.logging import setup_logging
from services.feature_service import FeatureService
from services.redis_service import RedisService
from services.influxdb_service import InfluxDBService
from schemas.feature_schemas import (
    FeatureRequest, FeatureResponse, BatchFeatureRequest, 
    BatchFeatureResponse, FeatureStoreStats
)

# Setup logging
setup_logging()
logger = structlog.get_logger()

# Metrics
FEATURE_REQUESTS = Counter('feature_store_requests_total', 'Total feature requests', ['operation', 'status'])
FEATURE_LATENCY = Histogram('feature_store_latency_seconds', 'Feature store operation latency', ['operation'])
CACHE_HITS = Counter('feature_store_cache_hits_total', 'Cache hits')
CACHE_MISSES = Counter('feature_store_cache_misses_total', 'Cache misses')

# Global services
feature_service: Optional[FeatureService] = None
redis_service: Optional[RedisService] = None
influxdb_service: Optional[InfluxDBService] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global feature_service, redis_service, influxdb_service
    
    # Startup
    logger.info("Starting Feature Store Service", version=settings.VERSION)
    
    # Initialize database
    await init_db()
    
    # Initialize services
    redis_service = RedisService()
    await redis_service.initialize()
    
    influxdb_service = InfluxDBService()
    await influxdb_service.initialize()
    
    feature_service = FeatureService(redis_service, influxdb_service)
    await feature_service.initialize()
    
    logger.info("Feature Store Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Feature Store Service")
    if redis_service:
        await redis_service.close()
    if influxdb_service:
        await influxdb_service.close()


# Create FastAPI application
app = FastAPI(
    title="FraudShield Feature Store",
    description="Feature store service for real-time fraud detection",
    version=settings.VERSION,
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def add_process_time_header(request, call_next):
    """Add processing time header"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Health check endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


# Feature endpoints
@app.post("/features", response_model=FeatureResponse)
async def get_features(
    request: FeatureRequest,
    background_tasks: BackgroundTasks
) -> FeatureResponse:
    """Get features for a single transaction"""
    if not feature_service:
        raise HTTPException(status_code=503, detail="Feature service not initialized")
    
    start_time = time.time()
    
    try:
        with FEATURE_LATENCY.labels(operation="get_features").time():
            features = await feature_service.get_features(request)
        
        FEATURE_REQUESTS.labels(operation="get_features", status="success").inc()
        
        # Log feature extraction
        logger.info(
            "Features extracted",
            transaction_id=request.transaction_id,
            feature_count=len(features.features),
            processing_time=time.time() - start_time
        )
        
        return features
        
    except Exception as e:
        FEATURE_REQUESTS.labels(operation="get_features", status="error").inc()
        logger.error(
            "Feature extraction failed",
            transaction_id=request.transaction_id,
            error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"Feature extraction failed: {str(e)}")


@app.post("/features/batch", response_model=BatchFeatureResponse)
async def get_features_batch(
    request: BatchFeatureRequest,
    background_tasks: BackgroundTasks
) -> BatchFeatureResponse:
    """Get features for multiple transactions"""
    if not feature_service:
        raise HTTPException(status_code=503, detail="Feature service not initialized")
    
    start_time = time.time()
    
    try:
        with FEATURE_LATENCY.labels(operation="get_features_batch").time():
            results = await feature_service.get_features_batch(request)
        
        FEATURE_REQUESTS.labels(operation="get_features_batch", status="success").inc()
        
        logger.info(
            "Batch features extracted",
            batch_size=len(request.transactions),
            processing_time=time.time() - start_time
        )
        
        return results
        
    except Exception as e:
        FEATURE_REQUESTS.labels(operation="get_features_batch", status="error").inc()
        logger.error(
            "Batch feature extraction failed",
            batch_size=len(request.transactions),
            error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"Batch feature extraction failed: {str(e)}")


@app.post("/features/store")
async def store_features(
    transaction_id: str,
    features: Dict[str, Any],
    background_tasks: BackgroundTasks
):
    """Store computed features"""
    if not feature_service:
        raise HTTPException(status_code=503, detail="Feature service not initialized")
    
    try:
        with FEATURE_LATENCY.labels(operation="store_features").time():
            await feature_service.store_features(transaction_id, features)
        
        FEATURE_REQUESTS.labels(operation="store_features", status="success").inc()
        
        logger.info(
            "Features stored",
            transaction_id=transaction_id,
            feature_count=len(features)
        )
        
        return {"status": "success", "transaction_id": transaction_id}
        
    except Exception as e:
        FEATURE_REQUESTS.labels(operation="store_features", status="error").inc()
        logger.error(
            "Feature storage failed",
            transaction_id=transaction_id,
            error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"Feature storage failed: {str(e)}")


@app.get("/features/stats", response_model=FeatureStoreStats)
async def get_feature_store_stats():
    """Get feature store statistics"""
    if not feature_service:
        raise HTTPException(status_code=503, detail="Feature service not initialized")
    
    try:
        stats = await feature_service.get_stats()
        return stats
        
    except Exception as e:
        logger.error("Failed to get feature store stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=settings.ENVIRONMENT == "development"
    )
