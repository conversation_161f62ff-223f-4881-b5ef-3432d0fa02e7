# FraudShield Frontend

A modern React-based frontend application for the FraudShield fraud detection system, built with TypeScript, Vite, and Tailwind CSS.

## 🚀 Features

### Core Functionality
- **Real-time Dashboard** - Live fraud detection metrics and system overview
- **Transaction Management** - View, filter, and analyze transactions with fraud scores
- **Alert System** - Manage fraud alerts with severity levels and status tracking
- **Analytics & Reporting** - Comprehensive fraud detection analytics and trends
- **User Management** - Admin interface for managing users and permissions
- **Real-time Updates** - WebSocket integration for live transaction and alert feeds

### Technical Features
- **Modern React** - Built with React 18 and TypeScript
- **State Management** - Zustand for efficient state management
- **Real-time Communication** - Socket.IO for WebSocket connections
- **Responsive Design** - Mobile-first design with Tailwind CSS
- **Component Library** - Reusable UI components with Headless UI
- **Form Handling** - React Hook Form with validation
- **Charts & Visualization** - Recharts for data visualization
- **Authentication** - JWT-based authentication with role-based access control
- **Error Handling** - Comprehensive error handling and user feedback
- **Performance** - Optimized builds with Vite and code splitting

## 📋 Prerequisites

- Node.js 18+ and npm
- Backend API running (see backend README)
- Modern web browser with WebSocket support

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fraud-detection-system/frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   VITE_API_BASE_URL=http://localhost:8000/api/v1
   VITE_WS_URL=ws://localhost:8000
   VITE_APP_ENV=development
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:5173`

## 🏗️ Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── ui/           # Base UI components
│   │   ├── layout/       # Layout components
│   │   ├── auth/         # Authentication components
│   │   ├── dashboard/    # Dashboard-specific components
│   │   ├── transactions/ # Transaction-related components
│   │   ├── alerts/       # Alert-related components
│   │   └── notifications/ # Notification components
│   ├── hooks/            # Custom React hooks
│   ├── pages/            # Page components
│   ├── services/         # API and WebSocket services
│   ├── store/            # Zustand state management
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│   ├── App.tsx           # Main application component
│   ├── main.tsx          # Application entry point
│   └── index.css         # Global styles
├── nginx.conf            # Nginx configuration for production
├── Dockerfile            # Docker configuration
├── package.json          # Dependencies and scripts
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
└── vite.config.ts        # Vite configuration
```

## 🎨 UI Components

### Base Components
- **Button** - Configurable button with variants and sizes
- **Card** - Container component with header, body, and footer
- **Modal** - Accessible modal dialogs with animations
- **Badge** - Status indicators with color variants
- **LoadingSpinner** - Loading indicators
- **Pagination** - Data pagination with page controls

### Feature Components
- **TransactionTable** - Data table for transactions with sorting and filtering
- **AlertTable** - Alert management table with status updates
- **FraudTrendsChart** - Interactive charts for fraud analytics
- **RealTimeActivity** - Live feed of transactions and alerts
- **NotificationPanel** - Real-time notification system

## 🔐 Authentication & Authorization

### Authentication Flow
1. **Login** - JWT token-based authentication
2. **Token Management** - Automatic token refresh
3. **Protected Routes** - Route-level access control
4. **Permission Checks** - Component-level permission validation

### User Roles
- **Admin** - Full system access including user management
- **Analyst** - Access to analytics and reporting features
- **Operator** - Transaction and alert management
- **User** - Basic dashboard and transaction viewing

## 📊 State Management

### Zustand Stores
- **authStore** - User authentication and session management
- **transactionStore** - Transaction data and operations
- **alertStore** - Alert management and real-time updates
- **dashboardStore** - Dashboard metrics and analytics

### Real-time Updates
- **WebSocket Integration** - Live transaction and alert feeds
- **Automatic Refresh** - Configurable auto-refresh for dashboard
- **Optimistic Updates** - Immediate UI updates with rollback on error

## 🎯 Key Features

### Dashboard
- Real-time fraud detection metrics
- Transaction volume and fraud rate trends
- Recent transactions and alerts
- System performance indicators

### Transaction Management
- Comprehensive transaction listing with filters
- Detailed transaction views with fraud analysis
- Manual transaction scoring interface
- Export capabilities for reporting

### Alert System
- Real-time fraud alert notifications
- Alert severity and status management
- Bulk alert operations
- Alert resolution workflow

### Analytics
- Fraud detection performance metrics
- Transaction pattern analysis
- Risk factor identification
- Customizable reporting periods

## 🚀 Deployment

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

### Docker Deployment
```bash
# Build Docker image
docker build -t fraudshield-frontend .

# Run container
docker run -p 3000:3000 fraudshield-frontend
```

### Production Considerations
- **Environment Variables** - Configure API endpoints for production
- **HTTPS** - Enable SSL/TLS for secure communication
- **CDN** - Use CDN for static asset delivery
- **Monitoring** - Implement error tracking and performance monitoring
- **Caching** - Configure appropriate cache headers

## 🧪 Testing

```bash
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Generate coverage report
```

### Testing Strategy
- **Unit Tests** - Component and utility function testing
- **Integration Tests** - API integration and user flow testing
- **E2E Tests** - End-to-end user journey testing

## 🔧 Configuration

### Environment Variables
- `VITE_API_BASE_URL` - Backend API base URL
- `VITE_WS_URL` - WebSocket server URL
- `VITE_APP_ENV` - Application environment
- `VITE_ENABLE_ANALYTICS` - Enable/disable analytics features
- `VITE_ENABLE_REAL_TIME` - Enable/disable real-time features

### Customization
- **Theming** - Modify Tailwind configuration for custom styling
- **Components** - Extend or customize UI components
- **API Integration** - Adapt API service layer for different backends
- **Authentication** - Customize authentication flow and providers

## 📝 API Integration

### REST API
- **Authentication** - JWT token-based authentication
- **CRUD Operations** - Full CRUD for transactions, alerts, and users
- **Filtering & Pagination** - Advanced query capabilities
- **Error Handling** - Comprehensive error response handling

### WebSocket Events
- **Real-time Transactions** - Live transaction processing updates
- **Alert Notifications** - Immediate fraud alert delivery
- **System Status** - Connection status and system health
- **User Presence** - Multi-user collaboration features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API documentation
- Contact the development team

## 🔄 Version History

- **v1.0.0** - Initial release with core fraud detection features
- Real-time dashboard and transaction management
- Alert system with notification capabilities
- User management and role-based access control
- Comprehensive analytics and reporting
