{"name": "fraudshield-frontend", "version": "1.0.0", "description": "FraudShield Frontend Application", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "tsc && vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "docker:build": "docker build -t fraudshield-frontend .", "docker:run": "docker run -p 3000:3000 fraudshield-frontend"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "socket.io-client": "^4.7.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "jsdom": "^23.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}