import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { startAutoRefresh } from '@/store/dashboardStore';

// Layout Components
import Layout from '@/components/layout/Layout';
import AuthLayout from '@/components/layout/AuthLayout';

// Page Components
import Dashboard from '@/pages/Dashboard';
import Transactions from '@/pages/Transactions';
import TransactionDetail from '@/pages/TransactionDetail';
import Alerts from '@/pages/Alerts';
import AlertDetail from '@/pages/AlertDetail';
import Analytics from '@/pages/Analytics';
import Users from '@/pages/Users';
import Settings from '@/pages/Settings';
import Login from '@/pages/Login';
import Register from '@/pages/Register';
import NotFound from '@/pages/NotFound';

// Protected Route Component
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function App() {
  const { isAuthenticated, getCurrentUser } = useAuth();

  useEffect(() => {
    // Initialize user session if authenticated
    if (isAuthenticated) {
      getCurrentUser();
      startAutoRefresh();
    }
  }, [isAuthenticated, getCurrentUser]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <AuthLayout>
                <Login />
              </AuthLayout>
            )
          }
        />
        <Route
          path="/register"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <AuthLayout>
                <Register />
              </AuthLayout>
            )
          }
        />

        {/* Protected Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          
          {/* Transaction Routes */}
          <Route path="transactions" element={<Transactions />} />
          <Route path="transactions/:id" element={<TransactionDetail />} />
          
          {/* Alert Routes */}
          <Route path="alerts" element={<Alerts />} />
          <Route path="alerts/:id" element={<AlertDetail />} />
          
          {/* Analytics Routes */}
          <Route path="analytics" element={<Analytics />} />
          
          {/* User Management Routes */}
          <Route
            path="users"
            element={
              <ProtectedRoute requiredPermission="read:users">
                <Users />
              </ProtectedRoute>
            }
          />
          
          {/* Settings Routes */}
          <Route path="settings" element={<Settings />} />
        </Route>

        {/* Catch all route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </div>
  );
}

export default App;
