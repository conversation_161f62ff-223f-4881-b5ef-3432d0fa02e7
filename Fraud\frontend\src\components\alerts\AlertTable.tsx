import React from 'react';
import { Link } from 'react-router-dom';
import { EyeIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { 
  formatDateTime, 
  formatRelativeTime,
  getAlertSeverityColor, 
  getAlertStatusColor 
} from '@/utils';
import { useAlertStore } from '@/store/alertStore';
import Badge from '@/components/ui/Badge';
import Button from '@/components/ui/Button';
import Pagination from '@/components/ui/Pagination';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { toast } from 'react-hot-toast';
import type { Alert } from '@/types';

interface AlertTableProps {
  alerts: Alert[];
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

const AlertTable: React.FC<AlertTableProps> = ({
  alerts,
  isLoading,
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onPageSizeChange,
}) => {
  const { updateAlertStatus } = useAlertStore();

  const handleStatusUpdate = async (alertId: string, status: string, notes?: string) => {
    try {
      await updateAlertStatus(alertId, status, notes);
      toast.success(`Alert ${status.toLowerCase()} successfully`);
    } catch (error) {
      toast.error('Failed to update alert status');
    }
  };

  const getAlertTypeColor = (type: string) => {
    switch (type) {
      case 'FRAUD_DETECTED':
        return 'bg-red-100 text-red-800';
      case 'HIGH_RISK_TRANSACTION':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUSPICIOUS_PATTERN':
        return 'bg-orange-100 text-orange-800';
      case 'SYSTEM_ANOMALY':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (alerts.length === 0 && !isLoading) {
    return (
      <div className="text-center py-12">
        <svg
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No alerts found</h3>
        <p className="mt-1 text-sm text-gray-500">
          No fraud alerts match your current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      {/* Table controls */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label htmlFor="pageSize" className="text-sm font-medium text-gray-700">
                Show:
              </label>
              <select
                id="pageSize"
                value={itemsPerPage}
                onChange={(e) => onPageSizeChange(Number(e.target.value))}
                className="rounded-md border-gray-300 text-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span className="text-sm text-gray-700">per page</span>
            </div>
          </div>
          
          {isLoading && (
            <div className="flex items-center space-x-2">
              <LoadingSpinner size="sm" />
              <span className="text-sm text-gray-500">Loading...</span>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Alert
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Severity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Transaction
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="relative px-6 py-3">
                <span className="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {alerts.map((alert) => (
              <tr key={alert.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                        alert.severity === 'CRITICAL' ? 'bg-red-100' :
                        alert.severity === 'HIGH' ? 'bg-red-100' :
                        alert.severity === 'MEDIUM' ? 'bg-yellow-100' :
                        'bg-blue-100'
                      }`}>
                        <svg
                          className={`h-5 w-5 ${
                            alert.severity === 'CRITICAL' ? 'text-red-600' :
                            alert.severity === 'HIGH' ? 'text-red-600' :
                            alert.severity === 'MEDIUM' ? 'text-yellow-600' :
                            'text-blue-600'
                          }`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {alert.title}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {alert.description}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Badge className={getAlertTypeColor(alert.alert_type)}>
                    {alert.alert_type.replace('_', ' ')}
                  </Badge>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Badge className={getAlertSeverityColor(alert.severity)}>
                    {alert.severity}
                  </Badge>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Badge className={getAlertStatusColor(alert.status)}>
                    {alert.status.replace('_', ' ')}
                  </Badge>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <Link
                    to={`/transactions/${alert.transaction_id}`}
                    className="text-sm text-primary-600 hover:text-primary-500"
                  >
                    {alert.transaction_id}
                  </Link>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {formatRelativeTime(alert.created_at)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatDateTime(alert.created_at)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    {alert.status === 'OPEN' && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStatusUpdate(alert.id, 'RESOLVED', 'Resolved via dashboard')}
                          title="Mark as resolved"
                        >
                          <CheckIcon className="h-4 w-4 text-green-600" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStatusUpdate(alert.id, 'FALSE_POSITIVE', 'Marked as false positive')}
                          title="Mark as false positive"
                        >
                          <XMarkIcon className="h-4 w-4 text-gray-600" />
                        </Button>
                      </>
                    )}
                    <Link to={`/alerts/${alert.id}`}>
                      <Button variant="ghost" size="sm" title="View details">
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
      />
    </div>
  );
};

export default AlertTable;
