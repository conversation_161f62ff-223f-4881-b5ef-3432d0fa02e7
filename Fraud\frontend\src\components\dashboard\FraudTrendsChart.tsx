import React from 'react';
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';
import { useDashboardStore } from '@/store/dashboardStore';
import { formatPercentage, formatNumber } from '@/utils';

const FraudTrendsChart: React.FC = () => {
  const { fraudTrends, transactionStats, isLoading } = useDashboardStore();

  // Combine fraud trends and transaction stats for the chart
  const chartData = transactionStats.map((stat, index) => ({
    timestamp: stat.period,
    fraudRate: stat.fraud_rate * 100, // Convert to percentage
    totalTransactions: stat.total_count,
    fraudTransactions: stat.fraud_count,
    fraudAmount: stat.fraud_amount,
  }));

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Fraud Detection Trends</h3>
        <p className="mt-1 text-sm text-gray-500">
          Real-time fraud detection rates and transaction volumes over time
        </p>
      </div>
      
      <div className="p-6">
        {chartData.length > 0 ? (
          <div className="space-y-6">
            {/* Fraud Rate Chart */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Fraud Rate (%)</h4>
              <ResponsiveContainer width="100%" height={200}>
                <AreaChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric',
                        hour: '2-digit'
                      });
                    }}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => `${value}%`}
                  />
                  <Tooltip
                    formatter={(value: number) => [`${value.toFixed(2)}%`, 'Fraud Rate']}
                    labelFormatter={(label) => {
                      const date = new Date(label);
                      return date.toLocaleString();
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="fraudRate"
                    stroke="#ef4444"
                    fill="#ef4444"
                    fillOpacity={0.1}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>

            {/* Transaction Volume Chart */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Transaction Volume</h4>
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric',
                        hour: '2-digit'
                      });
                    }}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => formatNumber(value)}
                  />
                  <Tooltip
                    formatter={(value: number, name: string) => [
                      formatNumber(value),
                      name === 'totalTransactions' ? 'Total Transactions' : 'Fraud Transactions'
                    ]}
                    labelFormatter={(label) => {
                      const date = new Date(label);
                      return date.toLocaleString();
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="totalTransactions"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    name="Total Transactions"
                  />
                  <Line
                    type="monotone"
                    dataKey="fraudTransactions"
                    stroke="#ef4444"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    name="Fraud Transactions"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No data available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Fraud trend data will appear here once transactions are processed.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FraudTrendsChart;
