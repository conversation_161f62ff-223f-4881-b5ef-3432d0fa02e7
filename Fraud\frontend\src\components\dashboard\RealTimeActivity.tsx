import React from 'react';
import { Link } from 'react-router-dom';
import { useTransactionStore } from '@/store/transactionStore';
import { useAlertStore } from '@/store/alertStore';
import { 
  formatCurrency, 
  formatRelativeTime, 
  getRiskLevelColor, 
  getFraudDecisionColor 
} from '@/utils';

const RealTimeActivity: React.FC = () => {
  const { realtimeTransactions } = useTransactionStore();
  const { realtimeAlerts } = useAlertStore();

  // Combine and sort recent activity
  const recentActivity = [
    ...realtimeTransactions.map(rt => ({
      id: rt.transaction.id,
      type: 'transaction' as const,
      timestamp: rt.transaction.created_at,
      data: rt,
    })),
    ...realtimeAlerts.map(ra => ({
      id: ra.alert.id,
      type: 'alert' as const,
      timestamp: ra.alert.created_at,
      data: ra,
    })),
  ]
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 10);

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Real-Time Activity</h3>
          <div className="flex items-center">
            <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="ml-2 text-sm text-gray-500">Live</span>
          </div>
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Latest transactions and alerts as they happen
        </p>
      </div>

      <div className="divide-y divide-gray-200">
        {recentActivity.length > 0 ? (
          recentActivity.map((activity) => (
            <div key={`${activity.type}-${activity.id}`} className="px-6 py-4 hover:bg-gray-50">
              {activity.type === 'transaction' ? (
                <TransactionActivity data={activity.data} />
              ) : (
                <AlertActivity data={activity.data} />
              )}
            </div>
          ))
        ) : (
          <div className="px-6 py-8 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
            <p className="mt-1 text-sm text-gray-500">
              Real-time transactions and alerts will appear here.
            </p>
          </div>
        )}
      </div>

      {recentActivity.length > 0 && (
        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">
              Showing {recentActivity.length} recent activities
            </span>
            <div className="space-x-2">
              <Link
                to="/transactions"
                className="text-sm text-primary-600 hover:text-primary-500"
              >
                View all transactions
              </Link>
              <span className="text-gray-300">|</span>
              <Link
                to="/alerts"
                className="text-sm text-primary-600 hover:text-primary-500"
              >
                View all alerts
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const TransactionActivity: React.FC<{ data: any }> = ({ data }) => {
  const { transaction, fraud_result } = data;

  return (
    <div className="flex items-center space-x-4">
      <div className="flex-shrink-0">
        <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
          <svg
            className="h-5 w-5 text-blue-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
            />
          </svg>
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium text-gray-900 truncate">
            {transaction.type} - {formatCurrency(transaction.amount)}
          </p>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(fraud_result.risk_level)}`}>
              {fraud_result.risk_level}
            </span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getFraudDecisionColor(fraud_result.decision)}`}>
              {fraud_result.decision}
            </span>
          </div>
        </div>
        <div className="flex items-center justify-between mt-1">
          <p className="text-sm text-gray-500 truncate">
            {transaction.name_orig} → {transaction.name_dest}
          </p>
          <p className="text-xs text-gray-400">
            {formatRelativeTime(transaction.created_at)}
          </p>
        </div>
      </div>
    </div>
  );
};

const AlertActivity: React.FC<{ data: any }> = ({ data }) => {
  const { alert } = data;

  const getSeverityColor = (severity: string) => {
    switch (severity.toUpperCase()) {
      case 'LOW':
        return 'text-blue-600 bg-blue-100';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100';
      case 'HIGH':
        return 'text-red-600 bg-red-100';
      case 'CRITICAL':
        return 'text-red-800 bg-red-200';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="flex items-center space-x-4">
      <div className="flex-shrink-0">
        <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center">
          <svg
            className="h-5 w-5 text-red-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium text-gray-900 truncate">
            {alert.title}
          </p>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
            {alert.severity}
          </span>
        </div>
        <div className="flex items-center justify-between mt-1">
          <p className="text-sm text-gray-500 truncate">
            {alert.description}
          </p>
          <p className="text-xs text-gray-400">
            {formatRelativeTime(alert.created_at)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RealTimeActivity;
