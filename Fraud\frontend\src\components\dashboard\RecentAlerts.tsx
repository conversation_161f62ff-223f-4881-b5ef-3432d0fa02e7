import React from 'react';
import { Link } from 'react-router-dom';
import { 
  formatRelativeTime, 
  getAlertSeverityColor, 
  getAlertStatusColor 
} from '@/utils';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import type { Alert } from '@/types';

interface RecentAlertsProps {
  alerts: Alert[];
  isLoading: boolean;
}

const RecentAlerts: React.FC<RecentAlertsProps> = ({ alerts, isLoading }) => {
  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Recent Alerts</h3>
          <Link
            to="/alerts"
            className="text-sm text-primary-600 hover:text-primary-500"
          >
            View all
          </Link>
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Latest fraud alerts requiring attention
        </p>
      </div>

      <div className="divide-y divide-gray-200">
        {alerts.length > 0 ? (
          alerts.map((alert) => (
            <div key={alert.id} className="px-6 py-4 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className={`h-2 w-2 rounded-full ${
                      alert.severity === 'CRITICAL' ? 'bg-red-500' :
                      alert.severity === 'HIGH' ? 'bg-red-400' :
                      alert.severity === 'MEDIUM' ? 'bg-yellow-400' :
                      'bg-blue-400'
                    }`}></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {alert.title}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {alert.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getAlertSeverityColor(alert.severity)}`}>
                    {alert.severity}
                  </span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getAlertStatusColor(alert.status)}`}>
                    {alert.status}
                  </span>
                </div>
              </div>
              <div className="mt-2 flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>Transaction: {alert.transaction_id}</span>
                  <span>{formatRelativeTime(alert.created_at)}</span>
                </div>
                <Link
                  to={`/alerts/${alert.id}`}
                  className="text-sm text-primary-600 hover:text-primary-500"
                >
                  View details
                </Link>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No alerts</h3>
            <p className="mt-1 text-sm text-gray-500">
              No fraud alerts at the moment. This is good news!
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentAlerts;
