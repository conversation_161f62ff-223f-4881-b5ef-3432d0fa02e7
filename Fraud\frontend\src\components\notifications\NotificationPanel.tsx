import React from 'react';
import { Link } from 'react-router-dom';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useAlertStore } from '@/store/alertStore';
import { formatRelativeTime, getAlertSeverityColor } from '@/utils';

interface NotificationPanelProps {
  onClose: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ onClose }) => {
  const { realtimeAlerts, markAlertAsRead, clearRealtimeAlerts } = useAlertStore();

  const handleMarkAsRead = (alertId: string) => {
    markAlertAsRead(alertId);
  };

  const handleClearAll = () => {
    clearRealtimeAlerts();
    onClose();
  };

  return (
    <div className="max-h-96 overflow-hidden">
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
          <div className="flex items-center space-x-2">
            {realtimeAlerts.length > 0 && (
              <button
                onClick={handleClearAll}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Clear all
              </button>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Notifications list */}
      <div className="max-h-80 overflow-y-auto">
        {realtimeAlerts.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {realtimeAlerts.map((alertData) => (
              <div
                key={alertData.alert.id}
                className={`px-4 py-3 hover:bg-gray-50 ${
                  !alertData.read ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className={`h-2 w-2 rounded-full ${
                      alertData.alert.severity === 'CRITICAL' ? 'bg-red-500' :
                      alertData.alert.severity === 'HIGH' ? 'bg-red-400' :
                      alertData.alert.severity === 'MEDIUM' ? 'bg-yellow-400' :
                      'bg-blue-400'
                    }`}></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {alertData.alert.title}
                      </p>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getAlertSeverityColor(alertData.alert.severity)}`}>
                        {alertData.alert.severity}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 truncate mt-1">
                      {alertData.alert.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <p className="text-xs text-gray-400">
                        {formatRelativeTime(alertData.alert.created_at)}
                      </p>
                      <div className="flex items-center space-x-2">
                        {!alertData.read && (
                          <button
                            onClick={() => handleMarkAsRead(alertData.alert.id)}
                            className="text-xs text-blue-600 hover:text-blue-500"
                          >
                            Mark as read
                          </button>
                        )}
                        <Link
                          to={`/alerts/${alertData.alert.id}`}
                          onClick={onClose}
                          className="text-xs text-blue-600 hover:text-blue-500"
                        >
                          View details
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="px-4 py-8 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 17h5l-5 5v-5zM9 17H4l5 5v-5zM12 3v12"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
            <p className="mt-1 text-sm text-gray-500">
              You're all caught up! New alerts will appear here.
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      {realtimeAlerts.length > 0 && (
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
          <Link
            to="/alerts"
            onClick={onClose}
            className="block text-center text-sm text-blue-600 hover:text-blue-500"
          >
            View all alerts
          </Link>
        </div>
      )}
    </div>
  );
};

export default NotificationPanel;
