import React, { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';

interface TransactionFiltersProps {
  filters: any;
  onFiltersChange: (filters: any) => void;
  onClose: () => void;
}

const TransactionFilters: React.FC<TransactionFiltersProps> = ({
  filters,
  onFiltersChange,
  onClose,
}) => {
  const [localFilters, setLocalFilters] = useState(filters);

  const handleFilterChange = (key: string, value: string) => {
    setLocalFilters((prev: any) => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleClearFilters = () => {
    const clearedFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Filter Transactions</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* Transaction Type */}
        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-700">
            Transaction Type
          </label>
          <select
            id="type"
            value={localFilters.type || ''}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          >
            <option value="">All Types</option>
            <option value="PAYMENT">Payment</option>
            <option value="TRANSFER">Transfer</option>
            <option value="CASH_OUT">Cash Out</option>
            <option value="DEBIT">Debit</option>
            <option value="CASH_IN">Cash In</option>
          </select>
        </div>

        {/* Risk Level */}
        <div>
          <label htmlFor="risk_level" className="block text-sm font-medium text-gray-700">
            Risk Level
          </label>
          <select
            id="risk_level"
            value={localFilters.risk_level || ''}
            onChange={(e) => handleFilterChange('risk_level', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          >
            <option value="">All Levels</option>
            <option value="LOW">Low</option>
            <option value="MEDIUM">Medium</option>
            <option value="HIGH">High</option>
          </select>
        </div>

        {/* Decision */}
        <div>
          <label htmlFor="decision" className="block text-sm font-medium text-gray-700">
            Decision
          </label>
          <select
            id="decision"
            value={localFilters.decision || ''}
            onChange={(e) => handleFilterChange('decision', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          >
            <option value="">All Decisions</option>
            <option value="ALLOW">Allow</option>
            <option value="REVIEW">Review</option>
            <option value="BLOCK">Block</option>
          </select>
        </div>

        {/* Amount Range */}
        <div>
          <label htmlFor="min_amount" className="block text-sm font-medium text-gray-700">
            Min Amount
          </label>
          <input
            type="number"
            id="min_amount"
            value={localFilters.min_amount || ''}
            onChange={(e) => handleFilterChange('min_amount', e.target.value)}
            placeholder="0"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="max_amount" className="block text-sm font-medium text-gray-700">
            Max Amount
          </label>
          <input
            type="number"
            id="max_amount"
            value={localFilters.max_amount || ''}
            onChange={(e) => handleFilterChange('max_amount', e.target.value)}
            placeholder="No limit"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          />
        </div>

        {/* Date Range */}
        <div>
          <label htmlFor="start_date" className="block text-sm font-medium text-gray-700">
            Start Date
          </label>
          <input
            type="datetime-local"
            id="start_date"
            value={localFilters.start_date || ''}
            onChange={(e) => handleFilterChange('start_date', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="end_date" className="block text-sm font-medium text-gray-700">
            End Date
          </label>
          <input
            type="datetime-local"
            id="end_date"
            value={localFilters.end_date || ''}
            onChange={(e) => handleFilterChange('end_date', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          />
        </div>

        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700">
            Search
          </label>
          <input
            type="text"
            id="search"
            value={localFilters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Transaction ID, account..."
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          />
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <Button variant="outline" onClick={handleClearFilters}>
          Clear All
        </Button>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleApplyFilters}>
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TransactionFilters;
