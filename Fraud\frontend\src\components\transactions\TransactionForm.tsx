import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { useTransactionStore } from '@/store/transactionStore';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import { formatCurrency, getRiskLevelColor, getFraudDecisionColor } from '@/utils';
import type { TransactionRequest, FraudResult } from '@/types';

interface TransactionFormProps {
  onClose: () => void;
}

const TransactionForm: React.FC<TransactionFormProps> = ({ onClose }) => {
  const [result, setResult] = useState<FraudResult | null>(null);
  const { scoreTransaction, isLoading } = useTransactionStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<TransactionRequest>();

  const onSubmit = async (data: TransactionRequest) => {
    try {
      const fraudResult = await scoreTransaction(data);
      setResult(fraudResult);
      toast.success('Transaction scored successfully!');
    } catch (error) {
      toast.error('Failed to score transaction');
    }
  };

  const handleReset = () => {
    reset();
    setResult(null);
  };

  const handleClose = () => {
    reset();
    setResult(null);
    onClose();
  };

  return (
    <div className="space-y-6">
      {!result ? (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Transaction ID */}
            <div className="sm:col-span-2">
              <label htmlFor="transaction_id" className="block text-sm font-medium text-gray-700">
                Transaction ID (Optional)
              </label>
              <input
                {...register('transaction_id')}
                type="text"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="Auto-generated if not provided"
              />
            </div>

            {/* Step */}
            <div>
              <label htmlFor="step" className="block text-sm font-medium text-gray-700">
                Step *
              </label>
              <input
                {...register('step', {
                  required: 'Step is required',
                  min: { value: 1, message: 'Step must be at least 1' },
                })}
                type="number"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="1"
              />
              {errors.step && (
                <p className="mt-1 text-sm text-red-600">{errors.step.message}</p>
              )}
            </div>

            {/* Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Transaction Type *
              </label>
              <select
                {...register('type', { required: 'Transaction type is required' })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              >
                <option value="">Select type</option>
                <option value="PAYMENT">Payment</option>
                <option value="TRANSFER">Transfer</option>
                <option value="CASH_OUT">Cash Out</option>
                <option value="DEBIT">Debit</option>
                <option value="CASH_IN">Cash In</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            {/* Amount */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                Amount *
              </label>
              <input
                {...register('amount', {
                  required: 'Amount is required',
                  min: { value: 0.01, message: 'Amount must be greater than 0' },
                })}
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="0.00"
              />
              {errors.amount && (
                <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
              )}
            </div>

            {/* Origin Account */}
            <div>
              <label htmlFor="name_orig" className="block text-sm font-medium text-gray-700">
                Origin Account *
              </label>
              <input
                {...register('name_orig', { required: 'Origin account is required' })}
                type="text"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="C123456789"
              />
              {errors.name_orig && (
                <p className="mt-1 text-sm text-red-600">{errors.name_orig.message}</p>
              )}
            </div>

            {/* Origin Old Balance */}
            <div>
              <label htmlFor="oldbalance_orig" className="block text-sm font-medium text-gray-700">
                Origin Old Balance *
              </label>
              <input
                {...register('oldbalance_orig', {
                  required: 'Origin old balance is required',
                  min: { value: 0, message: 'Balance cannot be negative' },
                })}
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="0.00"
              />
              {errors.oldbalance_orig && (
                <p className="mt-1 text-sm text-red-600">{errors.oldbalance_orig.message}</p>
              )}
            </div>

            {/* Origin New Balance */}
            <div>
              <label htmlFor="newbalance_orig" className="block text-sm font-medium text-gray-700">
                Origin New Balance *
              </label>
              <input
                {...register('newbalance_orig', {
                  required: 'Origin new balance is required',
                  min: { value: 0, message: 'Balance cannot be negative' },
                })}
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="0.00"
              />
              {errors.newbalance_orig && (
                <p className="mt-1 text-sm text-red-600">{errors.newbalance_orig.message}</p>
              )}
            </div>

            {/* Destination Account */}
            <div>
              <label htmlFor="name_dest" className="block text-sm font-medium text-gray-700">
                Destination Account *
              </label>
              <input
                {...register('name_dest', { required: 'Destination account is required' })}
                type="text"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="C987654321"
              />
              {errors.name_dest && (
                <p className="mt-1 text-sm text-red-600">{errors.name_dest.message}</p>
              )}
            </div>

            {/* Destination Old Balance */}
            <div>
              <label htmlFor="oldbalance_dest" className="block text-sm font-medium text-gray-700">
                Destination Old Balance *
              </label>
              <input
                {...register('oldbalance_dest', {
                  required: 'Destination old balance is required',
                  min: { value: 0, message: 'Balance cannot be negative' },
                })}
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="0.00"
              />
              {errors.oldbalance_dest && (
                <p className="mt-1 text-sm text-red-600">{errors.oldbalance_dest.message}</p>
              )}
            </div>

            {/* Destination New Balance */}
            <div>
              <label htmlFor="newbalance_dest" className="block text-sm font-medium text-gray-700">
                Destination New Balance *
              </label>
              <input
                {...register('newbalance_dest', {
                  required: 'Destination new balance is required',
                  min: { value: 0, message: 'Balance cannot be negative' },
                })}
                type="number"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="0.00"
              />
              {errors.newbalance_dest && (
                <p className="mt-1 text-sm text-red-600">{errors.newbalance_dest.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" loading={isLoading}>
              Score Transaction
            </Button>
          </div>
        </form>
      ) : (
        <div className="space-y-6">
          <Card>
            <Card.Header>
              <h3 className="text-lg font-medium text-gray-900">Fraud Detection Result</h3>
            </Card.Header>
            <Card.Body>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Fraud Score</dt>
                  <dd className="mt-1 text-2xl font-semibold text-gray-900">
                    {(result.fraud_score * 100).toFixed(1)}%
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Processing Time</dt>
                  <dd className="mt-1 text-lg text-gray-900">
                    {result.processing_time_ms}ms
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Risk Level</dt>
                  <dd className="mt-1">
                    <Badge className={getRiskLevelColor(result.risk_level)}>
                      {result.risk_level}
                    </Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Decision</dt>
                  <dd className="mt-1">
                    <Badge className={getFraudDecisionColor(result.decision)}>
                      {result.decision}
                    </Badge>
                  </dd>
                </div>
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Fraudulent</dt>
                  <dd className="mt-1">
                    <Badge variant={result.is_fraudulent ? 'danger' : 'success'}>
                      {result.is_fraudulent ? 'Yes' : 'No'}
                    </Badge>
                  </dd>
                </div>
                {result.reason_codes.length > 0 && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Reason Codes</dt>
                    <dd className="mt-1">
                      <div className="flex flex-wrap gap-2">
                        {result.reason_codes.map((code, index) => (
                          <Badge key={index} variant="info">
                            {code}
                          </Badge>
                        ))}
                      </div>
                    </dd>
                  </div>
                )}
              </div>
            </Card.Body>
          </Card>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleReset}>
              Score Another
            </Button>
            <Button onClick={handleClose}>
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionForm;
