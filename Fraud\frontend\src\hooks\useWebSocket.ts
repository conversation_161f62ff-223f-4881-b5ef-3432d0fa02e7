import { useEffect, useRef, useCallback } from 'react';
import { wsService } from '@/services/websocket';

type EventCallback = (data: any) => void;

export function useWebSocket() {
  const isConnected = wsService.isConnected();

  const send = useCallback((event: string, data: any) => {
    wsService.send(event, data);
  }, []);

  return {
    isConnected,
    send,
  };
}

export function useWebSocketEvent(event: string, callback: EventCallback) {
  const callbackRef = useRef(callback);
  callbackRef.current = callback;

  useEffect(() => {
    const wrappedCallback = (data: any) => {
      callbackRef.current(data);
    };

    wsService.on(event, wrappedCallback);

    return () => {
      wsService.off(event, wrappedCallback);
    };
  }, [event]);
}

export function useRealTimeTransactions() {
  const { useTransactionStore } = require('@/store/transactionStore');
  const { realtimeTransactions, addRealtimeTransaction, clearRealtimeTransactions } = useTransactionStore();

  useWebSocketEvent('transaction', addRealtimeTransaction);

  return {
    realtimeTransactions,
    clearRealtimeTransactions,
  };
}

export function useRealTimeAlerts() {
  const { useAlertStore } = require('@/store/alertStore');
  const { realtimeAlerts, addRealtimeAlert, clearRealtimeAlerts } = useAlertStore();

  useWebSocketEvent('alert', addRealtimeAlert);

  return {
    realtimeAlerts,
    clearRealtimeAlerts,
  };
}
