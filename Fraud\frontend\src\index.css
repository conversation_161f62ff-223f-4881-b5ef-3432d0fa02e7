@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-primary: 59 130 246;
    --color-primary-foreground: 255 255 255;
    --color-secondary: 156 163 175;
    --color-secondary-foreground: 17 24 39;
    --color-muted: 249 250 251;
    --color-muted-foreground: 107 114 128;
    --color-accent: 243 244 246;
    --color-accent-foreground: 17 24 39;
    --color-destructive: 239 68 68;
    --color-destructive-foreground: 255 255 255;
    --color-border: 229 231 235;
    --color-input: 255 255 255;
    --color-ring: 59 130 246;
    --radius: 0.5rem;
  }

  .dark {
    --color-primary: 59 130 246;
    --color-primary-foreground: 255 255 255;
    --color-secondary: 75 85 99;
    --color-secondary-foreground: 243 244 246;
    --color-muted: 31 41 55;
    --color-muted-foreground: 156 163 175;
    --color-accent: 55 65 81;
    --color-accent-foreground: 243 244 246;
    --color-destructive: 239 68 68;
    --color-destructive-foreground: 255 255 255;
    --color-border: 75 85 99;
    --color-input: 31 41 55;
    --color-ring: 59 130 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Fade in animation */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Slide in animation */
  .slide-in {
    animation: slideIn 0.3s ease-out;
  }

  @keyframes slideIn {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  /* Pulse animation for real-time updates */
  .pulse-update {
    animation: pulseUpdate 1s ease-in-out;
  }

  @keyframes pulseUpdate {
    0% {
      background-color: rgb(59 130 246 / 0.1);
    }
    50% {
      background-color: rgb(59 130 246 / 0.2);
    }
    100% {
      background-color: transparent;
    }
  }

  /* Status indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-success {
    @apply bg-green-100 text-green-800;
  }

  .status-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-danger {
    @apply bg-red-100 text-red-800;
  }

  .status-info {
    @apply bg-blue-100 text-blue-800;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-ring disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  /* Form styles */
  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply text-sm text-red-600 mt-1;
  }

  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-row {
    @apply hover:bg-gray-50;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Notification styles */
  .notification {
    @apply fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5;
  }

  .notification-success {
    @apply border-l-4 border-green-400;
  }

  .notification-error {
    @apply border-l-4 border-red-400;
  }

  .notification-warning {
    @apply border-l-4 border-yellow-400;
  }

  .notification-info {
    @apply border-l-4 border-blue-400;
  }
}

@layer components {
  /* Custom component styles can be added here */
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }
}
