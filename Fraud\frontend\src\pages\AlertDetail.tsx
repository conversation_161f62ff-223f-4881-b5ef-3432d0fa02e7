import React, { useEffect, useState } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useAlertStore } from '@/store/alertStore';
import { useTransactionStore } from '@/store/transactionStore';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import Modal from '@/components/ui/Modal';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { toast } from 'react-hot-toast';
import {
  formatDateTime,
  formatRelativeTime,
  getAlertSeverityColor,
  getAlertStatusColor,
} from '@/utils';

const AlertDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');
  
  const {
    currentAlert,
    isLoading: alertLoading,
    error: alertError,
    fetchAlert,
    updateAlertStatus,
  } = useAlertStore();

  const {
    currentTransaction,
    fetchTransaction,
  } = useTransactionStore();

  useEffect(() => {
    if (id) {
      fetchAlert(id);
    }
  }, [id, fetchAlert]);

  useEffect(() => {
    if (currentAlert?.transaction_id) {
      fetchTransaction(currentAlert.transaction_id);
    }
  }, [currentAlert?.transaction_id, fetchTransaction]);

  const handleStatusUpdate = async () => {
    if (!currentAlert || !selectedStatus) return;

    try {
      await updateAlertStatus(currentAlert.id, selectedStatus, resolutionNotes);
      toast.success(`Alert ${selectedStatus.toLowerCase()} successfully`);
      setShowStatusModal(false);
      setResolutionNotes('');
    } catch (error) {
      toast.error('Failed to update alert status');
    }
  };

  const openStatusModal = (status: string) => {
    setSelectedStatus(status);
    setShowStatusModal(true);
  };

  if (alertLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (alertError || !currentAlert) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h2 className="text-2xl font-bold text-gray-900">Alert Not Found</h2>
        </div>
        
        <Card>
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Alert not found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {alertError || 'The requested alert could not be found.'}
            </p>
            <div className="mt-6">
              <Link to="/alerts">
                <Button>View All Alerts</Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Alert Details</h2>
            <p className="text-sm text-gray-500">ID: {currentAlert.id}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Badge className={getAlertSeverityColor(currentAlert.severity)}>
            {currentAlert.severity}
          </Badge>
          <Badge className={getAlertStatusColor(currentAlert.status)}>
            {currentAlert.status.replace('_', ' ')}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alert Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <Card.Header>
              <h3 className="text-lg font-medium text-gray-900">Alert Information</h3>
            </Card.Header>
            <Card.Body>
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Title</dt>
                  <dd className="mt-1 text-lg font-medium text-gray-900">{currentAlert.title}</dd>
                </div>
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900">{currentAlert.description}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Alert Type</dt>
                  <dd className="mt-1">
                    <Badge>{currentAlert.alert_type.replace('_', ' ')}</Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Severity</dt>
                  <dd className="mt-1">
                    <Badge className={getAlertSeverityColor(currentAlert.severity)}>
                      {currentAlert.severity}
                    </Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1">
                    <Badge className={getAlertStatusColor(currentAlert.status)}>
                      {currentAlert.status.replace('_', ' ')}
                    </Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Transaction ID</dt>
                  <dd className="mt-1">
                    <Link
                      to={`/transactions/${currentAlert.transaction_id}`}
                      className="text-sm text-primary-600 hover:text-primary-500"
                    >
                      {currentAlert.transaction_id}
                    </Link>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {formatDateTime(currentAlert.created_at)}
                    <div className="text-xs text-gray-500">
                      {formatRelativeTime(currentAlert.created_at)}
                    </div>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {formatDateTime(currentAlert.updated_at)}
                    <div className="text-xs text-gray-500">
                      {formatRelativeTime(currentAlert.updated_at)}
                    </div>
                  </dd>
                </div>
                {currentAlert.resolved_at && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Resolved</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {formatDateTime(currentAlert.resolved_at)}
                      <div className="text-xs text-gray-500">
                        {formatRelativeTime(currentAlert.resolved_at)}
                      </div>
                    </dd>
                  </div>
                )}
                {currentAlert.resolved_by && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Resolved By</dt>
                    <dd className="mt-1 text-sm text-gray-900">{currentAlert.resolved_by}</dd>
                  </div>
                )}
                {currentAlert.resolution_notes && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Resolution Notes</dt>
                    <dd className="mt-1 text-sm text-gray-900">{currentAlert.resolution_notes}</dd>
                  </div>
                )}
              </dl>
            </Card.Body>
          </Card>

          {/* Metadata */}
          {currentAlert.metadata && Object.keys(currentAlert.metadata).length > 0 && (
            <Card>
              <Card.Header>
                <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
              </Card.Header>
              <Card.Body>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  {Object.entries(currentAlert.metadata).map(([key, value]) => (
                    <div key={key}>
                      <dt className="text-sm font-medium text-gray-500 capitalize">
                        {key.replace('_', ' ')}
                      </dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                      </dd>
                    </div>
                  ))}
                </dl>
              </Card.Body>
            </Card>
          )}
        </div>

        {/* Actions & Related Transaction */}
        <div className="space-y-6">
          {/* Actions */}
          {currentAlert.status === 'OPEN' && (
            <Card>
              <Card.Header>
                <h3 className="text-lg font-medium text-gray-900">Actions</h3>
              </Card.Header>
              <Card.Body>
                <div className="space-y-3">
                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={() => openStatusModal('RESOLVED')}
                  >
                    Mark as Resolved
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => openStatusModal('IN_PROGRESS')}
                  >
                    Mark In Progress
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => openStatusModal('FALSE_POSITIVE')}
                  >
                    Mark as False Positive
                  </Button>
                </div>
              </Card.Body>
            </Card>
          )}

          {/* Related Transaction */}
          {currentTransaction && (
            <Card>
              <Card.Header>
                <h3 className="text-lg font-medium text-gray-900">Related Transaction</h3>
              </Card.Header>
              <Card.Body>
                <div className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Transaction ID</dt>
                    <dd className="mt-1 text-sm text-gray-900 font-mono">
                      {currentTransaction.transaction_id}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Type</dt>
                    <dd className="mt-1">
                      <Badge>{currentTransaction.type}</Badge>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Amount</dt>
                    <dd className="mt-1 text-lg font-semibold text-gray-900">
                      ${currentTransaction.amount.toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Parties</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <div className="truncate">{currentTransaction.name_orig}</div>
                      <div className="text-gray-500 text-xs">↓</div>
                      <div className="truncate">{currentTransaction.name_dest}</div>
                    </dd>
                  </div>
                  <div className="pt-3">
                    <Link to={`/transactions/${currentTransaction.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        View Transaction Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </Card.Body>
            </Card>
          )}
        </div>
      </div>

      {/* Status Update Modal */}
      <Modal
        isOpen={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        title={`Update Alert Status`}
        description={`Change alert status to ${selectedStatus.replace('_', ' ').toLowerCase()}`}
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
              Resolution Notes {selectedStatus === 'RESOLVED' && '(Required)'}
            </label>
            <textarea
              id="notes"
              rows={4}
              value={resolutionNotes}
              onChange={(e) => setResolutionNotes(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              placeholder="Add notes about the resolution..."
              required={selectedStatus === 'RESOLVED'}
            />
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowStatusModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleStatusUpdate}
              disabled={selectedStatus === 'RESOLVED' && !resolutionNotes.trim()}
            >
              Update Status
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AlertDetail;
