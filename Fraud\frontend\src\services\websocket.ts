import { io, Socket } from 'socket.io-client';
import { toast } from 'react-hot-toast';
import type { WebSocketMessage, RealTimeTransaction, RealTimeAlert } from '@/types';

type EventCallback = (data: any) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventCallbacks: Map<string, EventCallback[]> = new Map();

  constructor() {
    this.connect();
  }

  private connect() {
    if (this.isConnecting || this.socket?.connected) {
      return;
    }

    this.isConnecting = true;
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';
    
    try {
      this.socket = io(wsUrl, {
        transports: ['websocket'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        timeout: 10000,
        auth: {
          token: localStorage.getItem('access_token'),
        },
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      // Join relevant rooms
      this.joinRoom('transactions');
      this.joinRoom('alerts');
      this.joinRoom('dashboard');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnecting = false;
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      this.handleConnectionError();
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`WebSocket reconnected after ${attemptNumber} attempts`);
      toast.success('Connection restored');
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('WebSocket reconnection error:', error);
      this.handleReconnectionError();
    });

    this.socket.on('reconnect_failed', () => {
      console.error('WebSocket reconnection failed');
      toast.error('Unable to establish real-time connection');
    });

    // Handle incoming messages
    this.socket.on('message', (message: WebSocketMessage) => {
      this.handleMessage(message);
    });

    // Specific event handlers
    this.socket.on('transaction_processed', (data: RealTimeTransaction) => {
      this.emit('transaction', data);
    });

    this.socket.on('alert_created', (data: RealTimeAlert) => {
      this.emit('alert', data);
      
      // Show toast notification for high-severity alerts
      if (data.alert.severity === 'HIGH' || data.alert.severity === 'CRITICAL') {
        toast.error(`${data.alert.severity} Alert: ${data.alert.title}`, {
          duration: 5000,
        });
      }
    });

    this.socket.on('stats_updated', (data: any) => {
      this.emit('stats', data);
    });

    this.socket.on('system_notification', (data: any) => {
      this.emit('system', data);
      
      if (data.type === 'error') {
        toast.error(data.message);
      } else if (data.type === 'warning') {
        toast.error(data.message, { icon: '⚠️' });
      } else if (data.type === 'info') {
        toast(data.message, { icon: 'ℹ️' });
      }
    });
  }

  private handleMessage(message: WebSocketMessage) {
    console.log('WebSocket message received:', message);
    
    try {
      switch (message.type) {
        case 'transaction':
          this.emit('transaction', message.data);
          break;
        case 'alert':
          this.emit('alert', message.data);
          break;
        case 'stats':
          this.emit('stats', message.data);
          break;
        case 'system':
          this.emit('system', message.data);
          break;
        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }

  private handleConnectionError() {
    this.reconnectAttempts++;
    
    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      toast.error('Unable to connect to real-time services');
    }
  }

  private handleReconnectionError() {
    this.reconnectAttempts++;
    
    if (this.reconnectAttempts > this.maxReconnectAttempts) {
      toast.error('Real-time connection lost. Please refresh the page.');
    }
  }

  private reconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay);
  }

  private joinRoom(room: string) {
    if (this.socket?.connected) {
      this.socket.emit('join_room', room);
    }
  }

  private leaveRoom(room: string) {
    if (this.socket?.connected) {
      this.socket.emit('leave_room', room);
    }
  }

  // Public methods
  public on(event: string, callback: EventCallback) {
    if (!this.eventCallbacks.has(event)) {
      this.eventCallbacks.set(event, []);
    }
    this.eventCallbacks.get(event)!.push(callback);
  }

  public off(event: string, callback?: EventCallback) {
    if (!callback) {
      this.eventCallbacks.delete(event);
      return;
    }

    const callbacks = this.eventCallbacks.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any) {
    const callbacks = this.eventCallbacks.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event callback for ${event}:`, error);
        }
      });
    }
  }

  public send(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventCallbacks.clear();
  }

  public updateAuth(token: string) {
    if (this.socket) {
      this.socket.auth = { token };
      this.socket.disconnect();
      this.socket.connect();
    }
  }
}

// Create and export singleton instance
export const wsService = new WebSocketService();
export default wsService;
