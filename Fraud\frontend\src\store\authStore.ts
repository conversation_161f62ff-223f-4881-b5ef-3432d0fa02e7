import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/services/api';
import { wsService } from '@/services/websocket';
import type { User, LoginCredentials, RegisterData, AuthTokens } from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const tokens: AuthTokens = await apiClient.login(credentials);
          
          // Get user information
          const user = await apiClient.getCurrentUser();
          
          // Update WebSocket auth
          wsService.updateAuth(tokens.access_token);
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.detail || error.message || 'Login failed',
          });
          throw error;
        }
      },

      register: async (data: RegisterData) => {
        set({ isLoading: true, error: null });
        
        try {
          const user = await apiClient.register(data);
          
          set({
            user,
            isAuthenticated: false, // User needs to login after registration
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.detail || error.message || 'Registration failed',
          });
          throw error;
        }
      },

      logout: () => {
        apiClient.logout();
        wsService.disconnect();
        
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      getCurrentUser: async () => {
        if (!apiClient.isAuthenticated()) {
          return;
        }

        set({ isLoading: true, error: null });
        
        try {
          const user = await apiClient.getCurrentUser();
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          // If getting current user fails, user is likely not authenticated
          get().logout();
          
          set({
            isLoading: false,
            error: error.response?.data?.detail || error.message || 'Authentication failed',
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setUser: (user: User) => {
        set({ user });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Initialize auth state on app start
export const initializeAuth = async () => {
  const { getCurrentUser, logout } = useAuthStore.getState();
  
  if (apiClient.isAuthenticated()) {
    try {
      await getCurrentUser();
    } catch (error) {
      console.error('Failed to initialize auth:', error);
      logout();
    }
  }
};
