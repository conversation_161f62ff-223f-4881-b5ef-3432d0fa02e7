import { create } from 'zustand';
import { apiClient } from '@/services/api';
import { wsService } from '@/services/websocket';
import type { DashboardStats, TransactionStats, ChartDataPoint } from '@/types';

interface DashboardState {
  stats: DashboardStats | null;
  transactionStats: TransactionStats[];
  fraudTrends: ChartDataPoint[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  autoRefresh: boolean;
  refreshInterval: number; // in seconds

  // Actions
  fetchDashboardStats: (period?: string) => Promise<void>;
  fetchTransactionStats: (params?: any) => Promise<void>;
  fetchFraudTrends: (params?: any) => Promise<void>;
  refreshAll: () => Promise<void>;
  setAutoRefresh: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  clearError: () => void;
  updateStats: (stats: DashboardStats) => void;
}

export const useDashboardStore = create<DashboardState>((set, get) => ({
  stats: null,
  transactionStats: [],
  fraudTrends: [],
  isLoading: false,
  error: null,
  lastUpdated: null,
  autoRefresh: true,
  refreshInterval: 30, // 30 seconds

  fetchDashboardStats: async (period?: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const stats = await apiClient.getDashboardStats(period);
      
      set({
        stats,
        isLoading: false,
        error: null,
        lastUpdated: new Date(),
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to fetch dashboard stats',
      });
    }
  },

  fetchTransactionStats: async (params?: any) => {
    try {
      const transactionStats = await apiClient.getTransactionStats(params);
      
      set({
        transactionStats,
        error: null,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || error.message || 'Failed to fetch transaction stats',
      });
    }
  },

  fetchFraudTrends: async (params?: any) => {
    try {
      const fraudTrends = await apiClient.getFraudTrends(params);
      
      set({
        fraudTrends,
        error: null,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || error.message || 'Failed to fetch fraud trends',
      });
    }
  },

  refreshAll: async () => {
    const { fetchDashboardStats, fetchTransactionStats, fetchFraudTrends } = get();
    
    await Promise.all([
      fetchDashboardStats(),
      fetchTransactionStats(),
      fetchFraudTrends(),
    ]);
  },

  setAutoRefresh: (enabled: boolean) => {
    set({ autoRefresh: enabled });
  },

  setRefreshInterval: (interval: number) => {
    set({ refreshInterval: interval });
  },

  clearError: () => {
    set({ error: null });
  },

  updateStats: (stats: DashboardStats) => {
    set({ stats, lastUpdated: new Date() });
  },
}));

// Set up real-time stats updates
wsService.on('stats', (data: DashboardStats) => {
  useDashboardStore.getState().updateStats(data);
});

// Auto-refresh functionality
let refreshTimer: NodeJS.Timeout | null = null;

export const startAutoRefresh = () => {
  const { autoRefresh, refreshInterval, refreshAll } = useDashboardStore.getState();
  
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  
  if (autoRefresh) {
    refreshTimer = setInterval(() => {
      refreshAll();
    }, refreshInterval * 1000);
  }
};

export const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// Subscribe to auto-refresh changes
useDashboardStore.subscribe((state) => {
  if (state.autoRefresh) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
});
