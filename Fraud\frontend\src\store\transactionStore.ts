import { create } from 'zustand';
import { apiClient } from '@/services/api';
import { wsService } from '@/services/websocket';
import type { 
  Transaction, 
  TransactionRequest, 
  FraudResult, 
  PaginatedResponse,
  RealTimeTransaction 
} from '@/types';

interface TransactionState {
  transactions: Transaction[];
  currentTransaction: Transaction | null;
  realtimeTransactions: RealTimeTransaction[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  isLoading: boolean;
  error: string | null;
  filters: {
    type?: string;
    risk_level?: string;
    start_date?: string;
    end_date?: string;
  };

  // Actions
  fetchTransactions: (params?: any) => Promise<void>;
  fetchTransaction: (id: string) => Promise<void>;
  scoreTransaction: (transaction: TransactionRequest) => Promise<FraudResult>;
  batchScoreTransactions: (transactions: TransactionRequest[]) => Promise<FraudResult[]>;
  setFilters: (filters: any) => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  clearError: () => void;
  addRealtimeTransaction: (transaction: RealTimeTransaction) => void;
  clearRealtimeTransactions: () => void;
}

export const useTransactionStore = create<TransactionState>((set, get) => ({
  transactions: [],
  currentTransaction: null,
  realtimeTransactions: [],
  totalCount: 0,
  currentPage: 1,
  pageSize: 20,
  isLoading: false,
  error: null,
  filters: {},

  fetchTransactions: async (params?: any) => {
    set({ isLoading: true, error: null });
    
    try {
      const { currentPage, pageSize, filters } = get();
      const queryParams = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
        ...filters,
        ...params,
      };

      const response: PaginatedResponse<Transaction> = await apiClient.getTransactions(queryParams);
      
      set({
        transactions: response.data,
        totalCount: response.total,
        currentPage: response.page,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to fetch transactions',
      });
    }
  },

  fetchTransaction: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const transaction = await apiClient.getTransaction(id);
      
      set({
        currentTransaction: transaction,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to fetch transaction',
      });
    }
  },

  scoreTransaction: async (transaction: TransactionRequest) => {
    set({ isLoading: true, error: null });
    
    try {
      const result = await apiClient.scoreTransaction(transaction);
      
      set({
        isLoading: false,
        error: null,
      });
      
      return result;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to score transaction',
      });
      throw error;
    }
  },

  batchScoreTransactions: async (transactions: TransactionRequest[]) => {
    set({ isLoading: true, error: null });
    
    try {
      const results = await apiClient.batchScoreTransactions(transactions);
      
      set({
        isLoading: false,
        error: null,
      });
      
      return results;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.detail || error.message || 'Failed to score transactions',
      });
      throw error;
    }
  },

  setFilters: (filters: any) => {
    set({ filters, currentPage: 1 });
    get().fetchTransactions();
  },

  setPage: (page: number) => {
    set({ currentPage: page });
    get().fetchTransactions();
  },

  setPageSize: (size: number) => {
    set({ pageSize: size, currentPage: 1 });
    get().fetchTransactions();
  },

  clearError: () => {
    set({ error: null });
  },

  addRealtimeTransaction: (transaction: RealTimeTransaction) => {
    set((state) => ({
      realtimeTransactions: [transaction, ...state.realtimeTransactions.slice(0, 49)], // Keep last 50
    }));
  },

  clearRealtimeTransactions: () => {
    set({ realtimeTransactions: [] });
  },
}));

// Set up real-time transaction updates
wsService.on('transaction', (data: RealTimeTransaction) => {
  useTransactionStore.getState().addRealtimeTransaction(data);
});
