# Default values for fraudshield
# This is a YAML-formatted file.

global:
  imageRegistry: ghcr.io
  imagePullSecrets:
    - name: ghcr-secret
  storageClass: "fast-ssd"

# Application configuration
app:
  name: fraudshield
  version: "1.0.0"
  environment: production

# Namespace configuration
namespace:
  create: true
  name: fraudshield
  labels:
    app.kubernetes.io/name: fraudshield
    app.kubernetes.io/version: "1.0.0"

# Backend service configuration
backend:
  enabled: true
  replicaCount: 3
  image:
    repository: fraudshield/backend
    tag: "latest"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
  
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: letsencrypt-prod
    hosts:
      - host: api.fraudshield.example.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: backend-tls
        hosts:
          - api.fraudshield.example.com
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2
      memory: 4Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    - name: DATABASE_URL
      valueFrom:
        secretKeyRef:
          name: database-secrets
          key: url
    - name: REDIS_URL
      valueFrom:
        secretKeyRef:
          name: redis-secrets
          key: url
    - name: KAFKA_BOOTSTRAP_SERVERS
      value: "kafka:9092"
    - name: ENVIRONMENT
      value: "production"

# ML Service configuration
mlService:
  enabled: true
  replicaCount: 3
  image:
    repository: fraudshield/ml-service
    tag: "latest"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
  
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 4
      memory: 8Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 15
    targetCPUUtilizationPercentage: 70
    metrics:
      - type: Pods
        pods:
          metric:
            name: ml_requests_per_second
          target:
            type: AverageValue
            averageValue: "100"
  
  env:
    - name: MLFLOW_TRACKING_URI
      valueFrom:
        secretKeyRef:
          name: mlflow-secrets
          key: tracking-uri
    - name: MODEL_NAME
      value: "fraud-detection"
    - name: MODEL_STAGE
      value: "Production"

# Frontend configuration
frontend:
  enabled: true
  replicaCount: 2
  image:
    repository: fraudshield/frontend
    tag: "latest"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
  
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      cert-manager.io/cluster-issuer: letsencrypt-prod
    hosts:
      - host: fraudshield.example.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: frontend-tls
        hosts:
          - fraudshield.example.com
  
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi

# Stream Processor configuration
streamProcessor:
  enabled: true
  replicaCount: 2
  image:
    repository: fraudshield/stream-processor
    tag: "latest"
    pullPolicy: Always
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2
      memory: 4Gi
  
  env:
    - name: KAFKA_BOOTSTRAP_SERVERS
      value: "kafka:9092"
    - name: REDIS_URL
      valueFrom:
        secretKeyRef:
          name: redis-secrets
          key: url

# Feature Store configuration
featureStore:
  enabled: true
  replicaCount: 2
  image:
    repository: fraudshield/feature-store
    tag: "latest"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2
      memory: 4Gi

# Data Quality Monitor configuration
dataQualityMonitor:
  enabled: true
  replicaCount: 1
  image:
    repository: fraudshield/data-quality-monitor
    tag: "latest"
    pullPolicy: Always
  
  resources:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      cpu: 1
      memory: 2Gi
  
  schedule: "0 */6 * * *"  # Run every 6 hours

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "fraudshield-postgres-password"
    username: "fraudshield"
    password: "fraudshield-password"
    database: "fraudshield"
  
  primary:
    persistence:
      enabled: true
      size: 100Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 4
        memory: 8Gi
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
    password: "fraudshield-redis-password"
  
  master:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 4Gi
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Kafka configuration
kafka:
  enabled: true
  replicaCount: 3
  
  persistence:
    enabled: true
    size: 50Gi
    storageClass: "fast-ssd"
  
  resources:
    requests:
      cpu: 1
      memory: 2Gi
    limits:
      cpu: 4
      memory: 8Gi
  
  metrics:
    kafka:
      enabled: true
    jmx:
      enabled: true

# Elasticsearch configuration
elasticsearch:
  enabled: true
  master:
    replicaCount: 3
    persistence:
      enabled: true
      size: 50Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 4
        memory: 8Gi
  
  data:
    replicaCount: 3
    persistence:
      enabled: true
      size: 100Gi
      storageClass: "fast-ssd"

# Prometheus configuration
prometheus:
  enabled: true
  server:
    persistentVolume:
      enabled: true
      size: 50Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 4Gi
  
  alertmanager:
    enabled: true
    persistentVolume:
      enabled: true
      size: 10Gi

# Grafana configuration
grafana:
  enabled: true
  persistence:
    enabled: true
    size: 10Gi
    storageClass: "fast-ssd"
  
  adminPassword: "fraudshield-grafana-password"
  
  resources:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      cpu: 1
      memory: 2Gi

# MLflow configuration
mlflow:
  enabled: true
  tracking:
    replicaCount: 2
    persistence:
      enabled: true
      size: 20Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 4Gi
  
  artifactStore:
    type: s3
    s3:
      bucket: fraudshield-mlflow-artifacts
      region: us-east-1

# Security configuration
security:
  networkPolicies:
    enabled: true
  
  podSecurityPolicy:
    enabled: true
  
  rbac:
    create: true
  
  serviceAccount:
    create: true
    name: fraudshield

# Monitoring configuration
monitoring:
  enabled: true
  
  serviceMonitor:
    enabled: true
    interval: 30s
  
  prometheusRule:
    enabled: true
  
  grafanaDashboards:
    enabled: true

# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: "30d"
  
  postgresql:
    enabled: true
  
  elasticsearch:
    enabled: true

# Resource quotas
resourceQuota:
  enabled: true
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "20"

# Limit ranges
limitRange:
  enabled: true
  limits:
    - default:
        cpu: 500m
        memory: 1Gi
      defaultRequest:
        cpu: 100m
        memory: 256Mi
      type: Container
