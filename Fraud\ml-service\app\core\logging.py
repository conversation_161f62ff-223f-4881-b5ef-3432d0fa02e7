"""
Logging configuration for ML service
"""

import sys
import logging
from typing import Dict, Any
import structlog
from pythonjsonlogger import jsonlogger

from .config import settings


def setup_logging() -> None:
    """Setup structured logging with JSON formatter"""
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=logging.INFO if not settings.DEBUG else logging.DEBUG,
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance"""
    return structlog.get_logger(name)


class MLMetricsLogger:
    """Logger for ML-specific metrics and events"""
    
    def __init__(self):
        self.logger = get_logger("ml_metrics")
    
    def log_prediction(
        self,
        transaction_id: str,
        model_version: str,
        fraud_score: float,
        is_fraudulent: bool,
        processing_time_ms: float,
        features: Dict[str, Any] = None
    ):
        """Log prediction details"""
        self.logger.info(
            "prediction_made",
            transaction_id=transaction_id,
            model_version=model_version,
            fraud_score=fraud_score,
            is_fraudulent=is_fraudulent,
            processing_time_ms=processing_time_ms,
            feature_count=len(features) if features else 0
        )
    
    def log_model_performance(
        self,
        model_name: str,
        model_version: str,
        metrics: Dict[str, float],
        dataset_size: int
    ):
        """Log model performance metrics"""
        self.logger.info(
            "model_performance",
            model_name=model_name,
            model_version=model_version,
            metrics=metrics,
            dataset_size=dataset_size
        )
    
    def log_feature_importance(
        self,
        model_name: str,
        feature_importance: Dict[str, float]
    ):
        """Log feature importance scores"""
        self.logger.info(
            "feature_importance",
            model_name=model_name,
            feature_importance=feature_importance
        )
    
    def log_data_drift(
        self,
        feature_name: str,
        drift_score: float,
        threshold: float,
        is_drift_detected: bool
    ):
        """Log data drift detection results"""
        self.logger.warning(
            "data_drift_detected" if is_drift_detected else "data_drift_check",
            feature_name=feature_name,
            drift_score=drift_score,
            threshold=threshold,
            is_drift_detected=is_drift_detected
        )
    
    def log_model_training(
        self,
        model_name: str,
        training_time_seconds: float,
        training_samples: int,
        validation_score: float
    ):
        """Log model training completion"""
        self.logger.info(
            "model_training_completed",
            model_name=model_name,
            training_time_seconds=training_time_seconds,
            training_samples=training_samples,
            validation_score=validation_score
        )
    
    def log_error(
        self,
        error_type: str,
        error_message: str,
        context: Dict[str, Any] = None
    ):
        """Log ML service errors"""
        self.logger.error(
            "ml_service_error",
            error_type=error_type,
            error_message=error_message,
            context=context or {}
        )
