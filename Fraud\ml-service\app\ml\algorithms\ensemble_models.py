"""
Ensemble models for fraud detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import xgboost as xgb
import lightgbm as lgb
import joblib
import mlflow
import mlflow.sklearn
import mlflow.xgboost
import mlflow.lightgbm

from ...core.config import settings
from ...core.logging import get_logger
from ...core.exceptions import ModelTrainingException, ModelLoadException

logger = get_logger(__name__)


class EnsembleModelManager:
    """Manager for ensemble fraud detection models"""
    
    def __init__(self):
        self.models = {}
        self.model_weights = None
        self.feature_names = None
        self.is_trained = False
    
    def create_random_forest(self, **kwargs) -> RandomForestClassifier:
        """Create Random Forest model with optimized parameters for fraud detection"""
        default_params = {
            'n_estimators': 200,
            'max_depth': 15,
            'min_samples_split': 10,
            'min_samples_leaf': 5,
            'max_features': 'sqrt',
            'bootstrap': True,
            'oob_score': True,
            'random_state': 42,
            'n_jobs': -1,
            'class_weight': 'balanced'  # Handle imbalanced data
        }
        default_params.update(kwargs)
        return RandomForestClassifier(**default_params)
    
    def create_xgboost(self, **kwargs) -> xgb.XGBClassifier:
        """Create XGBoost model with optimized parameters for fraud detection"""
        default_params = {
            'n_estimators': 200,
            'max_depth': 8,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': 42,
            'n_jobs': -1,
            'eval_metric': 'auc',
            'scale_pos_weight': 10  # Handle imbalanced data
        }
        default_params.update(kwargs)
        return xgb.XGBClassifier(**default_params)
    
    def create_lightgbm(self, **kwargs) -> lgb.LGBMClassifier:
        """Create LightGBM model with optimized parameters for fraud detection"""
        default_params = {
            'n_estimators': 200,
            'max_depth': 8,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': 42,
            'n_jobs': -1,
            'metric': 'auc',
            'class_weight': 'balanced',
            'verbose': -1
        }
        default_params.update(kwargs)
        return lgb.LGBMClassifier(**default_params)
    
    def create_gradient_boosting(self, **kwargs) -> GradientBoostingClassifier:
        """Create Gradient Boosting model with optimized parameters"""
        default_params = {
            'n_estimators': 200,
            'max_depth': 8,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'max_features': 'sqrt',
            'random_state': 42,
            'validation_fraction': 0.1,
            'n_iter_no_change': 10
        }
        default_params.update(kwargs)
        return GradientBoostingClassifier(**default_params)
    
    def train_ensemble(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None,
        model_types: List[str] = None
    ) -> Dict[str, Any]:
        """Train ensemble of models"""
        
        if model_types is None:
            model_types = settings.ENSEMBLE_MODELS
        
        logger.info("Starting ensemble training", model_types=model_types)
        
        # Store feature names
        self.feature_names = list(X_train.columns)
        
        # Initialize models
        model_creators = {
            'random_forest': self.create_random_forest,
            'xgboost': self.create_xgboost,
            'lightgbm': self.create_lightgbm,
            'gradient_boosting': self.create_gradient_boosting
        }
        
        training_results = {}
        
        with mlflow.start_run(run_name="ensemble_training"):
            mlflow.log_param("model_types", model_types)
            mlflow.log_param("training_samples", len(X_train))
            mlflow.log_param("feature_count", len(self.feature_names))
            
            for model_name in model_types:
                if model_name not in model_creators:
                    logger.warning(f"Unknown model type: {model_name}")
                    continue
                
                logger.info(f"Training {model_name}")
                
                try:
                    # Create and train model
                    model = model_creators[model_name]()
                    
                    # Train with validation set if provided
                    if X_val is not None and y_val is not None and model_name in ['xgboost', 'lightgbm']:
                        if model_name == 'xgboost':
                            model.fit(
                                X_train, y_train,
                                eval_set=[(X_val, y_val)],
                                early_stopping_rounds=10,
                                verbose=False
                            )
                        elif model_name == 'lightgbm':
                            model.fit(
                                X_train, y_train,
                                eval_set=[(X_val, y_val)],
                                early_stopping_rounds=10,
                                verbose=False
                            )
                    else:
                        model.fit(X_train, y_train)
                    
                    # Evaluate model
                    train_score = model.score(X_train, y_train)
                    val_score = model.score(X_val, y_val) if X_val is not None else None
                    
                    # Cross-validation
                    cv_scores = cross_val_score(
                        model, X_train, y_train,
                        cv=StratifiedKFold(n_splits=5, shuffle=True, random_state=42),
                        scoring='roc_auc'
                    )
                    
                    # Store model and results
                    self.models[model_name] = model
                    training_results[model_name] = {
                        'train_score': train_score,
                        'val_score': val_score,
                        'cv_mean': cv_scores.mean(),
                        'cv_std': cv_scores.std()
                    }
                    
                    # Log to MLflow
                    with mlflow.start_run(run_name=f"{model_name}_training", nested=True):
                        mlflow.log_param("model_type", model_name)
                        mlflow.log_metric("train_accuracy", train_score)
                        if val_score:
                            mlflow.log_metric("val_accuracy", val_score)
                        mlflow.log_metric("cv_auc_mean", cv_scores.mean())
                        mlflow.log_metric("cv_auc_std", cv_scores.std())
                        
                        # Log model
                        if model_name == 'xgboost':
                            mlflow.xgboost.log_model(model, f"{model_name}_model")
                        elif model_name == 'lightgbm':
                            mlflow.lightgbm.log_model(model, f"{model_name}_model")
                        else:
                            mlflow.sklearn.log_model(model, f"{model_name}_model")
                    
                    logger.info(
                        f"{model_name} training completed",
                        train_score=train_score,
                        val_score=val_score,
                        cv_mean=cv_scores.mean()
                    )
                    
                except Exception as e:
                    logger.error(f"Failed to train {model_name}", error=str(e))
                    raise ModelTrainingException(f"Failed to train {model_name}: {str(e)}")
            
            # Calculate ensemble weights based on validation performance
            self._calculate_ensemble_weights(training_results)
            
            # Log ensemble metrics
            mlflow.log_param("ensemble_weights", self.model_weights)
            
        self.is_trained = True
        logger.info("Ensemble training completed", results=training_results)
        
        return training_results
    
    def _calculate_ensemble_weights(self, training_results: Dict[str, Any]):
        """Calculate ensemble weights based on model performance"""
        if settings.ENSEMBLE_WEIGHTS:
            # Use predefined weights
            self.model_weights = dict(zip(self.models.keys(), settings.ENSEMBLE_WEIGHTS))
        else:
            # Calculate weights based on cross-validation AUC scores
            scores = {name: results['cv_mean'] for name, results in training_results.items()}
            total_score = sum(scores.values())
            self.model_weights = {name: score / total_score for name, score in scores.items()}
        
        logger.info("Ensemble weights calculated", weights=self.model_weights)
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Make ensemble predictions"""
        if not self.is_trained or not self.models:
            raise ModelLoadException("Ensemble models not trained or loaded")
        
        # Get predictions from each model
        predictions = []
        weights = []
        
        for model_name, model in self.models.items():
            try:
                pred_proba = model.predict_proba(X)[:, 1]  # Probability of fraud class
                predictions.append(pred_proba)
                weights.append(self.model_weights.get(model_name, 1.0))
            except Exception as e:
                logger.warning(f"Failed to get prediction from {model_name}", error=str(e))
        
        if not predictions:
            raise ModelLoadException("No models available for prediction")
        
        # Weighted average of predictions
        predictions = np.array(predictions)
        weights = np.array(weights)
        weights = weights / weights.sum()  # Normalize weights
        
        ensemble_pred = np.average(predictions, axis=0, weights=weights)
        
        return ensemble_pred
    
    def predict(self, X: pd.DataFrame, threshold: float = None) -> np.ndarray:
        """Make binary predictions"""
        if threshold is None:
            threshold = settings.FRAUD_THRESHOLD
        
        probabilities = self.predict_proba(X)
        return (probabilities >= threshold).astype(int)
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get aggregated feature importance from ensemble"""
        if not self.models:
            return {}
        
        importance_dict = {}
        
        for model_name, model in self.models.items():
            try:
                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                elif hasattr(model, 'coef_'):
                    importances = np.abs(model.coef_[0])
                else:
                    continue
                
                weight = self.model_weights.get(model_name, 1.0)
                
                for i, feature_name in enumerate(self.feature_names):
                    if feature_name not in importance_dict:
                        importance_dict[feature_name] = 0
                    importance_dict[feature_name] += importances[i] * weight
                    
            except Exception as e:
                logger.warning(f"Failed to get feature importance from {model_name}", error=str(e))
        
        # Normalize importance scores
        if importance_dict:
            total_importance = sum(importance_dict.values())
            importance_dict = {k: v / total_importance for k, v in importance_dict.items()}
        
        return importance_dict
    
    def save_models(self, model_dir: str):
        """Save ensemble models to disk"""
        import os
        os.makedirs(model_dir, exist_ok=True)
        
        for model_name, model in self.models.items():
            model_path = os.path.join(model_dir, f"{model_name}.pkl")
            joblib.dump(model, model_path)
        
        # Save metadata
        metadata = {
            'model_weights': self.model_weights,
            'feature_names': self.feature_names,
            'is_trained': self.is_trained
        }
        metadata_path = os.path.join(model_dir, "ensemble_metadata.pkl")
        joblib.dump(metadata, metadata_path)
        
        logger.info("Ensemble models saved", model_dir=model_dir)
    
    def load_models(self, model_dir: str):
        """Load ensemble models from disk"""
        import os
        
        # Load metadata
        metadata_path = os.path.join(model_dir, "ensemble_metadata.pkl")
        if os.path.exists(metadata_path):
            metadata = joblib.load(metadata_path)
            self.model_weights = metadata['model_weights']
            self.feature_names = metadata['feature_names']
            self.is_trained = metadata['is_trained']
        
        # Load models
        self.models = {}
        for model_file in os.listdir(model_dir):
            if model_file.endswith('.pkl') and model_file != 'ensemble_metadata.pkl':
                model_name = model_file.replace('.pkl', '')
                model_path = os.path.join(model_dir, model_file)
                self.models[model_name] = joblib.load(model_path)
        
        logger.info("Ensemble models loaded", model_count=len(self.models))
