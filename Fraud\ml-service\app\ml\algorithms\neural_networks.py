"""
Neural network models for fraud detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, callbacks, optimizers
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import mlflow
import mlflow.tensorflow
import joblib

from ...core.config import settings
from ...core.logging import get_logger
from ...core.exceptions import ModelTrainingException, ModelLoadException

logger = get_logger(__name__)


class FraudDetectionNN:
    """Neural Network for fraud detection with advanced architecture"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = None
        self.is_trained = False
        self.history = None
    
    def create_model(
        self,
        input_dim: int,
        hidden_layers: List[int] = None,
        dropout_rate: float = None,
        learning_rate: float = None
    ) -> keras.Model:
        """Create neural network model with advanced architecture"""
        
        if hidden_layers is None:
            hidden_layers = settings.NN_HIDDEN_LAYERS
        if dropout_rate is None:
            dropout_rate = settings.NN_DROPOUT_RATE
        if learning_rate is None:
            learning_rate = settings.NN_LEARNING_RATE
        
        # Input layer
        inputs = keras.Input(shape=(input_dim,), name='transaction_features')
        
        # Feature normalization layer
        x = layers.BatchNormalization(name='input_normalization')(inputs)
        
        # Hidden layers with residual connections
        for i, units in enumerate(hidden_layers):
            # Dense layer
            dense = layers.Dense(
                units,
                activation='relu',
                kernel_regularizer=keras.regularizers.l2(0.01),
                name=f'dense_{i+1}'
            )(x)
            
            # Batch normalization
            bn = layers.BatchNormalization(name=f'bn_{i+1}')(dense)
            
            # Dropout
            dropout = layers.Dropout(dropout_rate, name=f'dropout_{i+1}')(bn)
            
            # Residual connection (if dimensions match)
            if x.shape[-1] == units:
                x = layers.Add(name=f'residual_{i+1}')([x, dropout])
            else:
                x = dropout
        
        # Attention mechanism for feature importance
        attention_weights = layers.Dense(
            x.shape[-1],
            activation='softmax',
            name='attention_weights'
        )(x)
        attended_features = layers.Multiply(name='attention_applied')([x, attention_weights])
        
        # Final layers
        x = layers.Dense(32, activation='relu', name='pre_output')(attended_features)
        x = layers.Dropout(dropout_rate * 0.5, name='final_dropout')(x)
        
        # Output layer with sigmoid activation for binary classification
        outputs = layers.Dense(1, activation='sigmoid', name='fraud_probability')(x)
        
        # Create model
        model = keras.Model(inputs=inputs, outputs=outputs, name='fraud_detection_nn')
        
        # Compile model with advanced optimizer
        optimizer = optimizers.Adam(
            learning_rate=learning_rate,
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-7
        )
        
        model.compile(
            optimizer=optimizer,
            loss='binary_crossentropy',
            metrics=[
                'accuracy',
                keras.metrics.Precision(name='precision'),
                keras.metrics.Recall(name='recall'),
                keras.metrics.AUC(name='auc'),
                keras.metrics.AUC(curve='PR', name='auc_pr')
            ]
        )
        
        return model
    
    def create_callbacks(self, model_path: str) -> List[callbacks.Callback]:
        """Create training callbacks"""
        
        callback_list = [
            # Early stopping
            callbacks.EarlyStopping(
                monitor='val_auc',
                patience=15,
                restore_best_weights=True,
                mode='max',
                verbose=1
            ),
            
            # Model checkpoint
            callbacks.ModelCheckpoint(
                filepath=model_path,
                monitor='val_auc',
                save_best_only=True,
                save_weights_only=False,
                mode='max',
                verbose=1
            ),
            
            # Learning rate reduction
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            ),
            
            # MLflow logging
            callbacks.LambdaCallback(
                on_epoch_end=lambda epoch, logs: self._log_metrics_to_mlflow(epoch, logs)
            )
        ]
        
        return callback_list
    
    def _log_metrics_to_mlflow(self, epoch: int, logs: Dict[str, float]):
        """Log metrics to MLflow during training"""
        for metric_name, value in logs.items():
            mlflow.log_metric(metric_name, value, step=epoch)
    
    def train(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: Optional[pd.DataFrame] = None,
        y_val: Optional[pd.Series] = None,
        epochs: int = None,
        batch_size: int = None,
        class_weights: Dict[int, float] = None
    ) -> Dict[str, Any]:
        """Train the neural network"""
        
        if epochs is None:
            epochs = settings.NN_EPOCHS
        if batch_size is None:
            batch_size = settings.NN_BATCH_SIZE
        
        logger.info("Starting neural network training")
        
        # Store feature names
        self.feature_names = list(X_train.columns)
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val) if X_val is not None else None
        
        # Calculate class weights if not provided
        if class_weights is None:
            class_counts = y_train.value_counts()
            total_samples = len(y_train)
            class_weights = {
                0: total_samples / (2 * class_counts[0]),
                1: total_samples / (2 * class_counts[1])
            }
        
        with mlflow.start_run(run_name="neural_network_training"):
            # Log parameters
            mlflow.log_param("model_type", "neural_network")
            mlflow.log_param("hidden_layers", settings.NN_HIDDEN_LAYERS)
            mlflow.log_param("dropout_rate", settings.NN_DROPOUT_RATE)
            mlflow.log_param("learning_rate", settings.NN_LEARNING_RATE)
            mlflow.log_param("batch_size", batch_size)
            mlflow.log_param("epochs", epochs)
            mlflow.log_param("class_weights", class_weights)
            mlflow.log_param("training_samples", len(X_train))
            mlflow.log_param("feature_count", len(self.feature_names))
            
            # Create model
            self.model = self.create_model(input_dim=X_train_scaled.shape[1])
            
            # Log model architecture
            mlflow.log_param("total_parameters", self.model.count_params())
            
            # Create callbacks
            model_path = "/tmp/best_fraud_nn_model.h5"
            callback_list = self.create_callbacks(model_path)
            
            try:
                # Train model
                self.history = self.model.fit(
                    X_train_scaled, y_train,
                    validation_data=(X_val_scaled, y_val) if X_val is not None else None,
                    epochs=epochs,
                    batch_size=batch_size,
                    class_weight=class_weights,
                    callbacks=callback_list,
                    verbose=1
                )
                
                # Load best model
                self.model = keras.models.load_model(model_path)
                
                # Evaluate model
                train_metrics = self.model.evaluate(X_train_scaled, y_train, verbose=0)
                val_metrics = self.model.evaluate(X_val_scaled, y_val, verbose=0) if X_val is not None else None
                
                # Log final metrics
                metric_names = self.model.metrics_names
                train_results = dict(zip(metric_names, train_metrics))
                val_results = dict(zip(metric_names, val_metrics)) if val_metrics else {}
                
                for name, value in train_results.items():
                    mlflow.log_metric(f"final_train_{name}", value)
                
                for name, value in val_results.items():
                    mlflow.log_metric(f"final_val_{name}", value)
                
                # Log model
                mlflow.tensorflow.log_model(self.model, "neural_network_model")
                
                # Save scaler
                scaler_path = "/tmp/nn_scaler.pkl"
                joblib.dump(self.scaler, scaler_path)
                mlflow.log_artifact(scaler_path, "preprocessing")
                
                self.is_trained = True
                
                training_results = {
                    'train_metrics': train_results,
                    'val_metrics': val_results,
                    'history': self.history.history,
                    'epochs_trained': len(self.history.history['loss'])
                }
                
                logger.info(
                    "Neural network training completed",
                    train_auc=train_results.get('auc', 0),
                    val_auc=val_results.get('auc', 0),
                    epochs_trained=training_results['epochs_trained']
                )
                
                return training_results
                
            except Exception as e:
                logger.error("Neural network training failed", error=str(e))
                raise ModelTrainingException(f"Neural network training failed: {str(e)}")
    
    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Make probability predictions"""
        if not self.is_trained or self.model is None:
            raise ModelLoadException("Neural network model not trained or loaded")
        
        X_scaled = self.scaler.transform(X)
        predictions = self.model.predict(X_scaled, verbose=0)
        return predictions.flatten()
    
    def predict(self, X: pd.DataFrame, threshold: float = None) -> np.ndarray:
        """Make binary predictions"""
        if threshold is None:
            threshold = settings.FRAUD_THRESHOLD
        
        probabilities = self.predict_proba(X)
        return (probabilities >= threshold).astype(int)
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance using gradient-based method"""
        if not self.is_trained or self.model is None:
            return {}
        
        # This is a simplified approach - in practice, you might use SHAP or LIME
        # for more accurate feature importance in neural networks
        
        # Get weights from the first layer
        first_layer_weights = self.model.layers[1].get_weights()[0]  # Skip normalization layer
        
        # Calculate importance as mean absolute weight
        importance_scores = np.mean(np.abs(first_layer_weights), axis=1)
        
        # Normalize
        importance_scores = importance_scores / np.sum(importance_scores)
        
        # Create dictionary
        importance_dict = {}
        for i, feature_name in enumerate(self.feature_names):
            importance_dict[feature_name] = float(importance_scores[i])
        
        return importance_dict
    
    def save_model(self, model_path: str, scaler_path: str):
        """Save model and scaler"""
        if self.model is not None:
            self.model.save(model_path)
        
        if self.scaler is not None:
            joblib.dump(self.scaler, scaler_path)
        
        # Save metadata
        metadata = {
            'feature_names': self.feature_names,
            'is_trained': self.is_trained
        }
        metadata_path = model_path.replace('.h5', '_metadata.pkl')
        joblib.dump(metadata, metadata_path)
        
        logger.info("Neural network model saved", model_path=model_path)
    
    def load_model(self, model_path: str, scaler_path: str):
        """Load model and scaler"""
        try:
            self.model = keras.models.load_model(model_path)
            self.scaler = joblib.load(scaler_path)
            
            # Load metadata
            metadata_path = model_path.replace('.h5', '_metadata.pkl')
            metadata = joblib.load(metadata_path)
            self.feature_names = metadata['feature_names']
            self.is_trained = metadata['is_trained']
            
            logger.info("Neural network model loaded", model_path=model_path)
            
        except Exception as e:
            logger.error("Failed to load neural network model", error=str(e))
            raise ModelLoadException(f"Failed to load neural network model: {str(e)}")


class AutoEncoder:
    """Autoencoder for anomaly detection in fraud detection"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.threshold = None
        self.is_trained = False
    
    def create_autoencoder(self, input_dim: int, encoding_dim: int = None) -> keras.Model:
        """Create autoencoder model"""
        
        if encoding_dim is None:
            encoding_dim = max(input_dim // 4, 8)  # Compress to 1/4 of input size
        
        # Encoder
        input_layer = keras.Input(shape=(input_dim,))
        encoded = layers.Dense(encoding_dim * 2, activation='relu')(input_layer)
        encoded = layers.BatchNormalization()(encoded)
        encoded = layers.Dropout(0.2)(encoded)
        encoded = layers.Dense(encoding_dim, activation='relu')(encoded)
        
        # Decoder
        decoded = layers.Dense(encoding_dim * 2, activation='relu')(encoded)
        decoded = layers.BatchNormalization()(decoded)
        decoded = layers.Dropout(0.2)(decoded)
        decoded = layers.Dense(input_dim, activation='linear')(decoded)
        
        # Autoencoder model
        autoencoder = keras.Model(input_layer, decoded)
        autoencoder.compile(
            optimizer='adam',
            loss='mse',
            metrics=['mae']
        )
        
        return autoencoder
    
    def train(
        self,
        X_train: pd.DataFrame,
        contamination: float = 0.1,
        epochs: int = 100,
        batch_size: int = 256
    ) -> Dict[str, Any]:
        """Train autoencoder on normal transactions only"""
        
        logger.info("Starting autoencoder training for anomaly detection")
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        # Create model
        self.model = self.create_autoencoder(X_train_scaled.shape[1])
        
        # Train autoencoder
        history = self.model.fit(
            X_train_scaled, X_train_scaled,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=0.1,
            verbose=1
        )
        
        # Calculate reconstruction errors on training data
        reconstructions = self.model.predict(X_train_scaled)
        reconstruction_errors = np.mean(np.square(X_train_scaled - reconstructions), axis=1)
        
        # Set threshold based on contamination rate
        self.threshold = np.percentile(reconstruction_errors, (1 - contamination) * 100)
        
        self.is_trained = True
        
        return {
            'history': history.history,
            'threshold': self.threshold,
            'mean_reconstruction_error': np.mean(reconstruction_errors)
        }
    
    def predict_anomaly(self, X: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Predict anomalies based on reconstruction error"""
        if not self.is_trained:
            raise ModelLoadException("Autoencoder not trained")
        
        X_scaled = self.scaler.transform(X)
        reconstructions = self.model.predict(X_scaled)
        reconstruction_errors = np.mean(np.square(X_scaled - reconstructions), axis=1)
        
        # Anomaly predictions
        anomalies = (reconstruction_errors > self.threshold).astype(int)
        
        return anomalies, reconstruction_errors
