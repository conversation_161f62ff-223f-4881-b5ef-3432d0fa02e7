"""
Advanced feature extraction for fraud detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import hashlib

from ...core.config import settings
from ...core.logging import get_logger
from ...models.prediction import TransactionData

logger = get_logger(__name__)


class FraudFeatureExtractor:
    """Advanced feature extractor for fraud detection"""
    
    def __init__(self):
        self.feature_cache = {}
        self.account_history = {}
        self.merchant_stats = {}
    
    def extract_features(
        self,
        transaction: TransactionData,
        include_historical: bool = True,
        include_derived: bool = True
    ) -> Dict[str, float]:
        """Extract comprehensive features from transaction data"""
        
        features = {}
        
        # Basic transaction features
        features.update(self._extract_basic_features(transaction))
        
        # Balance-related features
        features.update(self._extract_balance_features(transaction))
        
        # Transaction type features
        features.update(self._extract_type_features(transaction))
        
        # Amount-based features
        features.update(self._extract_amount_features(transaction))
        
        if include_derived:
            # Derived features
            features.update(self._extract_derived_features(transaction))
            
            # Risk indicators
            features.update(self._extract_risk_indicators(transaction))
        
        if include_historical:
            # Historical features (would require external data source)
            features.update(self._extract_historical_features(transaction))
        
        # Account relationship features
        features.update(self._extract_relationship_features(transaction))
        
        return features
    
    def _extract_basic_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract basic transaction features"""
        return {
            'amount': float(transaction.amount),
            'oldbalanceOrg': float(transaction.oldbalanceOrg),
            'newbalanceOrig': float(transaction.newbalanceOrig),
            'oldbalanceDest': float(transaction.oldbalanceDest),
            'newbalanceDest': float(transaction.newbalanceDest),
            'step': float(transaction.step)
        }
    
    def _extract_balance_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract balance-related features"""
        features = {}
        
        # Balance changes
        orig_balance_change = transaction.newbalanceOrig - transaction.oldbalanceOrg
        dest_balance_change = transaction.newbalanceDest - transaction.oldbalanceDest
        
        features['orig_balance_change'] = float(orig_balance_change)
        features['dest_balance_change'] = float(dest_balance_change)
        
        # Balance ratios
        if transaction.oldbalanceOrg > 0:
            features['amount_to_orig_balance_ratio'] = float(transaction.amount / transaction.oldbalanceOrg)
            features['new_to_old_orig_ratio'] = float(transaction.newbalanceOrig / transaction.oldbalanceOrg)
        else:
            features['amount_to_orig_balance_ratio'] = 0.0
            features['new_to_old_orig_ratio'] = 0.0
        
        if transaction.oldbalanceDest > 0:
            features['amount_to_dest_balance_ratio'] = float(transaction.amount / transaction.oldbalanceDest)
            features['new_to_old_dest_ratio'] = float(transaction.newbalanceDest / transaction.oldbalanceDest)
        else:
            features['amount_to_dest_balance_ratio'] = float('inf') if transaction.amount > 0 else 0.0
            features['new_to_old_dest_ratio'] = float('inf') if transaction.newbalanceDest > 0 else 0.0
        
        # Balance consistency checks
        expected_orig_balance = transaction.oldbalanceOrg - transaction.amount
        expected_dest_balance = transaction.oldbalanceDest + transaction.amount
        
        features['orig_balance_error'] = float(abs(transaction.newbalanceOrig - expected_orig_balance))
        features['dest_balance_error'] = float(abs(transaction.newbalanceDest - expected_dest_balance))
        
        # Relative balance errors
        if transaction.amount > 0:
            features['orig_balance_error_ratio'] = float(features['orig_balance_error'] / transaction.amount)
            features['dest_balance_error_ratio'] = float(features['dest_balance_error'] / transaction.amount)
        else:
            features['orig_balance_error_ratio'] = 0.0
            features['dest_balance_error_ratio'] = 0.0
        
        return features
    
    def _extract_type_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract transaction type features (one-hot encoding)"""
        transaction_types = ['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT', 'CASH_IN']
        
        features = {}
        for t_type in transaction_types:
            features[f'type_{t_type}'] = 1.0 if transaction.type == t_type else 0.0
        
        return features
    
    def _extract_amount_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract amount-based features"""
        features = {}
        
        # Amount statistics
        features['amount_log'] = float(np.log1p(transaction.amount))
        features['amount_sqrt'] = float(np.sqrt(transaction.amount))
        
        # Round number detection
        features['is_round_amount'] = 1.0 if transaction.amount % 100 == 0 else 0.0
        features['is_very_round_amount'] = 1.0 if transaction.amount % 1000 == 0 else 0.0
        
        # Amount ranges
        amount_ranges = [
            (0, 100, 'very_small'),
            (100, 1000, 'small'),
            (1000, 10000, 'medium'),
            (10000, 100000, 'large'),
            (100000, float('inf'), 'very_large')
        ]
        
        for min_val, max_val, range_name in amount_ranges:
            features[f'amount_range_{range_name}'] = 1.0 if min_val <= transaction.amount < max_val else 0.0
        
        return features
    
    def _extract_derived_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract derived features"""
        features = {}
        
        # Account type detection (heuristic based on naming)
        features['orig_is_customer'] = 1.0 if transaction.nameOrig.startswith('C') else 0.0
        features['dest_is_customer'] = 1.0 if transaction.nameDest.startswith('C') else 0.0
        features['dest_is_merchant'] = 1.0 if transaction.nameDest.startswith('M') else 0.0
        
        # Transaction patterns
        features['customer_to_customer'] = 1.0 if (features['orig_is_customer'] and features['dest_is_customer']) else 0.0
        features['customer_to_merchant'] = 1.0 if (features['orig_is_customer'] and features['dest_is_merchant']) else 0.0
        
        # Zero balance flags
        features['orig_zero_balance_before'] = 1.0 if transaction.oldbalanceOrg == 0 else 0.0
        features['orig_zero_balance_after'] = 1.0 if transaction.newbalanceOrig == 0 else 0.0
        features['dest_zero_balance_before'] = 1.0 if transaction.oldbalanceDest == 0 else 0.0
        features['dest_zero_balance_after'] = 1.0 if transaction.newbalanceDest == 0 else 0.0
        
        # Balance emptying
        features['orig_account_emptied'] = 1.0 if (transaction.oldbalanceOrg > 0 and transaction.newbalanceOrig == 0) else 0.0
        features['dest_account_created'] = 1.0 if (transaction.oldbalanceDest == 0 and transaction.newbalanceDest > 0) else 0.0
        
        return features
    
    def _extract_risk_indicators(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract risk indicator features"""
        features = {}
        
        # High-risk transaction patterns
        features['large_round_transfer'] = 1.0 if (
            transaction.type in ['TRANSFER', 'CASH_OUT'] and
            transaction.amount >= 10000 and
            transaction.amount % 1000 == 0
        ) else 0.0
        
        # Suspicious balance patterns
        features['suspicious_balance_pattern'] = 1.0 if (
            transaction.oldbalanceOrg > 0 and
            transaction.newbalanceOrig == 0 and
            transaction.oldbalanceDest == 0 and
            transaction.newbalanceDest == 0
        ) else 0.0
        
        # Exact amount transfers
        features['exact_balance_transfer'] = 1.0 if (
            abs(transaction.amount - transaction.oldbalanceOrg) < 0.01
        ) else 0.0
        
        # Multiple of common amounts
        common_amounts = [500, 1000, 2000, 5000, 10000, 20000, 50000]
        features['multiple_of_common_amount'] = 1.0 if any(
            transaction.amount % amount == 0 for amount in common_amounts
        ) else 0.0
        
        return features
    
    def _extract_historical_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract historical features (simplified version)"""
        features = {}
        
        # This would typically require access to historical transaction data
        # For now, we'll create placeholder features that could be populated
        # from a feature store or database
        
        features['orig_transaction_count_1h'] = 0.0  # Placeholder
        features['orig_transaction_count_24h'] = 0.0  # Placeholder
        features['orig_total_amount_1h'] = 0.0  # Placeholder
        features['orig_total_amount_24h'] = 0.0  # Placeholder
        features['orig_avg_transaction_amount'] = 0.0  # Placeholder
        features['orig_unique_destinations_24h'] = 0.0  # Placeholder
        
        features['dest_transaction_count_1h'] = 0.0  # Placeholder
        features['dest_transaction_count_24h'] = 0.0  # Placeholder
        features['dest_total_amount_1h'] = 0.0  # Placeholder
        features['dest_total_amount_24h'] = 0.0  # Placeholder
        
        # Time-based features
        features['hour_of_day'] = float(transaction.step % 24)  # Assuming step represents hours
        features['is_weekend'] = 1.0 if (transaction.step // 24) % 7 in [5, 6] else 0.0
        features['is_night_time'] = 1.0 if features['hour_of_day'] < 6 or features['hour_of_day'] > 22 else 0.0
        
        return features
    
    def _extract_relationship_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Extract account relationship features"""
        features = {}
        
        # Account ID hashing for anonymized features
        orig_hash = int(hashlib.md5(transaction.nameOrig.encode()).hexdigest()[:8], 16)
        dest_hash = int(hashlib.md5(transaction.nameDest.encode()).hexdigest()[:8], 16)
        
        # Relationship indicators
        features['account_id_similarity'] = 1.0 if abs(orig_hash - dest_hash) < 1000 else 0.0
        features['same_account_type'] = 1.0 if (
            transaction.nameOrig[0] == transaction.nameDest[0]
        ) else 0.0
        
        # Self-transaction detection
        features['self_transaction'] = 1.0 if transaction.nameOrig == transaction.nameDest else 0.0
        
        return features
    
    def get_feature_names(self) -> List[str]:
        """Get list of all possible feature names"""
        # Create a dummy transaction to get feature names
        dummy_transaction = TransactionData(
            step=1,
            type='PAYMENT',
            amount=1000.0,
            nameOrig='C123',
            oldbalanceOrg=5000.0,
            newbalanceOrig=4000.0,
            nameDest='M456',
            oldbalanceDest=0.0,
            newbalanceDest=0.0
        )
        
        features = self.extract_features(dummy_transaction)
        return list(features.keys())
    
    def validate_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """Validate and clean features"""
        cleaned_features = {}
        
        for name, value in features.items():
            # Handle infinite values
            if np.isinf(value):
                cleaned_features[name] = 1e6 if value > 0 else -1e6
            # Handle NaN values
            elif np.isnan(value):
                cleaned_features[name] = 0.0
            else:
                cleaned_features[name] = float(value)
        
        return cleaned_features
