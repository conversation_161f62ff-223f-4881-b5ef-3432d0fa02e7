"""
Model evaluation utilities for fraud detection
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, average_precision_score, confusion_matrix,
    classification_report, roc_curve, precision_recall_curve
)
import mlflow

from ...core.logging import get_logger, MLMetricsLogger

logger = get_logger(__name__)
metrics_logger = MLMetricsLogger()


class ModelEvaluator:
    """Comprehensive model evaluation for fraud detection"""
    
    def __init__(self):
        self.evaluation_history = []
    
    def evaluate_model(
        self,
        model: Any,
        X_test: pd.DataFrame,
        y_test: pd.Series,
        model_name: str,
        threshold: float = 0.5
    ) -> Dict[str, float]:
        """Comprehensive model evaluation"""
        
        logger.info(f"Evaluating model: {model_name}")
        
        try:
            # Get predictions
            if hasattr(model, 'predict_proba'):
                y_proba = model.predict_proba(X_test)
                if len(y_proba.shape) > 1 and y_proba.shape[1] > 1:
                    y_scores = y_proba[:, 1]  # Probability of fraud class
                else:
                    y_scores = y_proba.flatten()
            else:
                # For anomaly detection models
                y_scores = model.predict_proba(X_test)
            
            y_pred = (y_scores >= threshold).astype(int)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_test, y_pred, y_scores)
            
            # Add model-specific information
            metrics['model_name'] = model_name
            metrics['threshold'] = threshold
            metrics['test_samples'] = len(y_test)
            metrics['fraud_rate'] = y_test.mean()
            
            # Log metrics
            self._log_metrics(metrics, model_name)
            
            # Store evaluation
            self.evaluation_history.append({
                'model_name': model_name,
                'timestamp': pd.Timestamp.now(),
                'metrics': metrics
            })
            
            logger.info(
                f"Model evaluation completed: {model_name}",
                auc_roc=metrics['auc_roc'],
                auc_pr=metrics['auc_pr'],
                f1_score=metrics['f1_score']
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Model evaluation failed for {model_name}", error=str(e))
            raise
    
    def _calculate_metrics(
        self,
        y_true: pd.Series,
        y_pred: np.ndarray,
        y_scores: np.ndarray
    ) -> Dict[str, float]:
        """Calculate comprehensive evaluation metrics"""
        
        metrics = {}
        
        # Basic classification metrics
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        metrics['precision'] = precision_score(y_true, y_pred, zero_division=0)
        metrics['recall'] = recall_score(y_true, y_pred, zero_division=0)
        metrics['f1_score'] = f1_score(y_true, y_pred, zero_division=0)
        
        # ROC and PR AUC
        try:
            metrics['auc_roc'] = roc_auc_score(y_true, y_scores)
        except ValueError:
            metrics['auc_roc'] = 0.0
            
        try:
            metrics['auc_pr'] = average_precision_score(y_true, y_scores)
        except ValueError:
            metrics['auc_pr'] = 0.0
        
        # Confusion matrix components
        tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
        
        metrics['true_positives'] = int(tp)
        metrics['true_negatives'] = int(tn)
        metrics['false_positives'] = int(fp)
        metrics['false_negatives'] = int(fn)
        
        # Additional metrics
        metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0.0
        metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        metrics['positive_predictive_value'] = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        metrics['negative_predictive_value'] = tn / (tn + fn) if (tn + fn) > 0 else 0.0
        
        # Business metrics for fraud detection
        metrics['fraud_detection_rate'] = metrics['recall']  # Same as recall
        metrics['false_alarm_rate'] = fp / (fp + tn) if (fp + tn) > 0 else 0.0
        
        # Cost-based metrics (assuming costs)
        cost_fp = 10  # Cost of false positive (blocking legitimate transaction)
        cost_fn = 100  # Cost of false negative (missing fraud)
        total_cost = (fp * cost_fp) + (fn * cost_fn)
        metrics['total_cost'] = total_cost
        metrics['cost_per_transaction'] = total_cost / len(y_true)
        
        return metrics
    
    def _log_metrics(self, metrics: Dict[str, float], model_name: str):
        """Log metrics to MLflow and structured logging"""
        
        # Log to MLflow
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)) and metric_name != 'model_name':
                mlflow.log_metric(f"{model_name}_{metric_name}", value)
        
        # Log to structured logging
        metrics_logger.log_model_performance(
            model_name=model_name,
            model_version="current",
            metrics={k: v for k, v in metrics.items() if isinstance(v, (int, float))},
            dataset_size=metrics.get('test_samples', 0)
        )
    
    def compare_models(
        self,
        models: Dict[str, Any],
        X_test: pd.DataFrame,
        y_test: pd.Series,
        metrics_to_compare: List[str] = None
    ) -> pd.DataFrame:
        """Compare multiple models"""
        
        if metrics_to_compare is None:
            metrics_to_compare = ['auc_roc', 'auc_pr', 'f1_score', 'precision', 'recall']
        
        logger.info("Comparing models", model_count=len(models))
        
        comparison_results = []
        
        for model_name, model in models.items():
            metrics = self.evaluate_model(model, X_test, y_test, model_name)
            
            result = {'model_name': model_name}
            for metric in metrics_to_compare:
                result[metric] = metrics.get(metric, 0.0)
            
            comparison_results.append(result)
        
        comparison_df = pd.DataFrame(comparison_results)
        
        # Log comparison results
        logger.info("Model comparison completed")
        for _, row in comparison_df.iterrows():
            logger.info(f"Model: {row['model_name']}", **{k: v for k, v in row.items() if k != 'model_name'})
        
        return comparison_df
    
    def evaluate_threshold_sensitivity(
        self,
        model: Any,
        X_test: pd.DataFrame,
        y_test: pd.Series,
        thresholds: List[float] = None
    ) -> pd.DataFrame:
        """Evaluate model performance across different thresholds"""
        
        if thresholds is None:
            thresholds = np.arange(0.1, 1.0, 0.1)
        
        logger.info("Evaluating threshold sensitivity", threshold_count=len(thresholds))
        
        # Get prediction scores
        if hasattr(model, 'predict_proba'):
            y_scores = model.predict_proba(X_test)
            if len(y_scores.shape) > 1 and y_scores.shape[1] > 1:
                y_scores = y_scores[:, 1]
            else:
                y_scores = y_scores.flatten()
        else:
            y_scores = model.predict_proba(X_test)
        
        results = []
        
        for threshold in thresholds:
            y_pred = (y_scores >= threshold).astype(int)
            
            # Calculate metrics for this threshold
            metrics = self._calculate_metrics(y_test, y_pred, y_scores)
            metrics['threshold'] = threshold
            
            results.append(metrics)
        
        threshold_df = pd.DataFrame(results)
        
        # Find optimal threshold based on F1 score
        optimal_idx = threshold_df['f1_score'].idxmax()
        optimal_threshold = threshold_df.loc[optimal_idx, 'threshold']
        
        logger.info(
            "Threshold sensitivity analysis completed",
            optimal_threshold=optimal_threshold,
            optimal_f1=threshold_df.loc[optimal_idx, 'f1_score']
        )
        
        return threshold_df
    
    def generate_evaluation_report(
        self,
        model: Any,
        X_test: pd.DataFrame,
        y_test: pd.Series,
        model_name: str,
        save_plots: bool = False,
        plot_dir: str = "/tmp/plots"
    ) -> Dict[str, Any]:
        """Generate comprehensive evaluation report"""
        
        logger.info(f"Generating evaluation report for {model_name}")
        
        # Basic evaluation
        metrics = self.evaluate_model(model, X_test, y_test, model_name)
        
        # Threshold sensitivity
        threshold_analysis = self.evaluate_threshold_sensitivity(model, X_test, y_test)
        
        # Get predictions for detailed analysis
        if hasattr(model, 'predict_proba'):
            y_scores = model.predict_proba(X_test)
            if len(y_scores.shape) > 1 and y_scores.shape[1] > 1:
                y_scores = y_scores[:, 1]
            else:
                y_scores = y_scores.flatten()
        else:
            y_scores = model.predict_proba(X_test)
        
        y_pred = (y_scores >= 0.5).astype(int)
        
        # Classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)
        
        # Feature importance (if available)
        feature_importance = None
        if hasattr(model, 'get_feature_importance'):
            feature_importance = model.get_feature_importance()
        elif hasattr(model, 'feature_importances_'):
            feature_names = X_test.columns if hasattr(X_test, 'columns') else [f'feature_{i}' for i in range(X_test.shape[1])]
            feature_importance = dict(zip(feature_names, model.feature_importances_))
        
        # Compile report
        report = {
            'model_name': model_name,
            'evaluation_timestamp': pd.Timestamp.now().isoformat(),
            'basic_metrics': metrics,
            'threshold_analysis': threshold_analysis.to_dict('records'),
            'classification_report': class_report,
            'feature_importance': feature_importance,
            'confusion_matrix': confusion_matrix(y_test, y_pred).tolist()
        }
        
        # Generate plots if requested
        if save_plots:
            self._generate_evaluation_plots(
                y_test, y_pred, y_scores, model_name, plot_dir
            )
            report['plots_saved'] = True
            report['plot_directory'] = plot_dir
        
        logger.info(f"Evaluation report generated for {model_name}")
        
        return report
    
    def _generate_evaluation_plots(
        self,
        y_true: pd.Series,
        y_pred: np.ndarray,
        y_scores: np.ndarray,
        model_name: str,
        plot_dir: str
    ):
        """Generate evaluation plots"""
        
        import os
        os.makedirs(plot_dir, exist_ok=True)
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. ROC Curve
        fpr, tpr, _ = roc_curve(y_true, y_scores)
        auc_roc = roc_auc_score(y_true, y_scores)
        
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, label=f'ROC Curve (AUC = {auc_roc:.3f})')
        plt.plot([0, 1], [0, 1], 'k--', label='Random')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title(f'ROC Curve - {model_name}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(plot_dir, f'{model_name}_roc_curve.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Precision-Recall Curve
        precision, recall, _ = precision_recall_curve(y_true, y_scores)
        auc_pr = average_precision_score(y_true, y_scores)
        
        plt.figure(figsize=(8, 6))
        plt.plot(recall, precision, label=f'PR Curve (AUC = {auc_pr:.3f})')
        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title(f'Precision-Recall Curve - {model_name}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(plot_dir, f'{model_name}_pr_curve.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Confusion Matrix
        cm = confusion_matrix(y_true, y_pred)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Normal', 'Fraud'], 
                   yticklabels=['Normal', 'Fraud'])
        plt.title(f'Confusion Matrix - {model_name}')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.savefig(os.path.join(plot_dir, f'{model_name}_confusion_matrix.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 4. Score Distribution
        plt.figure(figsize=(10, 6))
        fraud_scores = y_scores[y_true == 1]
        normal_scores = y_scores[y_true == 0]
        
        plt.hist(normal_scores, bins=50, alpha=0.7, label='Normal', density=True)
        plt.hist(fraud_scores, bins=50, alpha=0.7, label='Fraud', density=True)
        plt.xlabel('Fraud Score')
        plt.ylabel('Density')
        plt.title(f'Score Distribution - {model_name}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(plot_dir, f'{model_name}_score_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Evaluation plots saved for {model_name}", plot_dir=plot_dir)
    
    def get_evaluation_summary(self) -> pd.DataFrame:
        """Get summary of all evaluations"""
        
        if not self.evaluation_history:
            return pd.DataFrame()
        
        summary_data = []
        for eval_record in self.evaluation_history:
            summary = {
                'model_name': eval_record['model_name'],
                'timestamp': eval_record['timestamp'],
                'auc_roc': eval_record['metrics'].get('auc_roc', 0),
                'auc_pr': eval_record['metrics'].get('auc_pr', 0),
                'f1_score': eval_record['metrics'].get('f1_score', 0),
                'precision': eval_record['metrics'].get('precision', 0),
                'recall': eval_record['metrics'].get('recall', 0)
            }
            summary_data.append(summary)
        
        return pd.DataFrame(summary_data)
