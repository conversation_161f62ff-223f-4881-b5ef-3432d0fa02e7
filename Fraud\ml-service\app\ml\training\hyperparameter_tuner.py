"""
Hyperparameter tuning for fraud detection models
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, <PERSON>, Tuple, Optional
import optuna
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
import xgboost as xgb
import lightgbm as lgb
import mlflow

from ...core.config import settings
from ...core.logging import get_logger

logger = get_logger(__name__)


class HyperparameterTuner:
    """Hyperparameter tuning using Optuna"""
    
    def __init__(self, n_trials: int = 100, cv_folds: int = 5):
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.study_results = {}
    
    def tune_ensemble_hyperparameters(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        model_types: List[str] = None
    ) -> Dict[str, Dict[str, Any]]:
        """Tune hyperparameters for ensemble models"""
        
        if model_types is None:
            model_types = ['random_forest', 'xgboost', 'lightgbm']
        
        logger.info("Starting ensemble hyperparameter tuning", model_types=model_types)
        
        best_params = {}
        
        for model_type in model_types:
            logger.info(f"Tuning hyperparameters for {model_type}")
            
            if model_type == 'random_forest':
                best_params[model_type] = self._tune_random_forest(X_train, y_train)
            elif model_type == 'xgboost':
                best_params[model_type] = self._tune_xgboost(X_train, y_train)
            elif model_type == 'lightgbm':
                best_params[model_type] = self._tune_lightgbm(X_train, y_train)
            else:
                logger.warning(f"Unknown model type for tuning: {model_type}")
        
        logger.info("Ensemble hyperparameter tuning completed", best_params=best_params)
        return best_params
    
    def _tune_random_forest(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Any]:
        """Tune Random Forest hyperparameters"""
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 5, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.5, 0.8]),
                'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
                'class_weight': 'balanced',
                'random_state': 42,
                'n_jobs': -1
            }
            
            model = RandomForestClassifier(**params)
            
            # Cross-validation
            cv_scores = cross_val_score(
                model, X_train, y_train,
                cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=42),
                scoring='roc_auc',
                n_jobs=-1
            )
            
            return cv_scores.mean()
        
        study = optuna.create_study(direction='maximize', study_name='random_forest_tuning')
        study.optimize(objective, n_trials=self.n_trials)
        
        self.study_results['random_forest'] = study
        
        logger.info(
            "Random Forest tuning completed",
            best_score=study.best_value,
            best_params=study.best_params
        )
        
        return study.best_params
    
    def _tune_xgboost(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Any]:
        """Tune XGBoost hyperparameters"""
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 3, 12),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 1.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 1.0),
                'scale_pos_weight': trial.suggest_float('scale_pos_weight', 1, 20),
                'random_state': 42,
                'n_jobs': -1,
                'eval_metric': 'auc'
            }
            
            model = xgb.XGBClassifier(**params)
            
            # Cross-validation
            cv_scores = cross_val_score(
                model, X_train, y_train,
                cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=42),
                scoring='roc_auc',
                n_jobs=-1
            )
            
            return cv_scores.mean()
        
        study = optuna.create_study(direction='maximize', study_name='xgboost_tuning')
        study.optimize(objective, n_trials=self.n_trials)
        
        self.study_results['xgboost'] = study
        
        logger.info(
            "XGBoost tuning completed",
            best_score=study.best_value,
            best_params=study.best_params
        )
        
        return study.best_params
    
    def _tune_lightgbm(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Any]:
        """Tune LightGBM hyperparameters"""
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 3, 12),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 1.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 1.0),
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'class_weight': 'balanced',
                'random_state': 42,
                'n_jobs': -1,
                'metric': 'auc',
                'verbose': -1
            }
            
            model = lgb.LGBMClassifier(**params)
            
            # Cross-validation
            cv_scores = cross_val_score(
                model, X_train, y_train,
                cv=StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=42),
                scoring='roc_auc',
                n_jobs=-1
            )
            
            return cv_scores.mean()
        
        study = optuna.create_study(direction='maximize', study_name='lightgbm_tuning')
        study.optimize(objective, n_trials=self.n_trials)
        
        self.study_results['lightgbm'] = study
        
        logger.info(
            "LightGBM tuning completed",
            best_score=study.best_value,
            best_params=study.best_params
        )
        
        return study.best_params
    
    def tune_neural_hyperparameters(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Any]:
        """Tune neural network hyperparameters"""
        
        logger.info("Starting neural network hyperparameter tuning")
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'hidden_layers': [
                    trial.suggest_int('layer_1_units', 32, 256),
                    trial.suggest_int('layer_2_units', 16, 128),
                    trial.suggest_int('layer_3_units', 8, 64)
                ],
                'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
                'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
                'batch_size': trial.suggest_categorical('batch_size', [64, 128, 256, 512]),
                'epochs': trial.suggest_int('epochs', 50, 200)
            }
            
            # Use a simplified validation approach for hyperparameter tuning
            # In practice, you might want to use a more sophisticated approach
            from sklearn.model_selection import train_test_split
            
            X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
                X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
            )
            
            # Create and train a simplified neural network for tuning
            # This is a placeholder - you would implement actual NN training here
            from sklearn.neural_network import MLPClassifier
            
            # Use sklearn's MLPClassifier as a proxy for hyperparameter tuning
            mlp_params = {
                'hidden_layer_sizes': tuple(params['hidden_layers']),
                'learning_rate_init': params['learning_rate'],
                'batch_size': min(params['batch_size'], len(X_train_split)),
                'max_iter': min(params['epochs'], 100),  # Limit for faster tuning
                'random_state': 42,
                'early_stopping': True,
                'validation_fraction': 0.1
            }
            
            model = MLPClassifier(**mlp_params)
            
            try:
                model.fit(X_train_split, y_train_split)
                score = model.score(X_val_split, y_val_split)
                return score
            except Exception:
                return 0.0  # Return poor score if training fails
        
        study = optuna.create_study(direction='maximize', study_name='neural_network_tuning')
        study.optimize(objective, n_trials=min(self.n_trials, 50))  # Fewer trials for NN
        
        self.study_results['neural_network'] = study
        
        # Convert back to our format
        best_params = study.best_params
        formatted_params = {
            'hidden_layers': [
                best_params['layer_1_units'],
                best_params['layer_2_units'],
                best_params['layer_3_units']
            ],
            'dropout_rate': best_params['dropout_rate'],
            'learning_rate': best_params['learning_rate'],
            'batch_size': best_params['batch_size'],
            'epochs': best_params['epochs']
        }
        
        logger.info(
            "Neural network tuning completed",
            best_score=study.best_value,
            best_params=formatted_params
        )
        
        return formatted_params
    
    def tune_anomaly_hyperparameters(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Dict[str, Any]]:
        """Tune anomaly detection hyperparameters"""
        
        logger.info("Starting anomaly detection hyperparameter tuning")
        
        best_params = {}
        
        # Tune Isolation Forest
        best_params['isolation_forest'] = self._tune_isolation_forest(X_train, y_train)
        
        # Tune One-Class SVM
        best_params['one_class_svm'] = self._tune_one_class_svm(X_train, y_train)
        
        logger.info("Anomaly detection tuning completed", best_params=best_params)
        return best_params
    
    def _tune_isolation_forest(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Any]:
        """Tune Isolation Forest hyperparameters"""
        
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 200),
                'max_samples': trial.suggest_categorical('max_samples', ['auto', 0.5, 0.8, 1.0]),
                'contamination': trial.suggest_float('contamination', 0.05, 0.2),
                'max_features': trial.suggest_float('max_features', 0.5, 1.0),
                'random_state': 42
            }
            
            from sklearn.ensemble import IsolationForest
            model = IsolationForest(**params)
            
            # Train on normal transactions only
            X_normal = X_train[y_train == 0]
            model.fit(X_normal)
            
            # Evaluate on full dataset
            predictions = model.predict(X_train)
            # Convert to binary (1 for anomaly, 0 for normal)
            predictions = (predictions == -1).astype(int)
            
            # Calculate F1 score
            from sklearn.metrics import f1_score
            score = f1_score(y_train, predictions, zero_division=0)
            
            return score
        
        study = optuna.create_study(direction='maximize', study_name='isolation_forest_tuning')
        study.optimize(objective, n_trials=min(self.n_trials, 30))
        
        return study.best_params
    
    def _tune_one_class_svm(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series
    ) -> Dict[str, Any]:
        """Tune One-Class SVM hyperparameters"""
        
        def objective(trial):
            params = {
                'kernel': trial.suggest_categorical('kernel', ['rbf', 'poly', 'sigmoid']),
                'gamma': trial.suggest_categorical('gamma', ['scale', 'auto']),
                'nu': trial.suggest_float('nu', 0.05, 0.2),
                'degree': trial.suggest_int('degree', 2, 5) if trial.params.get('kernel') == 'poly' else 3
            }
            
            from sklearn.svm import OneClassSVM
            model = OneClassSVM(**params)
            
            # Train on normal transactions only
            X_normal = X_train[y_train == 0]
            
            # Use a subset for faster training
            if len(X_normal) > 1000:
                X_normal = X_normal.sample(n=1000, random_state=42)
            
            model.fit(X_normal)
            
            # Evaluate on a subset of full dataset
            X_eval = X_train.sample(n=min(1000, len(X_train)), random_state=42)
            y_eval = y_train.loc[X_eval.index]
            
            predictions = model.predict(X_eval)
            # Convert to binary (1 for anomaly, 0 for normal)
            predictions = (predictions == -1).astype(int)
            
            # Calculate F1 score
            from sklearn.metrics import f1_score
            score = f1_score(y_eval, predictions, zero_division=0)
            
            return score
        
        study = optuna.create_study(direction='maximize', study_name='one_class_svm_tuning')
        study.optimize(objective, n_trials=min(self.n_trials, 20))  # Fewer trials due to slow training
        
        return study.best_params
    
    def get_tuning_summary(self) -> Dict[str, Any]:
        """Get summary of all tuning results"""
        
        summary = {}
        
        for model_name, study in self.study_results.items():
            summary[model_name] = {
                'best_score': study.best_value,
                'best_params': study.best_params,
                'n_trials': len(study.trials),
                'study_name': study.study_name
            }
        
        return summary
    
    def save_tuning_results(self, output_path: str):
        """Save tuning results to file"""
        
        import json
        
        summary = self.get_tuning_summary()
        
        with open(output_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info("Tuning results saved", output_path=output_path)
