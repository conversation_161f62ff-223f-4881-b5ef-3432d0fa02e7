"""
Model training orchestrator for fraud detection
"""

import os
import time
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import mlflow
import mlflow.sklearn
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTEENN

from ...core.config import settings
from ...core.logging import get_logger, MLMetricsLogger
from ...core.exceptions import ModelTrainingException
from ..algorithms.ensemble_models import EnsembleModelManager
from ..algorithms.neural_networks import FraudDetectionNN
from ..algorithms.anomaly_detection import AnomalyDetectionEnsemble
from ..feature_engineering.feature_extractor import FraudFeatureExtractor
from .evaluator import ModelEvaluator
from .hyperparameter_tuner import HyperparameterTuner

logger = get_logger(__name__)
metrics_logger = MLMetricsLogger()


class FraudModelTrainer:
    """Comprehensive fraud detection model trainer"""
    
    def __init__(self):
        self.feature_extractor = FraudFeatureExtractor()
        self.evaluator = ModelEvaluator()
        self.hyperparameter_tuner = HyperparameterTuner()
        self.training_data = None
        self.feature_names = None
        
    def load_training_data(self, data_path: str) -> pd.DataFrame:
        """Load and preprocess training data"""
        logger.info("Loading training data", data_path=data_path)
        
        try:
            # Load data
            if data_path.endswith('.csv'):
                data = pd.read_csv(data_path)
            elif data_path.endswith('.parquet'):
                data = pd.read_parquet(data_path)
            else:
                raise ValueError(f"Unsupported file format: {data_path}")
            
            # Basic validation
            required_columns = ['step', 'type', 'amount', 'nameOrig', 'oldbalanceOrg', 
                              'newbalanceOrig', 'nameDest', 'oldbalanceDest', 'newbalanceDest']
            
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Check for target column
            if 'isFraud' not in data.columns:
                raise ValueError("Target column 'isFraud' not found")
            
            logger.info(
                "Training data loaded successfully",
                rows=len(data),
                columns=len(data.columns),
                fraud_rate=data['isFraud'].mean()
            )
            
            self.training_data = data
            return data
            
        except Exception as e:
            logger.error("Failed to load training data", error=str(e))
            raise ModelTrainingException(f"Failed to load training data: {str(e)}")
    
    def prepare_features(
        self,
        data: pd.DataFrame,
        include_historical: bool = False
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare features for training"""
        logger.info("Preparing features for training")
        
        try:
            features_list = []
            
            for idx, row in data.iterrows():
                # Convert row to TransactionData format
                from ...models.prediction import TransactionData, TransactionType
                
                transaction = TransactionData(
                    step=int(row['step']),
                    type=TransactionType(row['type']),
                    amount=float(row['amount']),
                    nameOrig=str(row['nameOrig']),
                    oldbalanceOrg=float(row['oldbalanceOrg']),
                    newbalanceOrig=float(row['newbalanceOrig']),
                    nameDest=str(row['nameDest']),
                    oldbalanceDest=float(row['oldbalanceDest']),
                    newbalanceDest=float(row['newbalanceDest'])
                )
                
                # Extract features
                features = self.feature_extractor.extract_features(
                    transaction,
                    include_historical=include_historical,
                    include_derived=True
                )
                
                features_list.append(features)
            
            # Convert to DataFrame
            X = pd.DataFrame(features_list)
            y = data['isFraud']
            
            # Store feature names
            self.feature_names = list(X.columns)
            
            logger.info(
                "Features prepared successfully",
                feature_count=len(X.columns),
                samples=len(X)
            )
            
            return X, y
            
        except Exception as e:
            logger.error("Failed to prepare features", error=str(e))
            raise ModelTrainingException(f"Failed to prepare features: {str(e)}")
    
    def handle_imbalanced_data(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        strategy: str = "smote"
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """Handle imbalanced dataset"""
        logger.info(f"Handling imbalanced data with strategy: {strategy}")
        
        original_fraud_rate = y.mean()
        logger.info(f"Original fraud rate: {original_fraud_rate:.4f}")
        
        try:
            if strategy == "smote":
                # SMOTE oversampling
                smote = SMOTE(random_state=42, k_neighbors=5)
                X_resampled, y_resampled = smote.fit_resample(X, y)
                
            elif strategy == "undersampling":
                # Random undersampling
                undersampler = RandomUnderSampler(random_state=42)
                X_resampled, y_resampled = undersampler.fit_resample(X, y)
                
            elif strategy == "smoteenn":
                # SMOTE + Edited Nearest Neighbours
                smoteenn = SMOTEENN(random_state=42)
                X_resampled, y_resampled = smoteenn.fit_resample(X, y)
                
            elif strategy == "none":
                # No resampling
                X_resampled, y_resampled = X, y
                
            else:
                raise ValueError(f"Unknown resampling strategy: {strategy}")
            
            new_fraud_rate = y_resampled.mean()
            
            logger.info(
                "Data resampling completed",
                strategy=strategy,
                original_samples=len(X),
                new_samples=len(X_resampled),
                original_fraud_rate=original_fraud_rate,
                new_fraud_rate=new_fraud_rate
            )
            
            return pd.DataFrame(X_resampled, columns=X.columns), pd.Series(y_resampled)
            
        except Exception as e:
            logger.error("Failed to handle imbalanced data", error=str(e))
            raise ModelTrainingException(f"Failed to handle imbalanced data: {str(e)}")
    
    def train_ensemble_model(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame,
        y_val: pd.Series,
        hyperparameter_tuning: bool = True
    ) -> Dict[str, Any]:
        """Train ensemble model"""
        logger.info("Starting ensemble model training")
        
        try:
            ensemble_manager = EnsembleModelManager()
            
            # Hyperparameter tuning if requested
            if hyperparameter_tuning:
                logger.info("Performing hyperparameter tuning for ensemble")
                best_params = self.hyperparameter_tuner.tune_ensemble_hyperparameters(
                    X_train, y_train
                )
                logger.info("Best hyperparameters found", params=best_params)
            else:
                best_params = {}
            
            # Train ensemble
            training_results = ensemble_manager.train_ensemble(
                X_train, y_train, X_val, y_val,
                model_types=settings.ENSEMBLE_MODELS
            )
            
            # Evaluate ensemble
            evaluation_results = self.evaluator.evaluate_model(
                ensemble_manager, X_val, y_val, "ensemble"
            )
            
            # Log training completion
            metrics_logger.log_model_training(
                model_name="ensemble",
                training_time_seconds=time.time(),  # This should be tracked properly
                training_samples=len(X_train),
                validation_score=evaluation_results['auc_roc']
            )
            
            # Save model
            model_dir = "/models/ensemble_models"
            os.makedirs(model_dir, exist_ok=True)
            ensemble_manager.save_models(model_dir)
            
            return {
                "model": ensemble_manager,
                "training_results": training_results,
                "evaluation_results": evaluation_results,
                "model_path": model_dir
            }
            
        except Exception as e:
            logger.error("Ensemble model training failed", error=str(e))
            raise ModelTrainingException(f"Ensemble model training failed: {str(e)}")
    
    def train_neural_model(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame,
        y_val: pd.Series,
        hyperparameter_tuning: bool = True
    ) -> Dict[str, Any]:
        """Train neural network model"""
        logger.info("Starting neural network training")
        
        try:
            nn_model = FraudDetectionNN()
            
            # Hyperparameter tuning if requested
            if hyperparameter_tuning:
                logger.info("Performing hyperparameter tuning for neural network")
                best_params = self.hyperparameter_tuner.tune_neural_hyperparameters(
                    X_train, y_train
                )
                logger.info("Best hyperparameters found", params=best_params)
            else:
                best_params = {}
            
            # Calculate class weights for imbalanced data
            class_counts = y_train.value_counts()
            total_samples = len(y_train)
            class_weights = {
                0: total_samples / (2 * class_counts[0]),
                1: total_samples / (2 * class_counts[1])
            }
            
            # Train neural network
            training_results = nn_model.train(
                X_train, y_train, X_val, y_val,
                epochs=best_params.get('epochs', settings.NN_EPOCHS),
                batch_size=best_params.get('batch_size', settings.NN_BATCH_SIZE),
                class_weights=class_weights
            )
            
            # Evaluate model
            evaluation_results = self.evaluator.evaluate_model(
                nn_model, X_val, y_val, "neural_network"
            )
            
            # Log training completion
            metrics_logger.log_model_training(
                model_name="neural_network",
                training_time_seconds=time.time(),  # This should be tracked properly
                training_samples=len(X_train),
                validation_score=evaluation_results['auc_roc']
            )
            
            # Save model
            model_dir = "/models/neural_models"
            os.makedirs(model_dir, exist_ok=True)
            model_path = os.path.join(model_dir, "fraud_nn_model.h5")
            scaler_path = os.path.join(model_dir, "nn_scaler.pkl")
            nn_model.save_model(model_path, scaler_path)
            
            return {
                "model": nn_model,
                "training_results": training_results,
                "evaluation_results": evaluation_results,
                "model_path": model_path
            }
            
        except Exception as e:
            logger.error("Neural network training failed", error=str(e))
            raise ModelTrainingException(f"Neural network training failed: {str(e)}")
    
    def train_anomaly_model(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame,
        y_val: pd.Series
    ) -> Dict[str, Any]:
        """Train anomaly detection model"""
        logger.info("Starting anomaly detection training")
        
        try:
            anomaly_ensemble = AnomalyDetectionEnsemble()
            
            # Train on normal transactions only
            training_results = anomaly_ensemble.train_ensemble(
                X_train, y_train,
                model_types=['isolation_forest', 'one_class_svm', 'local_outlier_factor', 'elliptic_envelope']
            )
            
            # Evaluate model
            evaluation_results = self.evaluator.evaluate_model(
                anomaly_ensemble, X_val, y_val, "anomaly_detection"
            )
            
            # Log training completion
            metrics_logger.log_model_training(
                model_name="anomaly_detection",
                training_time_seconds=time.time(),  # This should be tracked properly
                training_samples=len(X_train[y_train == 0]),  # Only normal transactions
                validation_score=evaluation_results['auc_roc']
            )
            
            # Save model
            model_dir = "/models/anomaly_models"
            os.makedirs(model_dir, exist_ok=True)
            anomaly_ensemble.save_models(model_dir)
            
            return {
                "model": anomaly_ensemble,
                "training_results": training_results,
                "evaluation_results": evaluation_results,
                "model_path": model_dir
            }
            
        except Exception as e:
            logger.error("Anomaly detection training failed", error=str(e))
            raise ModelTrainingException(f"Anomaly detection training failed: {str(e)}")
    
    def train_all_models(
        self,
        data_path: str,
        test_size: float = 0.2,
        val_size: float = 0.2,
        resampling_strategy: str = "smote",
        hyperparameter_tuning: bool = True
    ) -> Dict[str, Any]:
        """Train all fraud detection models"""
        logger.info("Starting comprehensive model training")
        
        start_time = time.time()
        
        try:
            # Load and prepare data
            data = self.load_training_data(data_path)
            X, y = self.prepare_features(data)
            
            # Split data chronologically (important for time-series data)
            # Sort by step to ensure chronological order
            data_sorted = data.sort_values('step')
            X_sorted = X.loc[data_sorted.index]
            y_sorted = y.loc[data_sorted.index]
            
            # Split into train/temp, then temp into val/test
            train_size = 1 - test_size - val_size
            train_end = int(len(X_sorted) * train_size)
            val_end = int(len(X_sorted) * (train_size + val_size))
            
            X_train = X_sorted.iloc[:train_end]
            y_train = y_sorted.iloc[:train_end]
            X_val = X_sorted.iloc[train_end:val_end]
            y_val = y_sorted.iloc[train_end:val_end]
            X_test = X_sorted.iloc[val_end:]
            y_test = y_sorted.iloc[val_end:]
            
            logger.info(
                "Data split completed",
                train_samples=len(X_train),
                val_samples=len(X_val),
                test_samples=len(X_test),
                train_fraud_rate=y_train.mean(),
                val_fraud_rate=y_val.mean(),
                test_fraud_rate=y_test.mean()
            )
            
            # Handle imbalanced data for training set only
            X_train_balanced, y_train_balanced = self.handle_imbalanced_data(
                X_train, y_train, resampling_strategy
            )
            
            results = {}
            
            with mlflow.start_run(run_name="comprehensive_fraud_training"):
                # Log experiment parameters
                mlflow.log_param("data_path", data_path)
                mlflow.log_param("resampling_strategy", resampling_strategy)
                mlflow.log_param("hyperparameter_tuning", hyperparameter_tuning)
                mlflow.log_param("train_samples", len(X_train))
                mlflow.log_param("val_samples", len(X_val))
                mlflow.log_param("test_samples", len(X_test))
                
                # Train ensemble model
                logger.info("Training ensemble model")
                ensemble_results = self.train_ensemble_model(
                    X_train_balanced, y_train_balanced, X_val, y_val, hyperparameter_tuning
                )
                results["ensemble"] = ensemble_results
                
                # Train neural network
                logger.info("Training neural network")
                neural_results = self.train_neural_model(
                    X_train_balanced, y_train_balanced, X_val, y_val, hyperparameter_tuning
                )
                results["neural"] = neural_results
                
                # Train anomaly detection
                logger.info("Training anomaly detection")
                anomaly_results = self.train_anomaly_model(
                    X_train, y_train, X_val, y_val  # Use original unbalanced data
                )
                results["anomaly"] = anomaly_results
                
                # Final evaluation on test set
                logger.info("Performing final evaluation on test set")
                test_results = {}
                
                for model_name, model_results in results.items():
                    model = model_results["model"]
                    test_evaluation = self.evaluator.evaluate_model(
                        model, X_test, y_test, f"{model_name}_test"
                    )
                    test_results[model_name] = test_evaluation
                    
                    # Log test metrics to MLflow
                    for metric_name, value in test_evaluation.items():
                        mlflow.log_metric(f"test_{model_name}_{metric_name}", value)
                
                results["test_results"] = test_results
                
                # Log overall training time
                total_training_time = time.time() - start_time
                mlflow.log_metric("total_training_time_seconds", total_training_time)
                
                logger.info(
                    "Comprehensive model training completed",
                    total_time_seconds=total_training_time,
                    models_trained=len(results) - 1  # Exclude test_results
                )
                
                return results
                
        except Exception as e:
            logger.error("Comprehensive model training failed", error=str(e))
            raise ModelTrainingException(f"Comprehensive model training failed: {str(e)}")
