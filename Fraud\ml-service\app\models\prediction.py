"""
Prediction request and response models
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class TransactionType(str, Enum):
    """Transaction types"""
    PAYMENT = "PAYMENT"
    TRANSFER = "TRANSFER"
    CASH_OUT = "CASH_OUT"
    DEBIT = "DEBIT"
    CASH_IN = "CASH_IN"


class RiskLevel(str, Enum):
    """Risk levels for fraud detection"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class TransactionData(BaseModel):
    """Transaction data for prediction"""
    step: int = Field(..., description="Time step in simulation")
    type: TransactionType = Field(..., description="Transaction type")
    amount: float = Field(..., gt=0, description="Transaction amount")
    nameOrig: str = Field(..., description="Customer ID who started the transaction")
    oldbalanceOrg: float = Field(..., ge=0, description="Initial balance before transaction")
    newbalanceOrig: float = Field(..., ge=0, description="New balance after transaction")
    nameDest: str = Field(..., description="Customer ID who is the recipient")
    oldbalanceDest: float = Field(..., ge=0, description="Initial balance recipient before transaction")
    newbalanceDest: float = Field(..., ge=0, description="New balance recipient after transaction")
    
    @validator('newbalanceOrig')
    def validate_balance_consistency(cls, v, values):
        """Validate balance consistency for originator"""
        if 'oldbalanceOrg' in values and 'amount' in values:
            expected_balance = values['oldbalanceOrg'] - values['amount']
            if abs(v - expected_balance) > 0.01:  # Allow small floating point errors
                # This might be suspicious but not necessarily invalid
                pass
        return v


class PredictionRequest(BaseModel):
    """Request for fraud prediction"""
    transaction_id: str = Field(..., description="Unique transaction identifier")
    transaction_data: TransactionData = Field(..., description="Transaction details")
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow, description="Request timestamp")
    include_explanation: bool = Field(default=False, description="Include prediction explanation")
    model_version: Optional[str] = Field(default=None, description="Specific model version to use")


class ModelExplanation(BaseModel):
    """Model prediction explanation"""
    feature_importance: Dict[str, float] = Field(..., description="Feature importance scores")
    top_features: List[str] = Field(..., description="Top contributing features")
    confidence_interval: Optional[List[float]] = Field(default=None, description="Prediction confidence interval")
    similar_cases: Optional[List[str]] = Field(default=None, description="Similar historical cases")


class PredictionResponse(BaseModel):
    """Response from fraud prediction"""
    transaction_id: str = Field(..., description="Transaction identifier")
    fraud_score: float = Field(..., ge=0, le=1, description="Fraud probability score (0-1)")
    is_fraudulent: bool = Field(..., description="Binary fraud classification")
    risk_level: RiskLevel = Field(..., description="Risk level assessment")
    confidence: float = Field(..., ge=0, le=1, description="Model confidence in prediction")
    model_version: str = Field(..., description="Model version used")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    explanation: Optional[ModelExplanation] = Field(default=None, description="Prediction explanation")
    
    @validator('risk_level', pre=True, always=True)
    def determine_risk_level(cls, v, values):
        """Determine risk level based on fraud score"""
        if v is not None:
            return v
        
        fraud_score = values.get('fraud_score', 0)
        if fraud_score >= 0.8:
            return RiskLevel.CRITICAL
        elif fraud_score >= 0.6:
            return RiskLevel.HIGH
        elif fraud_score >= 0.3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW


class BatchPredictionRequest(BaseModel):
    """Request for batch fraud predictions"""
    transactions: List[PredictionRequest] = Field(..., description="List of transactions to predict")
    batch_id: Optional[str] = Field(default=None, description="Batch identifier")
    include_explanations: bool = Field(default=False, description="Include explanations for all predictions")


class BatchPredictionResponse(BaseModel):
    """Response from batch fraud predictions"""
    batch_id: Optional[str] = Field(default=None, description="Batch identifier")
    results: List[PredictionResponse] = Field(..., description="Prediction results")
    success_count: int = Field(..., description="Number of successful predictions")
    error_count: int = Field(..., description="Number of failed predictions")
    errors: List[Dict[str, Any]] = Field(default=[], description="Error details")
    processing_time_ms: int = Field(..., description="Total processing time in milliseconds")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class ModelInfo(BaseModel):
    """Model information response"""
    model_name: str = Field(..., description="Model name")
    model_version: str = Field(..., description="Model version")
    model_type: str = Field(..., description="Model type (ensemble, neural, etc.)")
    training_date: Optional[datetime] = Field(default=None, description="Model training date")
    features: List[str] = Field(..., description="Model features")
    performance_metrics: Dict[str, float] = Field(..., description="Model performance metrics")
    is_active: bool = Field(..., description="Whether model is currently active")
    load_time: Optional[float] = Field(default=None, description="Model load time in seconds")


class ModelTrainingRequest(BaseModel):
    """Request for model training"""
    model_type: str = Field(..., description="Type of model to train")
    training_data_path: str = Field(..., description="Path to training data")
    validation_split: float = Field(default=0.2, ge=0, le=1, description="Validation split ratio")
    hyperparameters: Optional[Dict[str, Any]] = Field(default=None, description="Model hyperparameters")
    experiment_name: Optional[str] = Field(default=None, description="MLflow experiment name")


class ModelTrainingResponse(BaseModel):
    """Response from model training"""
    training_id: str = Field(..., description="Training job identifier")
    model_version: str = Field(..., description="New model version")
    training_metrics: Dict[str, float] = Field(..., description="Training metrics")
    validation_metrics: Dict[str, float] = Field(..., description="Validation metrics")
    training_time_seconds: float = Field(..., description="Training time in seconds")
    model_path: str = Field(..., description="Path to saved model")
    status: str = Field(..., description="Training status")


class FeatureImportance(BaseModel):
    """Feature importance information"""
    feature_name: str = Field(..., description="Feature name")
    importance_score: float = Field(..., description="Importance score")
    rank: int = Field(..., description="Feature rank by importance")


class ModelPerformanceMetrics(BaseModel):
    """Model performance metrics"""
    accuracy: float = Field(..., ge=0, le=1, description="Model accuracy")
    precision: float = Field(..., ge=0, le=1, description="Precision score")
    recall: float = Field(..., ge=0, le=1, description="Recall score")
    f1_score: float = Field(..., ge=0, le=1, description="F1 score")
    auc_roc: float = Field(..., ge=0, le=1, description="AUC-ROC score")
    auc_pr: float = Field(..., ge=0, le=1, description="AUC-PR score")
    confusion_matrix: List[List[int]] = Field(..., description="Confusion matrix")
    feature_importance: List[FeatureImportance] = Field(..., description="Feature importance rankings")
