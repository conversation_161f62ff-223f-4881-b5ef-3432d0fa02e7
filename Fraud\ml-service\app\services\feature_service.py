"""
Feature service for fraud detection
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
import httpx
import redis
from datetime import datetime, timedelta

from ..core.config import settings
from ..core.logging import get_logger
from ..core.exceptions import FeatureExtractionException, FeatureStoreException
from ..models.prediction import PredictionRequest, TransactionData
from ..ml.feature_engineering.feature_extractor import FraudFeatureExtractor

logger = get_logger(__name__)


class FeatureService:
    """Service for extracting and managing features for fraud detection"""
    
    def __init__(self):
        self.feature_extractor = FraudFeatureExtractor()
        self.redis_client = None
        self.feature_store_client = None
        self.is_initialized = False
        
        # Performance tracking
        self.feature_extraction_count = 0
        self.total_extraction_time = 0.0
        self.cache_hits = 0
        self.cache_misses = 0
    
    async def initialize(self):
        """Initialize the feature service"""
        logger.info("Initializing feature service")
        
        try:
            # Initialize Redis client for caching
            await self._initialize_redis()
            
            # Initialize feature store client
            await self._initialize_feature_store_client()
            
            self.is_initialized = True
            
            logger.info("Feature service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize feature service", error=str(e))
            raise FeatureStoreException(f"Feature service initialization failed: {str(e)}")
    
    async def _initialize_redis(self):
        """Initialize Redis client for feature caching"""
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # Test connection
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.ping
            )
            
            logger.info("Redis client initialized successfully")
            
        except Exception as e:
            logger.warning("Failed to initialize Redis client", error=str(e))
            self.redis_client = None
    
    async def _initialize_feature_store_client(self):
        """Initialize HTTP client for feature store"""
        try:
            self.feature_store_client = httpx.AsyncClient(
                base_url=settings.FEATURE_STORE_URL,
                timeout=httpx.Timeout(10.0)
            )
            
            # Test connection
            response = await self.feature_store_client.get("/health")
            if response.status_code == 200:
                logger.info("Feature store client initialized successfully")
            else:
                logger.warning("Feature store health check failed", status_code=response.status_code)
                
        except Exception as e:
            logger.warning("Failed to initialize feature store client", error=str(e))
            self.feature_store_client = None
    
    async def extract_features(
        self,
        request: PredictionRequest,
        use_cache: bool = True,
        include_historical: bool = True
    ) -> Dict[str, float]:
        """Extract features for a transaction"""
        
        if not self.is_initialized:
            raise FeatureExtractionException("Feature service not initialized")
        
        start_time = time.time()
        transaction_id = request.transaction_id
        
        try:
            # Check cache first
            if use_cache and self.redis_client:
                cached_features = await self._get_cached_features(transaction_id)
                if cached_features:
                    self.cache_hits += 1
                    logger.debug("Features retrieved from cache", transaction_id=transaction_id)
                    return cached_features
                else:
                    self.cache_misses += 1
            
            # Extract basic features
            basic_features = self.feature_extractor.extract_features(
                request.transaction_data,
                include_historical=False,  # Will be enriched separately
                include_derived=True
            )
            
            # Enrich with historical features if requested
            if include_historical:
                historical_features = await self._get_historical_features(request.transaction_data)
                basic_features.update(historical_features)
            
            # Validate and clean features
            features = self.feature_extractor.validate_features(basic_features)
            
            # Cache features
            if use_cache and self.redis_client:
                await self._cache_features(transaction_id, features)
            
            # Update metrics
            self.feature_extraction_count += 1
            extraction_time = (time.time() - start_time) * 1000
            self.total_extraction_time += extraction_time
            
            logger.debug(
                "Features extracted successfully",
                transaction_id=transaction_id,
                feature_count=len(features),
                extraction_time_ms=extraction_time
            )
            
            return features
            
        except Exception as e:
            logger.error(
                "Feature extraction failed",
                transaction_id=transaction_id,
                error=str(e)
            )
            raise FeatureExtractionException(
                f"Feature extraction failed: {str(e)}",
                transaction_id
            )
    
    async def _get_cached_features(self, transaction_id: str) -> Optional[Dict[str, float]]:
        """Get features from cache"""
        try:
            cache_key = f"features:{transaction_id}"
            
            cached_data = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hgetall, cache_key
            )
            
            if cached_data:
                # Convert string values back to float
                return {k: float(v) for k, v in cached_data.items()}
            
            return None
            
        except Exception as e:
            logger.warning("Failed to get cached features", error=str(e))
            return None
    
    async def _cache_features(self, transaction_id: str, features: Dict[str, float]):
        """Cache features in Redis"""
        try:
            cache_key = f"features:{transaction_id}"
            
            # Convert features to string format for Redis
            string_features = {k: str(v) for k, v in features.items()}
            
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.hset, cache_key, string_features
            )
            
            # Set expiration
            await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.expire, cache_key, settings.FEATURE_CACHE_TTL
            )
            
        except Exception as e:
            logger.warning("Failed to cache features", error=str(e))
    
    async def _get_historical_features(self, transaction: TransactionData) -> Dict[str, float]:
        """Get historical features from feature store"""
        historical_features = {}
        
        try:
            if self.feature_store_client:
                # Request historical features for originator
                orig_features = await self._request_account_features(
                    transaction.nameOrig,
                    transaction.step
                )
                
                # Request historical features for destination
                dest_features = await self._request_account_features(
                    transaction.nameDest,
                    transaction.step
                )
                
                # Merge features with prefixes
                for key, value in orig_features.items():
                    historical_features[f"orig_{key}"] = value
                
                for key, value in dest_features.items():
                    historical_features[f"dest_{key}"] = value
                
                # Get transaction type statistics
                type_features = await self._request_transaction_type_features(
                    transaction.type,
                    transaction.amount
                )
                historical_features.update(type_features)
                
            else:
                # Fallback to default values if feature store unavailable
                historical_features = self._get_default_historical_features()
                
        except Exception as e:
            logger.warning("Failed to get historical features", error=str(e))
            # Return default values on error
            historical_features = self._get_default_historical_features()
        
        return historical_features
    
    async def _request_account_features(
        self,
        account_id: str,
        current_step: int
    ) -> Dict[str, float]:
        """Request account-specific features from feature store"""
        try:
            if not self.feature_store_client:
                return {}
            
            # Calculate time windows
            time_windows = {
                '1h': current_step - 1,
                '24h': current_step - 24,
                '7d': current_step - 168  # 7 days * 24 hours
            }
            
            features = {}
            
            for window_name, start_step in time_windows.items():
                response = await self.feature_store_client.get(
                    f"/features/account/{account_id}",
                    params={
                        'start_step': max(0, start_step),
                        'end_step': current_step,
                        'window': window_name
                    }
                )
                
                if response.status_code == 200:
                    window_features = response.json()
                    for key, value in window_features.items():
                        features[f"{key}_{window_name}"] = float(value)
                else:
                    # Set default values for this window
                    default_features = {
                        f"transaction_count_{window_name}": 0.0,
                        f"total_amount_{window_name}": 0.0,
                        f"avg_amount_{window_name}": 0.0,
                        f"unique_destinations_{window_name}": 0.0
                    }
                    features.update(default_features)
            
            return features
            
        except Exception as e:
            logger.warning(f"Failed to get account features for {account_id}", error=str(e))
            return {}
    
    async def _request_transaction_type_features(
        self,
        transaction_type: str,
        amount: float
    ) -> Dict[str, float]:
        """Request transaction type specific features"""
        try:
            if not self.feature_store_client:
                return {}
            
            response = await self.feature_store_client.get(
                f"/features/transaction_type/{transaction_type}",
                params={'amount': amount}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return self._get_default_transaction_type_features()
                
        except Exception as e:
            logger.warning(f"Failed to get transaction type features", error=str(e))
            return self._get_default_transaction_type_features()
    
    def _get_default_historical_features(self) -> Dict[str, float]:
        """Get default historical features when feature store is unavailable"""
        return {
            'orig_transaction_count_1h': 0.0,
            'orig_transaction_count_24h': 0.0,
            'orig_transaction_count_7d': 0.0,
            'orig_total_amount_1h': 0.0,
            'orig_total_amount_24h': 0.0,
            'orig_total_amount_7d': 0.0,
            'orig_avg_amount_1h': 0.0,
            'orig_avg_amount_24h': 0.0,
            'orig_avg_amount_7d': 0.0,
            'orig_unique_destinations_1h': 0.0,
            'orig_unique_destinations_24h': 0.0,
            'orig_unique_destinations_7d': 0.0,
            'dest_transaction_count_1h': 0.0,
            'dest_transaction_count_24h': 0.0,
            'dest_transaction_count_7d': 0.0,
            'dest_total_amount_1h': 0.0,
            'dest_total_amount_24h': 0.0,
            'dest_total_amount_7d': 0.0,
            'dest_avg_amount_1h': 0.0,
            'dest_avg_amount_24h': 0.0,
            'dest_avg_amount_7d': 0.0
        }
    
    def _get_default_transaction_type_features(self) -> Dict[str, float]:
        """Get default transaction type features"""
        return {
            'type_avg_amount': 0.0,
            'type_fraud_rate': 0.0,
            'amount_percentile_in_type': 0.5
        }
    
    async def get_feature_stats(self) -> Dict[str, Any]:
        """Get feature service statistics"""
        total_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = self.cache_hits / total_requests if total_requests > 0 else 0.0
        
        avg_extraction_time = (
            self.total_extraction_time / self.feature_extraction_count
            if self.feature_extraction_count > 0 else 0.0
        )
        
        return {
            "feature_extraction_count": self.feature_extraction_count,
            "avg_extraction_time_ms": avg_extraction_time,
            "cache_hit_rate": cache_hit_rate,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "redis_available": self.redis_client is not None,
            "feature_store_available": self.feature_store_client is not None,
            "feature_count": len(self.feature_extractor.get_feature_names()),
            "is_initialized": self.is_initialized
        }
    
    async def is_ready(self) -> bool:
        """Check if feature service is ready"""
        return self.is_initialized
    
    async def cleanup(self):
        """Cleanup feature service"""
        logger.info("Cleaning up feature service")
        
        if self.feature_store_client:
            await self.feature_store_client.aclose()
        
        if self.redis_client:
            self.redis_client.close()
        
        self.is_initialized = False
        
        logger.info("Feature service cleanup completed")
