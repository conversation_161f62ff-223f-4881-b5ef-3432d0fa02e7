#!/usr/bin/env python3
"""
Test script for ML service functionality
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.models.prediction import TransactionData, PredictionRequest, TransactionType
from app.ml.feature_engineering.feature_extractor import FraudFeatureExtractor
from app.ml.algorithms.ensemble_models import EnsembleModelManager
from app.ml.algorithms.neural_networks import FraudDetectionNN
from app.ml.algorithms.anomaly_detection import AnomalyDetectionEnsemble

# Setup logging
setup_logging()
logger = get_logger(__name__)


def create_sample_data(n_samples: int = 1000) -> pd.DataFrame:
    """Create sample fraud detection data for testing"""
    
    np.random.seed(42)
    
    data = []
    
    for i in range(n_samples):
        # Create realistic transaction data
        transaction_type = np.random.choice(['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT', 'CASH_IN'])
        
        # Normal transactions
        if np.random.random() > 0.1:  # 90% normal
            amount = np.random.lognormal(mean=6, sigma=1.5)  # Realistic amounts
            old_balance_org = np.random.lognormal(mean=8, sigma=2)
            new_balance_orig = max(0, old_balance_org - amount)
            old_balance_dest = np.random.lognormal(mean=7, sigma=1.8)
            new_balance_dest = old_balance_dest + amount
            is_fraud = 0
        else:  # 10% fraud
            amount = np.random.lognormal(mean=8, sigma=2)  # Larger amounts for fraud
            old_balance_org = np.random.lognormal(mean=9, sigma=1.5)
            
            # Suspicious patterns for fraud
            if np.random.random() > 0.5:
                # Account emptying
                new_balance_orig = 0
                old_balance_dest = 0
                new_balance_dest = 0  # Money disappears
            else:
                new_balance_orig = max(0, old_balance_org - amount)
                old_balance_dest = np.random.lognormal(mean=6, sigma=2)
                new_balance_dest = old_balance_dest + amount
            
            is_fraud = 1
        
        data.append({
            'step': i + 1,
            'type': transaction_type,
            'amount': amount,
            'nameOrig': f'C{i:09d}',
            'oldbalanceOrg': old_balance_org,
            'newbalanceOrig': new_balance_orig,
            'nameDest': f'M{i:09d}' if np.random.random() > 0.7 else f'C{i+1000:09d}',
            'oldbalanceDest': old_balance_dest,
            'newbalanceDest': new_balance_dest,
            'isFraud': is_fraud
        })
    
    return pd.DataFrame(data)


def test_feature_extraction():
    """Test feature extraction functionality"""
    
    logger.info("Testing feature extraction...")
    
    try:
        # Create feature extractor
        extractor = FraudFeatureExtractor()
        
        # Create sample transaction
        transaction = TransactionData(
            step=1,
            type=TransactionType.TRANSFER,
            amount=10000.0,
            nameOrig="C123456789",
            oldbalanceOrg=50000.0,
            newbalanceOrig=40000.0,
            nameDest="M987654321",
            oldbalanceDest=0.0,
            newbalanceDest=10000.0
        )
        
        # Extract features
        features = extractor.extract_features(transaction)
        
        logger.info(f"Extracted {len(features)} features")
        logger.info(f"Sample features: {list(features.keys())[:10]}")
        
        # Validate features
        validated_features = extractor.validate_features(features)
        
        assert len(validated_features) > 0, "No features extracted"
        assert all(isinstance(v, (int, float)) for v in validated_features.values()), "Invalid feature types"
        
        logger.info("✅ Feature extraction test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Feature extraction test failed: {e}")
        return False


def test_ensemble_model():
    """Test ensemble model functionality"""
    
    logger.info("Testing ensemble model...")
    
    try:
        # Create sample data
        data = create_sample_data(500)  # Smaller dataset for testing
        
        # Prepare features
        extractor = FraudFeatureExtractor()
        features_list = []
        
        for _, row in data.iterrows():
            transaction = TransactionData(
                step=int(row['step']),
                type=TransactionType(row['type']),
                amount=float(row['amount']),
                nameOrig=str(row['nameOrig']),
                oldbalanceOrg=float(row['oldbalanceOrg']),
                newbalanceOrig=float(row['newbalanceOrig']),
                nameDest=str(row['nameDest']),
                oldbalanceDest=float(row['oldbalanceDest']),
                newbalanceDest=float(row['newbalanceDest'])
            )
            
            features = extractor.extract_features(transaction, include_historical=False)
            features_list.append(features)
        
        X = pd.DataFrame(features_list)
        y = data['isFraud']
        
        # Split data
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # Create and train ensemble
        ensemble = EnsembleModelManager()
        
        # Train with limited models for testing
        training_results = ensemble.train_ensemble(
            X_train, y_train, X_test, y_test,
            model_types=['random_forest']  # Just one model for testing
        )
        
        # Test prediction
        predictions = ensemble.predict_proba(X_test)
        
        assert len(predictions) == len(X_test), "Prediction length mismatch"
        assert all(0 <= p <= 1 for p in predictions), "Invalid prediction probabilities"
        
        logger.info(f"✅ Ensemble model test passed - AUC: {training_results}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Ensemble model test failed: {e}")
        return False


def test_neural_network():
    """Test neural network functionality"""
    
    logger.info("Testing neural network...")
    
    try:
        # Create sample data
        data = create_sample_data(300)  # Smaller dataset for testing
        
        # Prepare features (simplified)
        X = data[['amount', 'oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest']].copy()
        
        # Add some engineered features
        X['amount_log'] = np.log1p(X['amount'])
        X['balance_ratio'] = X['amount'] / (X['oldbalanceOrg'] + 1)
        X['type_TRANSFER'] = (data['type'] == 'TRANSFER').astype(int)
        
        y = data['isFraud']
        
        # Split data
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # Create and train neural network
        nn_model = FraudDetectionNN()
        
        # Train with minimal epochs for testing
        training_results = nn_model.train(
            X_train, y_train, X_test, y_test,
            epochs=5,  # Minimal for testing
            batch_size=32
        )
        
        # Test prediction
        predictions = nn_model.predict_proba(X_test)
        
        assert len(predictions) == len(X_test), "Prediction length mismatch"
        assert all(0 <= p <= 1 for p in predictions), "Invalid prediction probabilities"
        
        logger.info(f"✅ Neural network test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Neural network test failed: {e}")
        return False


def test_anomaly_detection():
    """Test anomaly detection functionality"""
    
    logger.info("Testing anomaly detection...")
    
    try:
        # Create sample data
        data = create_sample_data(300)
        
        # Prepare features (simplified)
        X = data[['amount', 'oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest']].copy()
        X['amount_log'] = np.log1p(X['amount'])
        X['balance_ratio'] = X['amount'] / (X['oldbalanceOrg'] + 1)
        
        y = data['isFraud']
        
        # Split data
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # Create and train anomaly detection ensemble
        anomaly_ensemble = AnomalyDetectionEnsemble()
        
        # Train with limited models for testing
        training_results = anomaly_ensemble.train_ensemble(
            X_train, y_train,
            model_types=['isolation_forest']  # Just one model for testing
        )
        
        # Test prediction
        predictions = anomaly_ensemble.predict_proba(X_test)
        
        assert len(predictions) == len(X_test), "Prediction length mismatch"
        assert all(0 <= p <= 1 for p in predictions), "Invalid prediction probabilities"
        
        logger.info(f"✅ Anomaly detection test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Anomaly detection test failed: {e}")
        return False


async def test_api_endpoints():
    """Test API endpoints (if service is running)"""
    
    logger.info("Testing API endpoints...")
    
    try:
        import httpx
        
        # Test health endpoint
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8001/health")
            
            if response.status_code == 200:
                logger.info("✅ Health endpoint test passed")
                
                # Test prediction endpoint
                prediction_request = {
                    "transaction_id": "test_123",
                    "transaction_data": {
                        "step": 1,
                        "type": "TRANSFER",
                        "amount": 10000.0,
                        "nameOrig": "C123456789",
                        "oldbalanceOrg": 50000.0,
                        "newbalanceOrig": 40000.0,
                        "nameDest": "M987654321",
                        "oldbalanceDest": 0.0,
                        "newbalanceDest": 10000.0
                    }
                }
                
                pred_response = await client.post(
                    "http://localhost:8001/predict",
                    json=prediction_request
                )
                
                if pred_response.status_code == 200:
                    result = pred_response.json()
                    logger.info(f"✅ Prediction endpoint test passed - Score: {result.get('fraud_score', 'N/A')}")
                    return True
                else:
                    logger.warning(f"Prediction endpoint returned {pred_response.status_code}")
                    return False
            else:
                logger.warning(f"Health endpoint returned {response.status_code}")
                return False
                
    except Exception as e:
        logger.warning(f"API endpoint test skipped (service not running): {e}")
        return False


def main():
    """Run all tests"""
    
    logger.info("🚀 Starting ML Service Tests")
    logger.info("=" * 50)
    
    test_results = []
    
    # Run tests
    test_results.append(("Feature Extraction", test_feature_extraction()))
    test_results.append(("Ensemble Model", test_ensemble_model()))
    test_results.append(("Neural Network", test_neural_network()))
    test_results.append(("Anomaly Detection", test_anomaly_detection()))
    
    # Test API endpoints if service is running
    api_result = asyncio.run(test_api_endpoints())
    test_results.append(("API Endpoints", api_result))
    
    # Print results
    logger.info("=" * 50)
    logger.info("📊 Test Results Summary")
    logger.info("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    logger.info("=" * 50)
    logger.info(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All tests passed! ML Service is working correctly.")
        return 0
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
