# MLOps Implementation Guide

This guide provides step-by-step instructions for implementing the complete MLOps and CI/CD framework for the FraudShield fraud detection system.

## 🚀 Quick Start

### Prerequisites
- Kubernetes cluster (1.24+)
- Helm 3.8+
- Docker with BuildKit
- Python 3.11+
- Git with LFS support
- MLflow server
- Container registry access

### 1. Environment Setup

#### Install Required Tools
```bash
# Install DVC for data versioning
pip install dvc[s3] dvc[azure] dvc[gcs]

# Install MLflow
pip install mlflow

# Install Kubernetes tools
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install Helm
curl https://get.helm.sh/helm-v3.13.0-linux-amd64.tar.gz | tar xz
sudo mv linux-amd64/helm /usr/local/bin/
```

#### Configure Environment Variables
```bash
# MLflow configuration
export MLFLOW_TRACKING_URI=https://mlflow.fraudshield.example.com
export MLFLOW_S3_ENDPOINT_URL=https://s3.amazonaws.com
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key

# Container registry
export REGISTRY=ghcr.io/fraudshield
export GITHUB_TOKEN=your_github_token

# Kubernetes
export KUBECONFIG=~/.kube/config
```

### 2. Data Pipeline Setup

#### Initialize DVC
```bash
# Initialize DVC in your project
dvc init

# Add remote storage
dvc remote add -d myremote s3://fraudshield-data-bucket/dvc-storage
dvc remote modify myremote region us-east-1

# Configure DVC
dvc config core.analytics false
dvc config core.check_update false
```

#### Setup Data Pipeline
```bash
# Create data directories
mkdir -p data/{raw,validated,features,processed}
mkdir -p models artifacts metrics reports

# Run data pipeline
dvc repro mlops/data-pipeline/dvc.yaml

# Track data with DVC
dvc add data/raw/transactions.csv
dvc add data/processed/training_data.csv
dvc push
```

### 3. MLflow Setup

#### Deploy MLflow Server
```bash
# Using Docker Compose
cd mlops/deployment
docker-compose -f docker-compose.mlflow.yml up -d

# Or using Kubernetes
helm repo add community-charts https://community-charts.github.io/helm-charts
helm install mlflow community-charts/mlflow \
  --namespace mlflow \
  --create-namespace \
  --set tracking.service.type=LoadBalancer
```

#### Configure MLflow
```bash
# Setup MLflow configuration
python mlops/config/mlflow_config.py --setup --config config/mlflow_config.yaml

# Test connection
python mlops/config/mlflow_config.py --test
```

### 4. Model Training Pipeline

#### Configure Training
```bash
# Create training configuration
cat > config/training_config.json << EOF
{
  "data_path": "data/processed/training_data.csv",
  "model_name": "fraud-detection",
  "algorithms": ["random_forest", "gradient_boosting"],
  "hyperparameter_tuning": {
    "n_trials": 50,
    "optimization_metric": "roc_auc"
  }
}
EOF
```

#### Run Training Pipeline
```bash
# Train models
python mlops/scripts/train_model.py \
  --config config/training_config.json \
  --experiment-name fraud-detection-production

# Validate models
python mlops/scripts/validate_model.py \
  --run-id <run_id_from_training> \
  --config config/validation_config.json

# Compare models
python mlops/scripts/compare_models.py \
  --challenger-run-id <new_run_id> \
  --champion-run-id <current_production_run_id>
```

### 5. Model Deployment

#### Setup Kubernetes Deployment
```bash
# Install FraudShield Helm chart
helm repo add fraudshield ./infrastructure/helm
helm install fraudshield fraudshield/fraudshield \
  --namespace fraudshield \
  --create-namespace \
  --values infrastructure/helm/fraudshield/values.yaml

# Verify deployment
kubectl get pods -n fraudshield
kubectl get services -n fraudshield
```

#### Deploy Model with Blue/Green Strategy
```bash
# Deploy new model version
python mlops/deployment/model_deployer.py \
  --model-name fraud-detection \
  --model-version 2 \
  --strategy blue_green \
  --config config/deployment_config.json
```

#### Deploy Model with Canary Strategy
```bash
# Deploy with canary strategy
python mlops/deployment/model_deployer.py \
  --model-name fraud-detection \
  --model-version 2 \
  --strategy canary \
  --config config/deployment_config.json
```

### 6. Monitoring Setup

#### Deploy Monitoring Stack
```bash
# Install Prometheus and Grafana
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add grafana https://grafana.github.io/helm-charts

helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace

# Configure model monitoring
python mlops/monitoring/model_monitor.py \
  --config config/monitoring_config.json
```

#### Setup Alerts
```bash
# Configure alert rules
kubectl apply -f mlops/monitoring/alert-rules.yaml

# Setup notification channels
kubectl create secret generic slack-webhook \
  --from-literal=url=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK \
  -n monitoring
```

### 7. CI/CD Pipeline Setup

#### Configure GitHub Actions
```bash
# Setup repository secrets
gh secret set DOCKER_USERNAME --body "your_username"
gh secret set DOCKER_PASSWORD --body "your_password"
gh secret set KUBE_CONFIG_STAGING --body "$(cat ~/.kube/config | base64)"
gh secret set KUBE_CONFIG_PRODUCTION --body "$(cat ~/.kube/config | base64)"
gh secret set MLFLOW_TRACKING_URI --body "https://mlflow.fraudshield.example.com"
gh secret set AWS_ACCESS_KEY_ID --body "your_access_key"
gh secret set AWS_SECRET_ACCESS_KEY --body "your_secret_key"
```

#### Trigger Pipeline
```bash
# Push code to trigger CI/CD
git add .
git commit -m "feat: implement MLOps pipeline"
git push origin main

# Manual trigger with specific parameters
gh workflow run mlops-ci-cd.yml \
  -f deploy_environment=staging \
  -f retrain_model=true
```

## 🔧 Advanced Configuration

### Model Versioning Strategy

#### Semantic Versioning
```bash
# Major version: Breaking changes in model interface
# Minor version: New features or significant improvements
# Patch version: Bug fixes or minor improvements

# Example: v2.1.3
# - Major: 2 (new model architecture)
# - Minor: 1 (added new features)
# - Patch: 3 (bug fixes)
```

#### Model Lifecycle Management
```python
# Promote model through stages
from mlops.config.mlflow_config import create_mlflow_config

mlflow_config = create_mlflow_config()

# Development -> Staging
mlflow_config.transition_model_stage(
    model_name="fraud-detection",
    version="3",
    stage="Staging"
)

# Staging -> Production (after validation)
mlflow_config.transition_model_stage(
    model_name="fraud-detection",
    version="3",
    stage="Production"
)
```

### Data Drift Monitoring

#### Configure Drift Detection
```yaml
# config/drift_monitoring.yaml
drift_detection:
  enabled: true
  schedule: "0 */6 * * *"  # Every 6 hours
  
  methods:
    - kolmogorov_smirnov
    - population_stability_index
    - jensen_shannon_divergence
  
  thresholds:
    warning: 0.1
    critical: 0.3
  
  features:
    - amount
    - transaction_type
    - merchant_category
    - time_features
```

#### Setup Drift Alerts
```python
# mlops/monitoring/drift_alerts.py
from mlops.monitoring.model_monitor import ModelMonitor

monitor = ModelMonitor(config)

# Configure drift alerts
monitor.add_drift_alert(
    feature_name="amount",
    threshold=0.2,
    severity="high"
)
```

### A/B Testing Framework

#### Configure A/B Testing
```yaml
# config/ab_testing.yaml
ab_testing:
  enabled: true
  
  experiments:
    - name: "model_v2_vs_v1"
      traffic_split:
        control: 0.8  # 80% to current model
        treatment: 0.2  # 20% to new model
      
      success_metrics:
        - precision
        - recall
        - false_positive_rate
      
      duration_days: 14
      min_sample_size: 10000
```

#### Implement A/B Testing
```python
# ml-service/src/ab_testing.py
class ABTestingManager:
    def get_model_version(self, user_id: str) -> str:
        # Determine which model version to use
        hash_value = hash(user_id) % 100
        
        if hash_value < 20:  # 20% traffic
            return "treatment"
        else:
            return "control"
```

### Performance Optimization

#### Model Serving Optimization
```yaml
# config/serving_optimization.yaml
serving:
  batching:
    enabled: true
    max_batch_size: 32
    timeout_ms: 100
  
  caching:
    enabled: true
    ttl_seconds: 300
    max_size: 10000
  
  model_loading:
    lazy_loading: true
    memory_mapping: true
    quantization: int8
```

#### Resource Optimization
```yaml
# infrastructure/k8s/optimization.yaml
resources:
  ml_service:
    requests:
      cpu: "1"
      memory: "2Gi"
    limits:
      cpu: "4"
      memory: "8Gi"
  
  autoscaling:
    min_replicas: 3
    max_replicas: 20
    target_cpu: 70
    target_memory: 80
    
    custom_metrics:
      - name: ml_requests_per_second
        target: 100
```

## 📊 Monitoring and Observability

### Key Metrics to Monitor

#### Model Performance Metrics
- **Accuracy**: Overall prediction accuracy
- **Precision**: True positives / (True positives + False positives)
- **Recall**: True positives / (True positives + False negatives)
- **F1-Score**: Harmonic mean of precision and recall
- **ROC-AUC**: Area under the ROC curve
- **False Positive Rate**: False positives / (False positives + True negatives)

#### Operational Metrics
- **Prediction Latency**: Time to generate predictions
- **Throughput**: Predictions per second
- **Error Rate**: Percentage of failed predictions
- **Resource Utilization**: CPU, memory, GPU usage
- **Queue Length**: Pending prediction requests

#### Data Quality Metrics
- **Data Completeness**: Percentage of non-null values
- **Data Freshness**: Time since last data update
- **Schema Compliance**: Adherence to expected data schema
- **Feature Distribution**: Statistical properties of features

### Dashboard Configuration

#### Grafana Dashboards
```json
{
  "dashboard": {
    "title": "FraudShield MLOps Dashboard",
    "panels": [
      {
        "title": "Model Performance",
        "type": "stat",
        "targets": [
          {
            "expr": "ml_model_accuracy",
            "legendFormat": "Accuracy"
          }
        ]
      },
      {
        "title": "Prediction Latency",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, ml_prediction_latency_seconds_bucket)",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### Model Training Issues
```bash
# Issue: Out of memory during training
# Solution: Reduce batch size or use gradient accumulation
python mlops/scripts/train_model.py \
  --config config/training_config.json \
  --batch-size 16 \
  --gradient-accumulation-steps 2

# Issue: MLflow connection timeout
# Solution: Check MLflow server status and network connectivity
curl -f http://mlflow.fraudshield.example.com/health
kubectl logs -n mlflow deployment/mlflow-tracking
```

#### Deployment Issues
```bash
# Issue: Pod stuck in Pending state
# Solution: Check resource availability and node selectors
kubectl describe pod <pod-name> -n fraudshield
kubectl get nodes -o wide

# Issue: Model loading timeout
# Solution: Increase startup probe timeout
kubectl patch deployment fraudshield-ml-service \
  -p '{"spec":{"template":{"spec":{"containers":[{"name":"ml-service","startupProbe":{"timeoutSeconds":30}}]}}}}'
```

#### Monitoring Issues
```bash
# Issue: Missing metrics in Prometheus
# Solution: Check service monitor configuration
kubectl get servicemonitor -n fraudshield
kubectl logs -n monitoring prometheus-operator

# Issue: Grafana dashboard not loading
# Solution: Check data source configuration
kubectl exec -n monitoring grafana-0 -- grafana-cli admin reset-admin-password admin
```

## 📚 Best Practices

### Model Development
1. **Version Control**: Use Git for code and DVC for data
2. **Reproducibility**: Pin dependencies and use fixed random seeds
3. **Testing**: Implement unit tests for all model components
4. **Documentation**: Maintain comprehensive model documentation

### Deployment
1. **Blue/Green Deployments**: Use for zero-downtime updates
2. **Canary Releases**: Gradually roll out new models
3. **Health Checks**: Implement comprehensive health checks
4. **Rollback Strategy**: Have automated rollback procedures

### Monitoring
1. **Real-time Monitoring**: Monitor model performance in real-time
2. **Alerting**: Set up alerts for critical metrics
3. **Logging**: Comprehensive logging for debugging
4. **Dashboards**: Create intuitive monitoring dashboards

### Security
1. **Secrets Management**: Use Kubernetes secrets for sensitive data
2. **Network Policies**: Implement network segmentation
3. **RBAC**: Use role-based access control
4. **Image Scanning**: Scan container images for vulnerabilities

This implementation guide provides a comprehensive framework for deploying and managing the FraudShield MLOps pipeline. Follow the steps sequentially and adapt the configuration to your specific environment and requirements.
