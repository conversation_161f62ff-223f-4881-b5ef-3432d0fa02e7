# DVC Pipeline Configuration
# Defines the complete data and ML pipeline with dependencies

stages:
  # Data ingestion stage
  data_ingestion:
    cmd: python scripts/data_ingestion.py
    deps:
    - scripts/data_ingestion.py
    - config/data_sources.yaml
    outs:
    - data/raw/transactions.csv
    - data/raw/metadata.json
    params:
    - data_ingestion.batch_size
    - data_ingestion.date_range
    metrics:
    - metrics/data_ingestion.json

  # Data validation stage
  data_validation:
    cmd: python scripts/data_validation.py
    deps:
    - scripts/data_validation.py
    - data/raw/transactions.csv
    - config/data_validation_rules.yaml
    outs:
    - data/validated/transactions.csv
    - reports/data_validation_report.html
    params:
    - data_validation.quality_threshold
    - data_validation.completeness_threshold
    metrics:
    - metrics/data_validation.json

  # Feature engineering stage
  feature_engineering:
    cmd: python scripts/feature_engineering.py
    deps:
    - scripts/feature_engineering.py
    - data/validated/transactions.csv
    - ml-service/src/feature_engineering.py
    outs:
    - data/features/training_features.csv
    - data/features/feature_metadata.json
    params:
    - feature_engineering.window_sizes
    - feature_engineering.aggregation_functions
    metrics:
    - metrics/feature_engineering.json

  # Data preprocessing stage
  data_preprocessing:
    cmd: python scripts/data_preprocessing.py
    deps:
    - scripts/data_preprocessing.py
    - data/features/training_features.csv
    - ml-service/src/data_preprocessing.py
    outs:
    - data/processed/training_data.csv
    - data/processed/validation_data.csv
    - data/processed/test_data.csv
    - artifacts/preprocessor.pkl
    params:
    - data_preprocessing.test_size
    - data_preprocessing.validation_size
    - data_preprocessing.random_state
    metrics:
    - metrics/data_preprocessing.json

  # Model training stage
  model_training:
    cmd: python mlops/scripts/train_model.py --config config/training_config.json
    deps:
    - mlops/scripts/train_model.py
    - data/processed/training_data.csv
    - ml-service/src/feature_engineering.py
    - ml-service/src/data_preprocessing.py
    - ml-service/src/model_evaluation.py
    - config/training_config.json
    outs:
    - models/fraud_detection_model.pkl
    - models/model_metadata.json
    - artifacts/feature_engineer.pkl
    params:
    - model_training.algorithms
    - model_training.hyperparameter_tuning.n_trials
    - model_training.cross_validation.folds
    metrics:
    - metrics/model_training.json

  # Model validation stage
  model_validation:
    cmd: python mlops/scripts/validate_model.py --run-id ${model_training.run_id}
    deps:
    - mlops/scripts/validate_model.py
    - data/processed/validation_data.csv
    - models/fraud_detection_model.pkl
    - config/validation_config.json
    outs:
    - reports/model_validation_report.json
    - reports/model_validation_plots/
    params:
    - model_validation.performance_thresholds
    - model_validation.fairness_checks
    metrics:
    - metrics/model_validation.json

  # Model evaluation stage
  model_evaluation:
    cmd: python scripts/model_evaluation.py
    deps:
    - scripts/model_evaluation.py
    - data/processed/test_data.csv
    - models/fraud_detection_model.pkl
    - artifacts/preprocessor.pkl
    outs:
    - reports/model_evaluation_report.html
    - reports/confusion_matrix.png
    - reports/roc_curve.png
    - reports/feature_importance.png
    params:
    - model_evaluation.metrics
    - model_evaluation.visualization
    metrics:
    - metrics/model_evaluation.json

  # Model comparison stage
  model_comparison:
    cmd: python scripts/model_comparison.py
    deps:
    - scripts/model_comparison.py
    - models/fraud_detection_model.pkl
    - metrics/model_training.json
    - metrics/model_validation.json
    - metrics/model_evaluation.json
    outs:
    - reports/model_comparison_report.html
    - models/champion_model.pkl
    params:
    - model_comparison.comparison_metrics
    - model_comparison.selection_criteria
    metrics:
    - metrics/model_comparison.json

  # Data drift detection stage
  data_drift_detection:
    cmd: python scripts/data_drift_detection.py
    deps:
    - scripts/data_drift_detection.py
    - data/processed/training_data.csv
    - data/processed/validation_data.csv
    - ml-service/src/data_drift_detector.py
    outs:
    - reports/data_drift_report.html
    - reports/drift_plots/
    params:
    - data_drift.detection_methods
    - data_drift.significance_level
    metrics:
    - metrics/data_drift.json

  # Model packaging stage
  model_packaging:
    cmd: python scripts/model_packaging.py
    deps:
    - scripts/model_packaging.py
    - models/champion_model.pkl
    - artifacts/preprocessor.pkl
    - artifacts/feature_engineer.pkl
    outs:
    - packages/fraud_detection_model_v${model_version}.tar.gz
    - packages/model_manifest.json
    params:
    - model_packaging.version
    - model_packaging.metadata
    metrics:
    - metrics/model_packaging.json

# Parameters configuration
params:
  - data_ingestion.batch_size
  - data_ingestion.date_range
  - data_ingestion.sources
  - data_validation.quality_threshold
  - data_validation.completeness_threshold
  - data_validation.outlier_detection
  - data_validation.schema_validation
  - feature_engineering.window_sizes
  - feature_engineering.aggregation_functions
  - feature_engineering.time_features
  - feature_engineering.categorical_encoding
  - data_preprocessing.test_size
  - data_preprocessing.validation_size
  - data_preprocessing.random_state
  - data_preprocessing.scaling_method
  - data_preprocessing.handle_imbalance
  - model_training.algorithms
  - model_training.hyperparameter_tuning
  - model_training.cross_validation
  - model_validation.performance_thresholds
  - model_validation.fairness_checks
  - model_validation.robustness_tests
  - model_evaluation.metrics
  - model_evaluation.visualization
  - model_comparison.comparison_metrics
  - model_comparison.selection_criteria
  - data_drift.detection_methods
  - data_drift.significance_level
  - data_drift.feature_importance_threshold
  - model_packaging.version
  - model_packaging.metadata

# Metrics configuration
metrics:
  - metrics/data_ingestion.json
  - metrics/data_validation.json
  - metrics/feature_engineering.json
  - metrics/data_preprocessing.json
  - metrics/model_training.json
  - metrics/model_validation.json
  - metrics/model_evaluation.json
  - metrics/model_comparison.json
  - metrics/data_drift.json
  - metrics/model_packaging.json

# Plots configuration
plots:
  - plots/data_quality_metrics.json
  - plots/feature_distributions.json
  - plots/model_performance_metrics.json
  - plots/training_curves.json
  - plots/validation_metrics.json
  - plots/drift_detection_results.json
