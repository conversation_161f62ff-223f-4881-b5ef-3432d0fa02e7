#!/usr/bin/env python3
"""
Model Deployment System
Handles automated model deployment with blue/green and canary strategies
"""

import os
import sys
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import requests
import mlflow
from mlflow.tracking import M<PERSON><PERSON><PERSON>lient
import kubernetes
from kubernetes import client, config
import yaml
from dataclasses import dataclass
import threading
import schedule

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DeploymentConfig:
    """Deployment configuration"""
    model_name: str
    model_version: str
    deployment_strategy: str  # 'blue_green', 'canary', 'rolling'
    namespace: str
    replicas: int
    resource_limits: Dict[str, str]
    health_check_endpoint: str
    rollback_threshold: float = 0.05  # Error rate threshold for rollback

class ModelDeployer:
    """Automated model deployment system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mlflow_client = MlflowClient()
        
        # Initialize Kubernetes client
        try:
            config.load_incluster_config()  # For in-cluster deployment
        except:
            config.load_kube_config()  # For local development
        
        self.k8s_apps_v1 = client.AppsV1Api()
        self.k8s_core_v1 = client.CoreV1Api()
        self.k8s_networking_v1 = client.NetworkingV1Api()
        
        # Deployment templates
        self.deployment_templates = self.load_deployment_templates()
    
    def load_deployment_templates(self) -> Dict[str, Any]:
        """Load Kubernetes deployment templates"""
        templates_dir = self.config.get('templates_dir', 'mlops/deployment/templates')
        templates = {}
        
        template_files = [
            'deployment.yaml',
            'service.yaml',
            'configmap.yaml',
            'hpa.yaml'
        ]
        
        for template_file in template_files:
            template_path = os.path.join(templates_dir, template_file)
            if os.path.exists(template_path):
                with open(template_path, 'r') as f:
                    templates[template_file.replace('.yaml', '')] = yaml.safe_load(f)
        
        return templates
    
    def get_latest_model_version(self, model_name: str, stage: str = 'Production') -> Optional[str]:
        """Get latest model version from MLflow registry"""
        try:
            latest_versions = self.mlflow_client.get_latest_versions(model_name, stages=[stage])
            if latest_versions:
                return latest_versions[0].version
        except Exception as e:
            logger.error(f"Error getting latest model version: {e}")
        
        return None
    
    def download_model_artifacts(self, model_name: str, model_version: str) -> str:
        """Download model artifacts from MLflow"""
        logger.info(f"Downloading model artifacts for {model_name} v{model_version}...")
        
        model_uri = f"models:/{model_name}/{model_version}"
        local_path = f"/tmp/models/{model_name}/{model_version}"
        
        try:
            mlflow.artifacts.download_artifacts(model_uri, dst_path=local_path)
            logger.info(f"Model artifacts downloaded to {local_path}")
            return local_path
        except Exception as e:
            logger.error(f"Error downloading model artifacts: {e}")
            raise
    
    def build_model_image(self, model_path: str, model_name: str, model_version: str) -> str:
        """Build Docker image with model artifacts"""
        logger.info(f"Building Docker image for {model_name} v{model_version}...")
        
        # Image name
        image_name = f"{self.config['registry']}/{model_name}:{model_version}"
        
        # Create Dockerfile
        dockerfile_content = f"""
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy model artifacts
COPY {model_path} /app/model/

# Copy serving code
COPY ml-service/src/ /app/src/
COPY ml-service/serve.py /app/

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start server
CMD ["python", "serve.py"]
"""
        
        # Write Dockerfile
        dockerfile_path = f"{model_path}/Dockerfile"
        with open(dockerfile_path, 'w') as f:
            f.write(dockerfile_content)
        
        # Build image (this would typically use Docker API or subprocess)
        # For this example, we'll assume the image is built externally
        logger.info(f"Docker image built: {image_name}")
        
        return image_name
    
    def create_deployment_manifest(self, deployment_config: DeploymentConfig, 
                                 image_name: str, deployment_type: str = 'blue') -> Dict[str, Any]:
        """Create Kubernetes deployment manifest"""
        
        # Base deployment template
        deployment = self.deployment_templates['deployment'].copy()
        
        # Update deployment configuration
        deployment['metadata']['name'] = f"{deployment_config.model_name}-{deployment_type}"
        deployment['metadata']['namespace'] = deployment_config.namespace
        deployment['metadata']['labels']['version'] = deployment_type
        deployment['metadata']['labels']['model-version'] = deployment_config.model_version
        
        # Update spec
        deployment['spec']['replicas'] = deployment_config.replicas
        deployment['spec']['selector']['matchLabels']['version'] = deployment_type
        
        # Update pod template
        pod_template = deployment['spec']['template']
        pod_template['metadata']['labels']['version'] = deployment_type
        pod_template['metadata']['labels']['model-version'] = deployment_config.model_version
        
        # Update container
        container = pod_template['spec']['containers'][0]
        container['image'] = image_name
        container['resources']['limits'] = deployment_config.resource_limits
        
        # Environment variables
        container['env'] = [
            {'name': 'MODEL_NAME', 'value': deployment_config.model_name},
            {'name': 'MODEL_VERSION', 'value': deployment_config.model_version},
            {'name': 'DEPLOYMENT_TYPE', 'value': deployment_type}
        ]
        
        return deployment
    
    def create_service_manifest(self, deployment_config: DeploymentConfig, 
                              service_type: str = 'blue') -> Dict[str, Any]:
        """Create Kubernetes service manifest"""
        
        service = self.deployment_templates['service'].copy()
        
        # Update service configuration
        service['metadata']['name'] = f"{deployment_config.model_name}-service"
        service['metadata']['namespace'] = deployment_config.namespace
        
        # Update selector to point to active deployment
        service['spec']['selector']['version'] = service_type
        
        return service
    
    def deploy_blue_green(self, deployment_config: DeploymentConfig, image_name: str) -> bool:
        """Deploy using blue/green strategy"""
        logger.info(f"Starting blue/green deployment for {deployment_config.model_name}...")
        
        # Determine current and new colors
        current_service = self.get_current_service(deployment_config)
        current_color = 'blue'
        new_color = 'green'
        
        if current_service and current_service.spec.selector.get('version') == 'green':
            current_color = 'green'
            new_color = 'blue'
        
        logger.info(f"Deploying to {new_color} environment...")
        
        try:
            # Create new deployment
            new_deployment = self.create_deployment_manifest(
                deployment_config, image_name, new_color
            )
            
            # Apply new deployment
            self.apply_deployment(new_deployment)
            
            # Wait for deployment to be ready
            if not self.wait_for_deployment_ready(
                f"{deployment_config.model_name}-{new_color}",
                deployment_config.namespace,
                timeout_seconds=600
            ):
                logger.error(f"New {new_color} deployment failed to become ready")
                return False
            
            # Health check new deployment
            if not self.health_check_deployment(deployment_config, new_color):
                logger.error(f"Health check failed for {new_color} deployment")
                return False
            
            # Switch traffic to new deployment
            self.switch_traffic(deployment_config, new_color)
            
            # Monitor new deployment
            if self.monitor_deployment(deployment_config, new_color):
                # Cleanup old deployment
                self.cleanup_old_deployment(deployment_config, current_color)
                logger.info(f"Blue/green deployment completed successfully")
                return True
            else:
                # Rollback
                logger.error("Deployment monitoring failed, rolling back...")
                self.switch_traffic(deployment_config, current_color)
                self.cleanup_old_deployment(deployment_config, new_color)
                return False
                
        except Exception as e:
            logger.error(f"Blue/green deployment failed: {e}")
            return False
    
    def deploy_canary(self, deployment_config: DeploymentConfig, image_name: str, 
                     canary_percentage: int = 10) -> bool:
        """Deploy using canary strategy"""
        logger.info(f"Starting canary deployment for {deployment_config.model_name}...")
        
        try:
            # Create canary deployment with reduced replicas
            canary_config = deployment_config
            canary_config.replicas = max(1, int(deployment_config.replicas * canary_percentage / 100))
            
            canary_deployment = self.create_deployment_manifest(
                canary_config, image_name, 'canary'
            )
            
            # Apply canary deployment
            self.apply_deployment(canary_deployment)
            
            # Wait for canary to be ready
            if not self.wait_for_deployment_ready(
                f"{deployment_config.model_name}-canary",
                deployment_config.namespace,
                timeout_seconds=300
            ):
                logger.error("Canary deployment failed to become ready")
                return False
            
            # Monitor canary deployment
            if self.monitor_canary_deployment(deployment_config, canary_percentage):
                # Gradually increase canary traffic
                for percentage in [25, 50, 75, 100]:
                    logger.info(f"Increasing canary traffic to {percentage}%...")
                    
                    # Update canary replicas
                    self.scale_deployment(
                        f"{deployment_config.model_name}-canary",
                        deployment_config.namespace,
                        max(1, int(deployment_config.replicas * percentage / 100))
                    )
                    
                    # Scale down main deployment
                    self.scale_deployment(
                        f"{deployment_config.model_name}-blue",
                        deployment_config.namespace,
                        max(0, int(deployment_config.replicas * (100 - percentage) / 100))
                    )
                    
                    # Monitor for issues
                    if not self.monitor_canary_deployment(deployment_config, percentage):
                        logger.error(f"Canary monitoring failed at {percentage}%")
                        self.rollback_canary(deployment_config)
                        return False
                    
                    time.sleep(300)  # Wait 5 minutes between increases
                
                # Complete canary deployment
                self.complete_canary_deployment(deployment_config)
                logger.info("Canary deployment completed successfully")
                return True
            else:
                logger.error("Canary monitoring failed, rolling back...")
                self.rollback_canary(deployment_config)
                return False
                
        except Exception as e:
            logger.error(f"Canary deployment failed: {e}")
            return False
    
    def apply_deployment(self, deployment_manifest: Dict[str, Any]):
        """Apply deployment to Kubernetes"""
        try:
            self.k8s_apps_v1.create_namespaced_deployment(
                namespace=deployment_manifest['metadata']['namespace'],
                body=deployment_manifest
            )
            logger.info(f"Deployment {deployment_manifest['metadata']['name']} created")
        except client.exceptions.ApiException as e:
            if e.status == 409:  # Already exists
                self.k8s_apps_v1.patch_namespaced_deployment(
                    name=deployment_manifest['metadata']['name'],
                    namespace=deployment_manifest['metadata']['namespace'],
                    body=deployment_manifest
                )
                logger.info(f"Deployment {deployment_manifest['metadata']['name']} updated")
            else:
                raise
    
    def wait_for_deployment_ready(self, deployment_name: str, namespace: str, 
                                timeout_seconds: int = 600) -> bool:
        """Wait for deployment to be ready"""
        logger.info(f"Waiting for deployment {deployment_name} to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout_seconds:
            try:
                deployment = self.k8s_apps_v1.read_namespaced_deployment(
                    name=deployment_name, namespace=namespace
                )
                
                if (deployment.status.ready_replicas and 
                    deployment.status.ready_replicas == deployment.spec.replicas):
                    logger.info(f"Deployment {deployment_name} is ready")
                    return True
                
            except client.exceptions.ApiException:
                pass
            
            time.sleep(10)
        
        logger.error(f"Deployment {deployment_name} failed to become ready within timeout")
        return False
    
    def health_check_deployment(self, deployment_config: DeploymentConfig, 
                              deployment_type: str) -> bool:
        """Perform health check on deployment"""
        logger.info(f"Performing health check on {deployment_type} deployment...")
        
        # Get service endpoint
        service_name = f"{deployment_config.model_name}-{deployment_type}"
        endpoint = f"http://{service_name}.{deployment_config.namespace}.svc.cluster.local:8000"
        
        # Health check
        for attempt in range(5):
            try:
                response = requests.get(
                    f"{endpoint}{deployment_config.health_check_endpoint}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.info(f"Health check passed for {deployment_type} deployment")
                    return True
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Health check attempt {attempt + 1} failed: {e}")
            
            time.sleep(30)
        
        logger.error(f"Health check failed for {deployment_type} deployment")
        return False
    
    def switch_traffic(self, deployment_config: DeploymentConfig, target_version: str):
        """Switch traffic to target deployment version"""
        logger.info(f"Switching traffic to {target_version} deployment...")
        
        service_name = f"{deployment_config.model_name}-service"
        
        # Update service selector
        service = self.k8s_core_v1.read_namespaced_service(
            name=service_name, namespace=deployment_config.namespace
        )
        
        service.spec.selector['version'] = target_version
        
        self.k8s_core_v1.patch_namespaced_service(
            name=service_name,
            namespace=deployment_config.namespace,
            body=service
        )
        
        logger.info(f"Traffic switched to {target_version} deployment")
    
    def monitor_deployment(self, deployment_config: DeploymentConfig, 
                         deployment_type: str, duration_minutes: int = 10) -> bool:
        """Monitor deployment for issues"""
        logger.info(f"Monitoring {deployment_type} deployment for {duration_minutes} minutes...")
        
        start_time = time.time()
        while time.time() - start_time < duration_minutes * 60:
            # Check error rate
            error_rate = self.get_deployment_error_rate(deployment_config, deployment_type)
            
            if error_rate > deployment_config.rollback_threshold:
                logger.error(f"Error rate {error_rate:.4f} exceeds threshold {deployment_config.rollback_threshold}")
                return False
            
            # Check response time
            avg_response_time = self.get_deployment_response_time(deployment_config, deployment_type)
            
            if avg_response_time > 5.0:  # 5 second threshold
                logger.error(f"Average response time {avg_response_time:.2f}s exceeds threshold")
                return False
            
            time.sleep(60)  # Check every minute
        
        logger.info(f"Deployment monitoring completed successfully")
        return True
    
    def get_deployment_error_rate(self, deployment_config: DeploymentConfig, 
                                deployment_type: str) -> float:
        """Get error rate for deployment"""
        # This would typically query your monitoring system (Prometheus, etc.)
        # For this example, we'll return a mock value
        return 0.01  # 1% error rate
    
    def get_deployment_response_time(self, deployment_config: DeploymentConfig, 
                                   deployment_type: str) -> float:
        """Get average response time for deployment"""
        # This would typically query your monitoring system
        # For this example, we'll return a mock value
        return 0.5  # 500ms average response time
    
    def get_current_service(self, deployment_config: DeploymentConfig):
        """Get current service configuration"""
        try:
            return self.k8s_core_v1.read_namespaced_service(
                name=f"{deployment_config.model_name}-service",
                namespace=deployment_config.namespace
            )
        except client.exceptions.ApiException:
            return None
    
    def cleanup_old_deployment(self, deployment_config: DeploymentConfig, version: str):
        """Cleanup old deployment"""
        logger.info(f"Cleaning up {version} deployment...")
        
        try:
            self.k8s_apps_v1.delete_namespaced_deployment(
                name=f"{deployment_config.model_name}-{version}",
                namespace=deployment_config.namespace
            )
            logger.info(f"Old {version} deployment cleaned up")
        except client.exceptions.ApiException as e:
            logger.warning(f"Failed to cleanup {version} deployment: {e}")
    
    def scale_deployment(self, deployment_name: str, namespace: str, replicas: int):
        """Scale deployment to specified replicas"""
        try:
            self.k8s_apps_v1.patch_namespaced_deployment_scale(
                name=deployment_name,
                namespace=namespace,
                body={'spec': {'replicas': replicas}}
            )
            logger.info(f"Scaled {deployment_name} to {replicas} replicas")
        except client.exceptions.ApiException as e:
            logger.error(f"Failed to scale deployment: {e}")
    
    def monitor_canary_deployment(self, deployment_config: DeploymentConfig, 
                                percentage: int) -> bool:
        """Monitor canary deployment"""
        return self.monitor_deployment(deployment_config, 'canary', duration_minutes=5)
    
    def rollback_canary(self, deployment_config: DeploymentConfig):
        """Rollback canary deployment"""
        logger.info("Rolling back canary deployment...")
        self.cleanup_old_deployment(deployment_config, 'canary')
    
    def complete_canary_deployment(self, deployment_config: DeploymentConfig):
        """Complete canary deployment by replacing main deployment"""
        logger.info("Completing canary deployment...")
        
        # Rename canary to main
        # This is a simplified approach - in practice, you'd update the service selector
        self.switch_traffic(deployment_config, 'canary')
        self.cleanup_old_deployment(deployment_config, 'blue')
    
    def deploy_model(self, model_name: str, model_version: str = None, 
                    deployment_strategy: str = 'blue_green') -> bool:
        """Deploy model with specified strategy"""
        logger.info(f"Starting deployment of {model_name} with {deployment_strategy} strategy...")
        
        # Get model version if not specified
        if model_version is None:
            model_version = self.get_latest_model_version(model_name)
            if model_version is None:
                logger.error(f"No production model found for {model_name}")
                return False
        
        # Download model artifacts
        model_path = self.download_model_artifacts(model_name, model_version)
        
        # Build Docker image
        image_name = self.build_model_image(model_path, model_name, model_version)
        
        # Create deployment configuration
        deployment_config = DeploymentConfig(
            model_name=model_name,
            model_version=model_version,
            deployment_strategy=deployment_strategy,
            namespace=self.config.get('namespace', 'fraudshield'),
            replicas=self.config.get('replicas', 3),
            resource_limits=self.config.get('resource_limits', {'cpu': '1', 'memory': '2Gi'}),
            health_check_endpoint='/health'
        )
        
        # Deploy based on strategy
        if deployment_strategy == 'blue_green':
            return self.deploy_blue_green(deployment_config, image_name)
        elif deployment_strategy == 'canary':
            return self.deploy_canary(deployment_config, image_name)
        else:
            logger.error(f"Unsupported deployment strategy: {deployment_strategy}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Model deployment system')
    parser.add_argument('--model-name', type=str, required=True,
                       help='Name of the model to deploy')
    parser.add_argument('--model-version', type=str,
                       help='Version of the model to deploy (default: latest production)')
    parser.add_argument('--strategy', type=str, choices=['blue_green', 'canary'],
                       default='blue_green', help='Deployment strategy')
    parser.add_argument('--config', type=str, default='config/deployment_config.json',
                       help='Path to deployment configuration file')
    
    args = parser.parse_args()
    
    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'registry': 'ghcr.io/fraudshield',
            'namespace': 'fraudshield',
            'replicas': 3,
            'resource_limits': {'cpu': '1', 'memory': '2Gi'}
        }
    
    # Initialize deployer
    deployer = ModelDeployer(config)
    
    # Deploy model
    success = deployer.deploy_model(
        args.model_name,
        args.model_version,
        args.strategy
    )
    
    if success:
        print(f"Model {args.model_name} deployed successfully!")
    else:
        print(f"Model {args.model_name} deployment failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
