apiVersion: apps/v1
kind: Deployment
metadata:
  name: fraudshield-ml-service
  namespace: fraudshield
  labels:
    app: fraudshield-ml-service
    component: ml-service
    version: blue
    model-version: "1.0.0"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fraudshield-ml-service
      version: blue
  template:
    metadata:
      labels:
        app: fraudshield-ml-service
        component: ml-service
        version: blue
        model-version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: fraudshield-ml-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: ml-service
        image: ghcr.io/fraudshield/ml-service:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: MODEL_NAME
          value: "fraud-detection"
        - name: MODEL_VERSION
          value: "1.0.0"
        - name: DEPLOYMENT_TYPE
          value: "blue"
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: MLFLOW_TRACKING_URI
          valueFrom:
            secretKeyRef:
              name: mlflow-secrets
              key: tracking-uri
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secrets
              key: url
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: model-cache
          mountPath: /app/model-cache
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: tmp
        emptyDir: {}
      - name: model-cache
        emptyDir:
          sizeLimit: 1Gi
      - name: config
        configMap:
          name: ml-service-config
      nodeSelector:
        kubernetes.io/arch: amd64
        node-type: compute
      tolerations:
      - key: "ml-workload"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - fraudshield-ml-service
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-type
                operator: In
                values:
                - compute
      terminationGracePeriodSeconds: 60

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ml-service-config
  namespace: fraudshield
data:
  config.yaml: |
    model:
      name: fraud-detection
      version: "1.0.0"
      cache_size: 100
      batch_size: 32
      timeout_seconds: 30
    
    serving:
      host: "0.0.0.0"
      port: 8000
      workers: 4
      max_requests: 1000
      max_requests_jitter: 100
    
    monitoring:
      enable_metrics: true
      metrics_port: 9090
      log_predictions: true
      log_level: INFO
    
    performance:
      enable_batching: true
      max_batch_size: 32
      batch_timeout_ms: 100
      enable_caching: true
      cache_ttl_seconds: 300
    
    security:
      enable_auth: true
      api_key_header: "X-API-Key"
      rate_limit_per_minute: 1000
      enable_cors: true
      allowed_origins:
        - "https://fraudshield.example.com"
        - "https://api.fraudshield.example.com"

---
apiVersion: v1
kind: Service
metadata:
  name: fraudshield-ml-service
  namespace: fraudshield
  labels:
    app: fraudshield-ml-service
    component: ml-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8000
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: fraudshield-ml-service
    version: blue

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: fraudshield-ml-service-hpa
  namespace: fraudshield
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: fraudshield-ml-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: ml_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: fraudshield-ml-service-pdb
  namespace: fraudshield
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: fraudshield-ml-service

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: fraudshield-ml-service-netpol
  namespace: fraudshield
spec:
  podSelector:
    matchLabels:
      app: fraudshield-ml-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: fraudshield-backend
    - podSelector:
        matchLabels:
          app: fraudshield-stream-processor
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 9090
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443
