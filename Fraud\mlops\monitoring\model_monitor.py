#!/usr/bin/env python3
"""
Model Monitoring System
Real-time monitoring of model performance, data drift, and operational metrics
"""

import os
import sys
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
from dataclasses import dataclass, asdict
import redis
import psycopg2
from sqlalchemy import create_engine
import mlflow
from mlflow.tracking import M<PERSON>lowClient
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import schedule
import threading
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml_service.src.data_drift_detector import DataDriftDetector

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelMetrics:
    """Model performance metrics"""
    timestamp: datetime
    model_version: str
    predictions_count: int
    avg_prediction_score: float
    fraud_rate: float
    latency_p50: float
    latency_p95: float
    latency_p99: float
    error_rate: float
    data_drift_score: float
    concept_drift_score: Optional[float] = None

@dataclass
class AlertConfig:
    """Alert configuration"""
    metric_name: str
    threshold: float
    operator: str  # 'gt', 'lt', 'eq'
    severity: str  # 'low', 'medium', 'high', 'critical'
    cooldown_minutes: int = 30

class ModelMonitor:
    """Comprehensive model monitoring system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.redis_client = redis.Redis(
            host=config.get('redis_host', 'localhost'),
            port=config.get('redis_port', 6379),
            db=config.get('redis_db', 0)
        )
        
        # Database connection
        self.db_engine = create_engine(config['database_url'])
        
        # MLflow client
        self.mlflow_client = MlflowClient()
        
        # Data drift detector
        self.drift_detector = DataDriftDetector()
        
        # Prometheus metrics
        self.setup_prometheus_metrics()
        
        # Alert configurations
        self.alert_configs = self.load_alert_configs()
        
        # Alert cooldowns
        self.alert_cooldowns = {}
        
        # Reference data for drift detection
        self.reference_data = None
        self.load_reference_data()
    
    def setup_prometheus_metrics(self):
        """Setup Prometheus metrics"""
        self.metrics = {
            'predictions_total': Counter(
                'ml_predictions_total',
                'Total number of predictions made',
                ['model_version', 'prediction_type']
            ),
            'prediction_latency': Histogram(
                'ml_prediction_latency_seconds',
                'Prediction latency in seconds',
                ['model_version']
            ),
            'prediction_score': Histogram(
                'ml_prediction_score',
                'Distribution of prediction scores',
                ['model_version'],
                buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
            ),
            'fraud_rate': Gauge(
                'ml_fraud_rate',
                'Current fraud detection rate',
                ['model_version']
            ),
            'data_drift_score': Gauge(
                'ml_data_drift_score',
                'Data drift score',
                ['feature_name']
            ),
            'concept_drift_score': Gauge(
                'ml_concept_drift_score',
                'Concept drift score',
                ['model_version']
            ),
            'model_errors_total': Counter(
                'ml_model_errors_total',
                'Total number of model errors',
                ['model_version', 'error_type']
            ),
            'model_accuracy': Gauge(
                'ml_model_accuracy',
                'Current model accuracy',
                ['model_version']
            )
        }
    
    def load_alert_configs(self) -> List[AlertConfig]:
        """Load alert configurations"""
        default_alerts = [
            AlertConfig('fraud_rate', 0.1, 'gt', 'high', 30),
            AlertConfig('fraud_rate', 0.001, 'lt', 'medium', 60),
            AlertConfig('latency_p95', 1.0, 'gt', 'medium', 15),
            AlertConfig('error_rate', 0.05, 'gt', 'high', 10),
            AlertConfig('data_drift_score', 0.3, 'gt', 'medium', 60),
            AlertConfig('concept_drift_score', 0.2, 'gt', 'high', 120),
        ]
        
        # Load from config if available
        alert_config_path = self.config.get('alert_config_path')
        if alert_config_path and os.path.exists(alert_config_path):
            with open(alert_config_path, 'r') as f:
                alert_data = json.load(f)
                return [AlertConfig(**alert) for alert in alert_data]
        
        return default_alerts
    
    def load_reference_data(self):
        """Load reference data for drift detection"""
        reference_data_path = self.config.get('reference_data_path')
        if reference_data_path and os.path.exists(reference_data_path):
            self.reference_data = pd.read_csv(reference_data_path)
            logger.info(f"Loaded reference data with {len(self.reference_data)} samples")
        else:
            logger.warning("Reference data not found, drift detection will be limited")
    
    def collect_prediction_metrics(self, time_window_minutes: int = 5) -> ModelMetrics:
        """Collect prediction metrics from the last time window"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(minutes=time_window_minutes)
        
        # Query prediction logs from database
        query = """
        SELECT 
            model_version,
            prediction_score,
            prediction_time,
            processing_time_ms,
            is_error,
            features
        FROM prediction_logs 
        WHERE prediction_time >= %s AND prediction_time <= %s
        """
        
        df = pd.read_sql(query, self.db_engine, params=[start_time, end_time])
        
        if df.empty:
            logger.warning("No predictions found in the time window")
            return None
        
        # Calculate metrics
        model_version = df['model_version'].iloc[0]
        predictions_count = len(df)
        avg_prediction_score = df['prediction_score'].mean()
        fraud_rate = (df['prediction_score'] > 0.5).mean()
        
        # Latency metrics (convert from ms to seconds)
        latencies = df['processing_time_ms'] / 1000.0
        latency_p50 = latencies.quantile(0.5)
        latency_p95 = latencies.quantile(0.95)
        latency_p99 = latencies.quantile(0.99)
        
        # Error rate
        error_rate = df['is_error'].mean()
        
        # Data drift (if reference data available)
        data_drift_score = 0.0
        if self.reference_data is not None and 'features' in df.columns:
            try:
                # Parse features JSON
                current_features = pd.json_normalize(df['features'].apply(json.loads))
                drift_results = self.drift_detector.detect_drift(
                    self.reference_data, current_features
                )
                data_drift_score = drift_results.get('overall_drift_score', 0.0)
            except Exception as e:
                logger.error(f"Error calculating data drift: {e}")
        
        metrics = ModelMetrics(
            timestamp=end_time,
            model_version=model_version,
            predictions_count=predictions_count,
            avg_prediction_score=avg_prediction_score,
            fraud_rate=fraud_rate,
            latency_p50=latency_p50,
            latency_p95=latency_p95,
            latency_p99=latency_p99,
            error_rate=error_rate,
            data_drift_score=data_drift_score
        )
        
        return metrics
    
    def calculate_concept_drift(self, time_window_hours: int = 24) -> Optional[float]:
        """Calculate concept drift using ground truth feedback"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=time_window_hours)
        
        # Query predictions with ground truth
        query = """
        SELECT 
            prediction_score,
            actual_fraud
        FROM prediction_logs pl
        JOIN fraud_feedback ff ON pl.transaction_id = ff.transaction_id
        WHERE pl.prediction_time >= %s AND pl.prediction_time <= %s
        AND ff.feedback_time IS NOT NULL
        """
        
        try:
            df = pd.read_sql(query, self.db_engine, params=[start_time, end_time])
            
            if len(df) < 100:  # Minimum sample size
                return None
            
            # Calculate current performance
            y_true = df['actual_fraud']
            y_pred = (df['prediction_score'] > 0.5).astype(int)
            
            current_accuracy = (y_true == y_pred).mean()
            
            # Get baseline accuracy from model registry
            baseline_accuracy = self.get_baseline_accuracy()
            
            if baseline_accuracy is not None:
                concept_drift_score = abs(baseline_accuracy - current_accuracy)
                return concept_drift_score
            
        except Exception as e:
            logger.error(f"Error calculating concept drift: {e}")
        
        return None
    
    def get_baseline_accuracy(self) -> Optional[float]:
        """Get baseline accuracy from model registry"""
        try:
            # Get current production model
            model_name = self.config.get('model_name', 'fraud-detection')
            latest_version = self.mlflow_client.get_latest_versions(
                model_name, stages=['Production']
            )
            
            if latest_version:
                run_id = latest_version[0].run_id
                run = self.mlflow_client.get_run(run_id)
                return run.data.metrics.get('accuracy')
        except Exception as e:
            logger.error(f"Error getting baseline accuracy: {e}")
        
        return None
    
    def update_prometheus_metrics(self, metrics: ModelMetrics):
        """Update Prometheus metrics"""
        # Prediction counts
        self.metrics['predictions_total'].labels(
            model_version=metrics.model_version,
            prediction_type='total'
        ).inc(metrics.predictions_count)
        
        # Fraud rate
        self.metrics['fraud_rate'].labels(
            model_version=metrics.model_version
        ).set(metrics.fraud_rate)
        
        # Data drift
        self.metrics['data_drift_score'].labels(
            feature_name='overall'
        ).set(metrics.data_drift_score)
        
        # Concept drift (if available)
        if metrics.concept_drift_score is not None:
            self.metrics['concept_drift_score'].labels(
                model_version=metrics.model_version
            ).set(metrics.concept_drift_score)
    
    def store_metrics(self, metrics: ModelMetrics):
        """Store metrics in database and Redis"""
        # Store in database for historical analysis
        metrics_dict = asdict(metrics)
        
        insert_query = """
        INSERT INTO model_metrics (
            timestamp, model_version, predictions_count, avg_prediction_score,
            fraud_rate, latency_p50, latency_p95, latency_p99, error_rate,
            data_drift_score, concept_drift_score
        ) VALUES (
            %(timestamp)s, %(model_version)s, %(predictions_count)s, %(avg_prediction_score)s,
            %(fraud_rate)s, %(latency_p50)s, %(latency_p95)s, %(latency_p99)s, %(error_rate)s,
            %(data_drift_score)s, %(concept_drift_score)s
        )
        """
        
        try:
            with self.db_engine.connect() as conn:
                conn.execute(insert_query, metrics_dict)
        except Exception as e:
            logger.error(f"Error storing metrics in database: {e}")
        
        # Store in Redis for real-time access
        redis_key = f"model_metrics:latest"
        self.redis_client.setex(redis_key, 3600, json.dumps(metrics_dict, default=str))
    
    def check_alerts(self, metrics: ModelMetrics):
        """Check alert conditions and trigger alerts"""
        current_time = datetime.utcnow()
        
        for alert_config in self.alert_configs:
            # Check cooldown
            cooldown_key = f"alert_cooldown:{alert_config.metric_name}"
            if cooldown_key in self.alert_cooldowns:
                last_alert_time = self.alert_cooldowns[cooldown_key]
                if (current_time - last_alert_time).total_seconds() < alert_config.cooldown_minutes * 60:
                    continue
            
            # Get metric value
            metric_value = getattr(metrics, alert_config.metric_name, None)
            if metric_value is None:
                continue
            
            # Check threshold
            should_alert = False
            if alert_config.operator == 'gt' and metric_value > alert_config.threshold:
                should_alert = True
            elif alert_config.operator == 'lt' and metric_value < alert_config.threshold:
                should_alert = True
            elif alert_config.operator == 'eq' and metric_value == alert_config.threshold:
                should_alert = True
            
            if should_alert:
                self.trigger_alert(alert_config, metric_value, metrics)
                self.alert_cooldowns[cooldown_key] = current_time
    
    def trigger_alert(self, alert_config: AlertConfig, metric_value: float, metrics: ModelMetrics):
        """Trigger alert notification"""
        alert_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'metric_name': alert_config.metric_name,
            'metric_value': metric_value,
            'threshold': alert_config.threshold,
            'severity': alert_config.severity,
            'model_version': metrics.model_version,
            'message': f"Model monitoring alert: {alert_config.metric_name} = {metric_value:.4f} "
                      f"({alert_config.operator} {alert_config.threshold})"
        }
        
        # Log alert
        logger.warning(f"ALERT: {alert_data['message']}")
        
        # Store alert in database
        self.store_alert(alert_data)
        
        # Send notifications (implement based on your notification system)
        self.send_alert_notifications(alert_data)
    
    def store_alert(self, alert_data: Dict[str, Any]):
        """Store alert in database"""
        insert_query = """
        INSERT INTO model_alerts (
            timestamp, metric_name, metric_value, threshold, severity,
            model_version, message
        ) VALUES (
            %(timestamp)s, %(metric_name)s, %(metric_value)s, %(threshold)s,
            %(severity)s, %(model_version)s, %(message)s
        )
        """
        
        try:
            with self.db_engine.connect() as conn:
                conn.execute(insert_query, alert_data)
        except Exception as e:
            logger.error(f"Error storing alert: {e}")
    
    def send_alert_notifications(self, alert_data: Dict[str, Any]):
        """Send alert notifications (implement based on your notification system)"""
        # Example: Send to Slack, email, PagerDuty, etc.
        # This is a placeholder - implement based on your requirements
        
        if alert_data['severity'] in ['high', 'critical']:
            # Send immediate notification
            logger.info(f"Sending immediate notification for {alert_data['severity']} alert")
        
        # Store in Redis for dashboard consumption
        alert_key = f"alerts:latest:{alert_data['metric_name']}"
        self.redis_client.setex(alert_key, 3600, json.dumps(alert_data))
    
    def run_monitoring_cycle(self):
        """Run one monitoring cycle"""
        try:
            logger.info("Running monitoring cycle...")
            
            # Collect metrics
            metrics = self.collect_prediction_metrics()
            
            if metrics is None:
                logger.warning("No metrics collected, skipping cycle")
                return
            
            # Calculate concept drift
            concept_drift_score = self.calculate_concept_drift()
            if concept_drift_score is not None:
                metrics.concept_drift_score = concept_drift_score
            
            # Update Prometheus metrics
            self.update_prometheus_metrics(metrics)
            
            # Store metrics
            self.store_metrics(metrics)
            
            # Check alerts
            self.check_alerts(metrics)
            
            logger.info(f"Monitoring cycle completed. Fraud rate: {metrics.fraud_rate:.4f}, "
                       f"Latency P95: {metrics.latency_p95:.3f}s, "
                       f"Data drift: {metrics.data_drift_score:.4f}")
            
        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")
    
    def start_monitoring(self):
        """Start the monitoring system"""
        logger.info("Starting model monitoring system...")
        
        # Start Prometheus metrics server
        prometheus_port = self.config.get('prometheus_port', 8000)
        start_http_server(prometheus_port)
        logger.info(f"Prometheus metrics server started on port {prometheus_port}")
        
        # Schedule monitoring cycles
        monitoring_interval = self.config.get('monitoring_interval_minutes', 5)
        schedule.every(monitoring_interval).minutes.do(self.run_monitoring_cycle)
        
        # Run initial cycle
        self.run_monitoring_cycle()
        
        # Start scheduler
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Model monitoring system')
    parser.add_argument('--config', type=str, default='config/monitoring_config.json',
                       help='Path to monitoring configuration file')
    
    args = parser.parse_args()
    
    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'database_url': os.getenv('DATABASE_URL', 'postgresql://localhost/fraudshield'),
            'redis_host': os.getenv('REDIS_HOST', 'localhost'),
            'redis_port': int(os.getenv('REDIS_PORT', 6379)),
            'prometheus_port': int(os.getenv('PROMETHEUS_PORT', 8000)),
            'monitoring_interval_minutes': 5,
            'model_name': 'fraud-detection'
        }
    
    # Initialize and start monitor
    monitor = ModelMonitor(config)
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
