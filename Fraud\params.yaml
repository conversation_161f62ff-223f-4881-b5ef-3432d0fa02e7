# DVC Parameters Configuration
# This file contains all parameters used in the DVC pipeline

# Data ingestion parameters
data_ingestion:
  batch_size: 10000
  date_range:
    start: "2023-01-01"
    end: "2024-01-01"
  sources:
    - transactions_db
    - external_api

# Data validation parameters
data_validation:
  quality_threshold: 0.95
  completeness_threshold: 0.98
  outlier_detection: true
  schema_validation: true

# Feature engineering parameters
feature_engineering:
  window_sizes: [1, 7, 30]
  aggregation_functions:
    - mean
    - std
    - count
    - sum
  time_features: true
  categorical_encoding: "target"

# Data preprocessing parameters
data_preprocessing:
  test_size: 0.2
  validation_size: 0.1
  random_state: 42
  scaling_method: "standard"
  handle_imbalance: true

# Model training parameters
model_training:
  algorithms:
    - random_forest
    - gradient_boosting
    - logistic_regression
  hyperparameter_tuning:
    n_trials: 100
    optimization_metric: "roc_auc"
  cross_validation:
    folds: 5
    stratified: true

# Model validation parameters
model_validation:
  performance_thresholds:
    min_roc_auc: 0.85
    min_precision: 0.80
    min_recall: 0.75
    max_false_positive_rate: 0.05
  fairness_checks:
    enable: true
    protected_attributes: ["transaction_type"]
  robustness_tests:
    enable: true
    noise_levels: [0.01, 0.05, 0.1]

# Model evaluation parameters
model_evaluation:
  metrics:
    - accuracy
    - precision
    - recall
    - f1_score
    - roc_auc
    - pr_auc
  visualization:
    confusion_matrix: true
    roc_curve: true
    precision_recall_curve: true
    feature_importance: true

# Model comparison parameters
model_comparison:
  comparison_metrics:
    - roc_auc
    - precision
    - recall
    - f1_score
  selection_criteria:
    primary_metric: "roc_auc"
    secondary_metric: "precision"
    min_improvement: 0.01

# Data drift detection parameters
data_drift:
  detection_methods:
    - kolmogorov_smirnov
    - population_stability_index
    - jensen_shannon_divergence
  significance_level: 0.05
  feature_importance_threshold: 0.01

# Model packaging parameters
model_packaging:
  version: "1.0.0"
  metadata:
    author: "MLOps Team"
    description: "Fraud detection model"
    framework: "scikit-learn"
    python_version: "3.11"
