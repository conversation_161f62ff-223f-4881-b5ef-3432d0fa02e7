#!/bin/bash

# Database Restore Script for FraudShield
# This script restores the PostgreSQL database from a backup

set -e

# Configuration
BACKUP_DIR="/backup"
DB_NAME="fraudshield"
DB_USER="fraudshield"
DB_HOST="localhost"
DB_PORT="5432"

# Check if backup file is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <backup_file>"
    echo "Available backups:"
    ls -la ${BACKUP_DIR}/fraudshield_backup_*.sql.gz
    exit 1
fi

BACKUP_FILE="$1"

# Verify backup file exists
if [ ! -f "${BACKUP_FILE}" ]; then
    echo "ERROR: Backup file ${BACKUP_FILE} not found!"
    exit 1
fi

echo "Starting database restore from ${BACKUP_FILE} at $(date)"

# Create a temporary directory for extraction
TEMP_DIR=$(mktemp -d)
EXTRACTED_FILE="${TEMP_DIR}/restore.sql"

# Extract the backup file
if [[ ${BACKUP_FILE} == *.gz ]]; then
    gunzip -c ${BACKUP_FILE} > ${EXTRACTED_FILE}
else
    cp ${BACKUP_FILE} ${EXTRACTED_FILE}
fi

# Stop application services before restore
echo "Stopping application services..."
docker-compose stop backend ml-service feature-store stream-processor

# Create a backup of current database before restore
CURRENT_BACKUP="${BACKUP_DIR}/pre_restore_backup_$(date +"%Y%m%d_%H%M%S").sql"
echo "Creating backup of current database..."
pg_dump -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} \
    --verbose --clean --no-owner --no-privileges \
    --file=${CURRENT_BACKUP}
gzip ${CURRENT_BACKUP}

# Drop and recreate database
echo "Dropping and recreating database..."
dropdb -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} ${DB_NAME}
createdb -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} ${DB_NAME}

# Restore database
echo "Restoring database..."
psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -f ${EXTRACTED_FILE}

# Clean up temporary files
rm -rf ${TEMP_DIR}

echo "Database restore completed successfully"

# Restart application services
echo "Starting application services..."
docker-compose start backend ml-service feature-store stream-processor

echo "Database restore process completed at $(date)"
echo "Previous database backed up to: ${CURRENT_BACKUP}.gz"
