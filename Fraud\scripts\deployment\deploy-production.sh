#!/bin/bash

# Production Deployment Script for FraudShield
# This script handles blue-green deployment to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEPLOYMENT_TYPE=${1:-"rolling"}  # rolling, blue-green, canary
VERSION=${2:-"latest"}
HEALTH_CHECK_RETRIES=10
HEALTH_CHECK_INTERVAL=30

echo -e "${BLUE}=== FraudShield Production Deployment ===${NC}"
echo "Deployment Type: ${DEPLOYMENT_TYPE}"
echo "Version: ${VERSION}"
echo "Timestamp: $(date)"
echo "============================================"

# Pre-deployment checks
echo -e "${YELLOW}Running pre-deployment checks...${NC}"

# Check if all required files exist
REQUIRED_FILES=(
    "docker-compose.prod.yml"
    "secrets/db_password.txt"
    "secrets/jwt_secret.txt"
    "secrets/grafana_password.txt"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}ERROR: Required file $file not found${NC}"
        exit 1
    fi
done

# Check if secrets are properly configured
if [ ! -s "secrets/db_password.txt" ] || [ ! -s "secrets/jwt_secret.txt" ]; then
    echo -e "${RED}ERROR: Secrets not properly configured${NC}"
    exit 1
fi

echo -e "${GREEN}Pre-deployment checks passed${NC}"

# Function to wait for service health
wait_for_health() {
    local service=$1
    local retries=$HEALTH_CHECK_RETRIES
    
    echo "Waiting for $service to be healthy..."
    
    while [ $retries -gt 0 ]; do
        if ./scripts/deployment/health-check.sh > /dev/null 2>&1; then
            echo -e "${GREEN}$service is healthy${NC}"
            return 0
        fi
        
        echo "Health check failed, retrying in ${HEALTH_CHECK_INTERVAL}s... ($retries retries left)"
        sleep $HEALTH_CHECK_INTERVAL
        retries=$((retries - 1))
    done
    
    echo -e "${RED}$service failed to become healthy${NC}"
    return 1
}

# Function for rolling deployment
rolling_deployment() {
    echo -e "${YELLOW}Starting rolling deployment...${NC}"
    
    # Pull latest images
    docker-compose -f docker-compose.prod.yml pull
    
    # Update services one by one
    SERVICES=("ml-service" "feature-store" "backend" "stream-processor" "frontend")
    
    for service in "${SERVICES[@]}"; do
        echo "Updating $service..."
        docker-compose -f docker-compose.prod.yml up -d --no-deps $service
        
        # Wait for service to be healthy
        sleep 10
        if ! wait_for_health $service; then
            echo -e "${RED}Rolling back $service...${NC}"
            docker-compose -f docker-compose.prod.yml restart $service
            exit 1
        fi
    done
    
    echo -e "${GREEN}Rolling deployment completed successfully${NC}"
}

# Function for blue-green deployment
blue_green_deployment() {
    echo -e "${YELLOW}Starting blue-green deployment...${NC}"
    
    # Create green environment
    docker-compose -f docker-compose.prod.yml -p fraudshield-green up -d
    
    # Wait for green environment to be healthy
    if ! wait_for_health "green environment"; then
        echo -e "${RED}Green environment failed to start, cleaning up...${NC}"
        docker-compose -f docker-compose.prod.yml -p fraudshield-green down
        exit 1
    fi
    
    # Switch traffic to green (this would typically involve load balancer configuration)
    echo "Switching traffic to green environment..."
    
    # Stop blue environment
    docker-compose -f docker-compose.prod.yml -p fraudshield-blue down
    
    # Rename green to blue for next deployment
    docker-compose -f docker-compose.prod.yml -p fraudshield-green down
    docker-compose -f docker-compose.prod.yml -p fraudshield-blue up -d
    
    echo -e "${GREEN}Blue-green deployment completed successfully${NC}"
}

# Create database backup before deployment
echo -e "${YELLOW}Creating pre-deployment database backup...${NC}"
./scripts/backup/backup-database.sh

# Execute deployment based on type
case $DEPLOYMENT_TYPE in
    "rolling")
        rolling_deployment
        ;;
    "blue-green")
        blue_green_deployment
        ;;
    "canary")
        echo -e "${YELLOW}Canary deployment not implemented yet${NC}"
        rolling_deployment
        ;;
    *)
        echo -e "${RED}Unknown deployment type: $DEPLOYMENT_TYPE${NC}"
        exit 1
        ;;
esac

# Post-deployment verification
echo -e "${YELLOW}Running post-deployment verification...${NC}"

# Run comprehensive health checks
if ./scripts/deployment/health-check.sh; then
    echo -e "${GREEN}Post-deployment health checks passed${NC}"
else
    echo -e "${RED}Post-deployment health checks failed${NC}"
    exit 1
fi

# Run smoke tests
echo "Running smoke tests..."
if python scripts/test-phase4.py; then
    echo -e "${GREEN}Smoke tests passed${NC}"
else
    echo -e "${RED}Smoke tests failed${NC}"
    exit 1
fi

echo -e "${GREEN}=== Deployment Completed Successfully ===${NC}"
echo "Deployment completed at: $(date)"
echo "Version deployed: ${VERSION}"
echo "Monitoring dashboard: http://localhost:3001"
echo "Prometheus metrics: http://localhost:9090"
