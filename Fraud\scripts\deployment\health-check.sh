#!/bin/bash

# Health Check Script for FraudShield
# This script performs comprehensive health checks on all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SERVICES=(
    "frontend:3000:/health"
    "backend:8000:/health"
    "ml-service:8001:/health"
    "feature-store:8002:/health"
    "postgres:5432"
    "redis:6379"
    "kafka:9092"
    "prometheus:9090/-/healthy"
    "grafana:3001/api/health"
)

TIMEOUT=10
OVERALL_STATUS=0

echo "=== FraudShield Health Check ==="
echo "Timestamp: $(date)"
echo "================================"

# Function to check HTTP endpoint
check_http_endpoint() {
    local service=$1
    local port=$2
    local endpoint=$3
    local url="http://localhost:${port}${endpoint}"
    
    if curl -f -s --max-time ${TIMEOUT} ${url} > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} ${service} (${url}) - OK"
        return 0
    else
        echo -e "${RED}✗${NC} ${service} (${url}) - FAILED"
        return 1
    fi
}

# Function to check TCP port
check_tcp_port() {
    local service=$1
    local port=$2
    
    if nc -z localhost ${port} 2>/dev/null; then
        echo -e "${GREEN}✓${NC} ${service} (port ${port}) - OK"
        return 0
    else
        echo -e "${RED}✗${NC} ${service} (port ${port}) - FAILED"
        return 1
    fi
}

# Check Docker containers
echo "Checking Docker containers..."
docker-compose ps

echo -e "\nChecking service endpoints..."

# Check each service
for service_config in "${SERVICES[@]}"; do
    IFS=':' read -r service port endpoint <<< "$service_config"
    
    if [ -n "$endpoint" ]; then
        if ! check_http_endpoint "$service" "$port" "$endpoint"; then
            OVERALL_STATUS=1
        fi
    else
        if ! check_tcp_port "$service" "$port"; then
            OVERALL_STATUS=1
        fi
    fi
done

# Additional checks
echo -e "\nPerforming additional checks..."

# Check database connectivity
if psql -h localhost -p 5432 -U fraudshield -d fraudshield -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} Database connectivity - OK"
else
    echo -e "${RED}✗${NC} Database connectivity - FAILED"
    OVERALL_STATUS=1
fi

# Check Redis connectivity
if redis-cli -h localhost -p 6379 ping | grep -q "PONG"; then
    echo -e "${GREEN}✓${NC} Redis connectivity - OK"
else
    echo -e "${RED}✗${NC} Redis connectivity - FAILED"
    OVERALL_STATUS=1
fi

# Check Kafka connectivity
if kafka-topics.sh --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} Kafka connectivity - OK"
else
    echo -e "${RED}✗${NC} Kafka connectivity - FAILED"
    OVERALL_STATUS=1
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    echo -e "${GREEN}✓${NC} Disk space (${DISK_USAGE}%) - OK"
else
    echo -e "${YELLOW}⚠${NC} Disk space (${DISK_USAGE}%) - WARNING"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -lt 80 ]; then
    echo -e "${GREEN}✓${NC} Memory usage (${MEMORY_USAGE}%) - OK"
else
    echo -e "${YELLOW}⚠${NC} Memory usage (${MEMORY_USAGE}%) - WARNING"
fi

echo "================================"
if [ $OVERALL_STATUS -eq 0 ]; then
    echo -e "${GREEN}Overall Status: HEALTHY${NC}"
else
    echo -e "${RED}Overall Status: UNHEALTHY${NC}"
fi

exit $OVERALL_STATUS
