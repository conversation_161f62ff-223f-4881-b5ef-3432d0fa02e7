#!/bin/bash

# Secrets Setup Script for FraudShield Production
# This script generates and configures all required secrets

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up production secrets...${NC}"

# Create secrets directory
mkdir -p secrets

# Function to generate random password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -hex 64
}

# Generate database password
if [ ! -f "secrets/db_password.txt" ]; then
    echo "Generating database password..."
    generate_password > secrets/db_password.txt
    echo -e "${GREEN}Database password generated${NC}"
else
    echo -e "${YELLOW}Database password already exists${NC}"
fi

# Generate JWT secret
if [ ! -f "secrets/jwt_secret.txt" ]; then
    echo "Generating JWT secret..."
    generate_jwt_secret > secrets/jwt_secret.txt
    echo -e "${GREEN}JWT secret generated${NC}"
else
    echo -e "${YELLOW}JWT secret already exists${NC}"
fi

# Generate Grafana admin password
if [ ! -f "secrets/grafana_password.txt" ]; then
    echo "Generating Grafana admin password..."
    generate_password > secrets/grafana_password.txt
    echo -e "${GREEN}Grafana admin password generated${NC}"
else
    echo -e "${YELLOW}Grafana admin password already exists${NC}"
fi

# Generate InfluxDB token
if [ ! -f "secrets/influxdb_token.txt" ]; then
    echo "Generating InfluxDB token..."
    generate_jwt_secret > secrets/influxdb_token.txt
    echo -e "${GREEN}InfluxDB token generated${NC}"
else
    echo -e "${YELLOW}InfluxDB token already exists${NC}"
fi

# Set proper permissions
chmod 600 secrets/*
chmod 700 secrets

echo -e "${GREEN}Secrets setup completed${NC}"
echo ""
echo "Generated secrets:"
echo "- Database password: secrets/db_password.txt"
echo "- JWT secret: secrets/jwt_secret.txt"
echo "- Grafana admin password: secrets/grafana_password.txt"
echo "- InfluxDB token: secrets/influxdb_token.txt"
echo ""
echo -e "${YELLOW}IMPORTANT: Store these secrets securely and never commit them to version control${NC}"

# Create .env file for production
cat > .env.production << EOF
# Production Environment Variables
ENVIRONMENT=production
LOG_LEVEL=INFO

# Database Configuration
DB_PASSWORD_FILE=/run/secrets/db_password
JWT_SECRET_FILE=/run/secrets/jwt_secret

# Monitoring Configuration
GRAFANA_PASSWORD_FILE=/run/secrets/grafana_password
INFLUXDB_TOKEN_FILE=/run/secrets/influxdb_token

# Security Configuration
ENABLE_HTTPS=true
ENABLE_RATE_LIMITING=true
ENABLE_AUDIT_LOGGING=true

# Performance Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=30
KEEP_ALIVE=2

# Backup Configuration
BACKUP_RETENTION_DAYS=30
ENABLE_S3_BACKUP=false
# AWS_S3_BUCKET=fraudshield-backups
EOF

echo -e "${GREEN}Production environment file created: .env.production${NC}"
