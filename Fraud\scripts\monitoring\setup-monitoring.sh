#!/bin/bash

# Monitoring Setup Script for FraudShield
# This script configures comprehensive monitoring and alerting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== FraudShield Monitoring Setup ===${NC}"

# Create monitoring directories
echo -e "${YELLOW}Creating monitoring directories...${NC}"
mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources},alertmanager,logs}

# Setup Prometheus configuration
echo -e "${YELLOW}Setting up Prometheus configuration...${NC}"
if [ ! -f "monitoring/prometheus/prometheus.yml" ]; then
    echo -e "${RED}Prometheus configuration not found!${NC}"
    exit 1
fi

# Setup Grafana dashboards
echo -e "${YELLOW}Setting up Grafana dashboards...${NC}"
DASHBOARDS=(
    "fraud-detection-overview.json"
    "system-performance.json"
    "ml-model-monitoring.json"
    "business-metrics.json"
)

for dashboard in "${DASHBOARDS[@]}"; do
    if [ ! -f "monitoring/grafana/dashboards/$dashboard" ]; then
        echo -e "${YELLOW}Creating $dashboard...${NC}"
        # Dashboard files are already created above
    fi
done

# Setup log rotation
echo -e "${YELLOW}Setting up log rotation...${NC}"
cat > monitoring/logs/logrotate.conf << EOF
/var/log/fraudshield/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose restart rsyslog
    endscript
}
EOF

# Create monitoring docker-compose override
echo -e "${YELLOW}Creating monitoring docker-compose override...${NC}"
cat > docker-compose.monitoring.yml << EOF
version: '3.8'

services:
  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - fraudshield-network

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    restart: unless-stopped
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    ports:
      - "8080:8080"
    networks:
      - fraudshield-network

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: "***********************************************/fraudshield?sslmode=disable"
    ports:
      - "9187:9187"
    depends_on:
      - postgres
    networks:
      - fraudshield-network

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: "redis://redis:6379"
    ports:
      - "9121:9121"
    depends_on:
      - redis
    networks:
      - fraudshield-network

  # Kafka Exporter
  kafka-exporter:
    image: danielqsj/kafka-exporter:latest
    container_name: kafka-exporter
    restart: unless-stopped
    command:
      - '--kafka.server=kafka:9092'
    ports:
      - "9308:9308"
    depends_on:
      - kafka
    networks:
      - fraudshield-network

  # Elasticsearch for log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - fraudshield-network

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./monitoring/logstash/config:/usr/share/logstash/config:ro
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
    networks:
      - fraudshield-network

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - fraudshield-network

volumes:
  elasticsearch_data:

networks:
  fraudshield-network:
    external: true
EOF

# Create Logstash pipeline configuration
echo -e "${YELLOW}Setting up Logstash pipeline...${NC}"
mkdir -p monitoring/logstash/{pipeline,config}

cat > monitoring/logstash/pipeline/logstash.conf << EOF
input {
  beats {
    port => 5044
  }
  tcp {
    port => 5000
    codec => json
  }
}

filter {
  if [fields][service] {
    mutate {
      add_field => { "service" => "%{[fields][service]}" }
    }
  }
  
  if [service] == "fraudshield-backend" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "fraudshield-logs-%{+YYYY.MM.dd}"
  }
  
  if [level] == "ERROR" or [level] == "CRITICAL" {
    http {
      url => "http://alertmanager:9093/api/v1/alerts"
      http_method => "post"
      format => "json"
      mapping => {
        "alerts" => [
          {
            "labels" => {
              "alertname" => "ApplicationError"
              "service" => "%{service}"
              "severity" => "warning"
            }
            "annotations" => {
              "summary" => "Application error detected"
              "description" => "%{message}"
            }
          }
        ]
      }
    }
  }
}
EOF

# Create performance monitoring script
echo -e "${YELLOW}Creating performance monitoring script...${NC}"
cat > scripts/monitoring/performance-monitor.sh << 'EOF'
#!/bin/bash

# Performance Monitoring Script
# Collects and reports key performance metrics

METRICS_FILE="/tmp/fraudshield-metrics.json"
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Collect system metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.2f", ($3/$2) * 100.0)}')
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')

# Collect application metrics
RESPONSE_TIME=$(curl -w "%{time_total}" -s -o /dev/null http://localhost:8000/health)
ERROR_COUNT=$(docker logs fraudshield-backend 2>&1 | grep -c "ERROR" || echo 0)

# Create metrics JSON
cat > $METRICS_FILE << EOL
{
  "timestamp": "$TIMESTAMP",
  "system": {
    "cpu_usage": $CPU_USAGE,
    "memory_usage": $MEMORY_USAGE,
    "disk_usage": $DISK_USAGE
  },
  "application": {
    "response_time": $RESPONSE_TIME,
    "error_count": $ERROR_COUNT
  }
}
EOL

# Send metrics to monitoring system
curl -X POST -H "Content-Type: application/json" \
     -d @$METRICS_FILE \
     http://localhost:8086/write?db=fraudshield || true

echo "Performance metrics collected and sent"
EOF

chmod +x scripts/monitoring/performance-monitor.sh

# Setup cron job for performance monitoring
echo -e "${YELLOW}Setting up performance monitoring cron job...${NC}"
(crontab -l 2>/dev/null; echo "*/5 * * * * /path/to/fraudshield/scripts/monitoring/performance-monitor.sh") | crontab -

echo -e "${GREEN}Monitoring setup completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Start monitoring services: docker-compose -f docker-compose.monitoring.yml up -d"
echo "2. Access Grafana: http://localhost:3001 (admin/admin)"
echo "3. Access Prometheus: http://localhost:9090"
echo "4. Access Kibana: http://localhost:5601"
echo "5. Configure alert notifications in Alertmanager"
EOF
