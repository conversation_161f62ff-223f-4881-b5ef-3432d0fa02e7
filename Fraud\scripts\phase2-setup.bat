@echo off
REM Phase 2 Setup Script for FraudShield (Windows)
REM Sets up data engineering and real-time ML pipeline

echo.
echo 🚀 Setting up FraudShield Phase 2: Data Engineering ^& Real-Time ML Pipeline
echo ============================================================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Create necessary directories
echo.
echo 📁 Creating necessary directories...
if not exist "data\models" mkdir data\models
if not exist "logs" mkdir logs
if not exist "monitoring\grafana\dashboards" mkdir monitoring\grafana\dashboards
if not exist "monitoring\grafana\datasources" mkdir monitoring\grafana\datasources
if not exist "monitoring\prometheus" mkdir monitoring\prometheus

echo ✅ Directories created

REM Create environment file
echo.
echo ⚙️ Creating environment configuration...
(
echo # Environment
echo ENVIRONMENT=development
echo DEBUG=true
echo.
echo # Database
echo DATABASE_URL=***********************************************/fraudshield
echo.
echo # Redis
echo REDIS_URL=redis://redis:6379
echo.
echo # Kafka
echo KAFKA_BOOTSTRAP_SERVERS=kafka:9092
echo KAFKA_TOPIC_RAW_TRANSACTIONS=raw-transactions
echo KAFKA_TOPIC_ENRICHED_TRANSACTIONS=enriched-transactions
echo KAFKA_TOPIC_DLQ=dead-letter-queue
echo.
echo # InfluxDB
echo INFLUXDB_URL=http://influxdb:8086
echo INFLUXDB_TOKEN=fraudshield-token
echo INFLUXDB_ORG=fraudshield
echo INFLUXDB_BUCKET=transactions
echo.
echo # Feature Store
echo FEATURE_STORE_URL=http://feature-store:8002
echo FEATURE_STORE_TIMEOUT=5
echo.
echo # ML Service
echo ML_SERVICE_URL=http://ml-service:8001
echo.
echo # Security
echo JWT_SECRET_KEY=your-secret-key-change-in-production
echo SECRET_KEY=your-secret-key-change-in-production
echo.
echo # Monitoring
echo ENABLE_METRICS=true
echo LOG_LEVEL=INFO
echo LOG_FORMAT=json
echo.
echo # Data Quality Thresholds
echo NULL_RATE_THRESHOLD=0.05
echo DUPLICATE_RATE_THRESHOLD=0.01
echo SCHEMA_VIOLATION_THRESHOLD=0.02
echo MIN_TRANSACTIONS_PER_MINUTE=10
echo MAX_TRANSACTIONS_PER_MINUTE=10000
echo.
echo # Alerts
echo ENABLE_EMAIL_ALERTS=false
echo ALERT_EMAIL_RECIPIENTS=<EMAIL>
echo SMTP_SERVER=localhost
echo SMTP_PORT=587
) > .env

echo ✅ Environment file created

REM Create Prometheus configuration
echo.
echo 📊 Creating Prometheus configuration...
(
echo global:
echo   scrape_interval: 15s
echo   evaluation_interval: 15s
echo.
echo rule_files:
echo.
echo scrape_configs:
echo   - job_name: 'prometheus'
echo     static_configs:
echo       - targets: ['localhost:9090']
echo.
echo   - job_name: 'fraudshield-backend'
echo     static_configs:
echo       - targets: ['backend:8000']
echo     metrics_path: '/metrics'
echo.
echo   - job_name: 'fraudshield-ml-service'
echo     static_configs:
echo       - targets: ['ml-service:8001']
echo     metrics_path: '/metrics'
echo.
echo   - job_name: 'fraudshield-feature-store'
echo     static_configs:
echo       - targets: ['feature-store:8002']
echo     metrics_path: '/metrics'
) > monitoring\prometheus\prometheus.yml

echo ✅ Prometheus configuration created

REM Create Grafana configuration
echo.
echo 📈 Creating Grafana configuration...
(
echo apiVersion: 1
echo.
echo datasources:
echo   - name: Prometheus
echo     type: prometheus
echo     access: proxy
echo     url: http://prometheus:9090
echo     isDefault: true
echo.
echo   - name: InfluxDB
echo     type: influxdb
echo     access: proxy
echo     url: http://influxdb:8086
echo     database: transactions
echo     user: admin
echo     password: password123
) > monitoring\grafana\datasources\datasources.yml

echo ✅ Grafana configuration created

REM Build and start services
echo.
echo 🏗️ Building and starting Phase 2 services...
echo This may take several minutes...

docker-compose build
if %errorlevel% neq 0 (
    echo ❌ Failed to build services
    pause
    exit /b 1
)

REM Start infrastructure services first
echo.
echo 🚀 Starting infrastructure services...
docker-compose up -d postgres redis zookeeper kafka influxdb

echo ⏳ Waiting for infrastructure services to be ready...
timeout /t 30 /nobreak >nul

REM Start application services
echo.
echo 🚀 Starting application services...
docker-compose up -d backend ml-service feature-store stream-processor data-quality-monitor

echo ⏳ Waiting for application services to be ready...
timeout /t 20 /nobreak >nul

REM Start monitoring services
echo.
echo 📊 Starting monitoring services...
docker-compose up -d prometheus grafana

echo ✅ All services started

REM Initialize Kafka topics
echo.
echo 📡 Initializing Kafka topics...
timeout /t 10 /nobreak >nul

docker-compose exec -T kafka kafka-topics --create --topic raw-transactions --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 2>nul
docker-compose exec -T kafka kafka-topics --create --topic enriched-transactions --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 2>nul
docker-compose exec -T kafka kafka-topics --create --topic dead-letter-queue --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1 2>nul
docker-compose exec -T kafka kafka-topics --create --topic fraud-alerts --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1 2>nul

echo ✅ Kafka topics initialized

REM Verify services
echo.
echo 🔍 Verifying services...
docker-compose ps

echo.
echo 🎉 Phase 2 setup completed successfully!
echo.
echo 📊 Access points:
echo   • Backend API: http://localhost:8000
echo   • ML Service: http://localhost:8001
echo   • Feature Store: http://localhost:8002
echo   • Grafana Dashboard: http://localhost:3001 (admin/admin)
echo   • Prometheus: http://localhost:9090
echo   • InfluxDB: http://localhost:8086
echo.
echo 🔧 Next steps:
echo   1. Install Python dependencies: pip install kafka-python pandas requests
echo   2. Run sample data ingestion: python scripts\ingest-sample-data.py
echo   3. Test the pipeline: python scripts\test-phase2.py
echo   4. Check service logs: docker-compose logs -f [service-name]
echo   5. Monitor data quality in Grafana dashboards
echo.
echo 📚 Documentation: Check docs\PHASE2_COMPLETION.md for detailed guides
echo.
pause
