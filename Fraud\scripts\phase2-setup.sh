#!/bin/bash

# Phase 2 Setup Script for FraudShield
# Sets up data engineering and real-time ML pipeline

set -e

echo "🚀 Setting up FraudShield Phase 2: Data Engineering & Real-Time ML Pipeline"
echo "============================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON><PERSON> and <PERSON><PERSON> Compose are installed
check_prerequisites() {
    print_status "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."

    mkdir -p data/models
    mkdir -p logs
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p monitoring/prometheus

    print_success "Directories created"
}

# Create environment file for Phase 2 services
create_env_file() {
    print_status "Creating environment configuration..."

    cat > .env << EOF
# Environment
ENVIRONMENT=development
DEBUG=true

# Database
DATABASE_URL=***********************************************/fraudshield

# Redis
REDIS_URL=redis://redis:6379

# Kafka
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
KAFKA_TOPIC_RAW_TRANSACTIONS=raw-transactions
KAFKA_TOPIC_ENRICHED_TRANSACTIONS=enriched-transactions
KAFKA_TOPIC_DLQ=dead-letter-queue

# InfluxDB
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=fraudshield-token
INFLUXDB_ORG=fraudshield
INFLUXDB_BUCKET=transactions

# Feature Store
FEATURE_STORE_URL=http://feature-store:8002
FEATURE_STORE_TIMEOUT=5

# ML Service
ML_SERVICE_URL=http://ml-service:8001

# Security
JWT_SECRET_KEY=your-secret-key-change-in-production
SECRET_KEY=your-secret-key-change-in-production

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
LOG_FORMAT=json

# Data Quality Thresholds
NULL_RATE_THRESHOLD=0.05
DUPLICATE_RATE_THRESHOLD=0.01
SCHEMA_VIOLATION_THRESHOLD=0.02
MIN_TRANSACTIONS_PER_MINUTE=10
MAX_TRANSACTIONS_PER_MINUTE=10000

# Alerts (configure as needed)
ENABLE_EMAIL_ALERTS=false
ALERT_EMAIL_RECIPIENTS=<EMAIL>
SMTP_SERVER=localhost
SMTP_PORT=587
EOF

    print_success "Environment file created"
}

# Create Prometheus configuration
create_prometheus_config() {
    print_status "Creating Prometheus configuration..."

    mkdir -p monitoring/prometheus

    cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'fraudshield-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'fraudshield-ml-service'
    static_configs:
      - targets: ['ml-service:8001']
    metrics_path: '/metrics'

  - job_name: 'fraudshield-feature-store'
    static_configs:
      - targets: ['feature-store:8002']
    metrics_path: '/metrics'
EOF

    print_success "Prometheus configuration created"
}

# Create Grafana datasource configuration
create_grafana_config() {
    print_status "Creating Grafana configuration..."

    mkdir -p monitoring/grafana/datasources
    mkdir -p monitoring/grafana/dashboards

    cat > monitoring/grafana/datasources/datasources.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true

  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: transactions
    user: admin
    password: password123
EOF

    cat > monitoring/grafana/dashboards/dashboards.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

    print_success "Grafana configuration created"
}

# Initialize Kafka topics
init_kafka_topics() {
    print_status "Initializing Kafka topics..."

    # Wait for Kafka to be ready
    sleep 10

    docker-compose exec kafka kafka-topics --create --topic raw-transactions --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 || true
    docker-compose exec kafka kafka-topics --create --topic enriched-transactions --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 || true
    docker-compose exec kafka kafka-topics --create --topic dead-letter-queue --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1 || true
    docker-compose exec kafka kafka-topics --create --topic fraud-alerts --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1 || true

    print_success "Kafka topics initialized"
}

# Build and start services
start_services() {
    print_status "Building and starting Phase 2 services..."

    # Build all services
    docker-compose build

    # Start infrastructure services first
    docker-compose up -d postgres redis zookeeper kafka influxdb

    print_status "Waiting for infrastructure services to be ready..."
    sleep 30

    # Start application services
    docker-compose up -d backend ml-service feature-store stream-processor data-quality-monitor

    print_status "Waiting for application services to be ready..."
    sleep 20

    # Start monitoring services
    docker-compose up -d prometheus grafana

    print_success "All services started"
}

# Verify services are running
verify_services() {
    print_status "Verifying services..."

    services=(
        "postgres:5432"
        "redis:6379"
        "kafka:9092"
        "influxdb:8086"
        "backend:8000"
        "ml-service:8001"
        "feature-store:8002"
        "prometheus:9090"
        "grafana:3001"
    )

    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if docker-compose ps | grep -q "$name.*Up"; then
            print_success "$name service is running"
        else
            print_warning "$name service may not be ready yet"
        fi
    done
}

# Create sample data ingestion script
create_sample_data_script() {
    print_status "Creating sample data ingestion script..."

    cat > scripts/ingest-sample-data.py << 'EOF'
#!/usr/bin/env python3
"""
Sample data ingestion script for Phase 2 testing
"""

import json
import time
import uuid
from datetime import datetime
from kafka import KafkaProducer
import pandas as pd

def create_producer():
    return KafkaProducer(
        bootstrap_servers=['localhost:9092'],
        value_serializer=lambda x: json.dumps(x).encode('utf-8')
    )

def load_sample_data():
    """Load sample transaction data"""
    try:
        df = pd.read_csv('fraud_detection_samples.csv')
        return df.to_dict('records')
    except FileNotFoundError:
        print("Sample data file not found. Creating synthetic data...")
        return create_synthetic_data()

def create_synthetic_data():
    """Create synthetic transaction data"""
    import random

    data = []
    for i in range(100):
        data.append({
            'transaction_id': str(uuid.uuid4()),
            'step': i + 1,
            'type': random.choice(['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT']),
            'amount': round(random.uniform(10, 10000), 2),
            'name_orig': f'C{random.randint(100, 999)}',
            'oldbalance_org': round(random.uniform(0, 50000), 2),
            'newbalance_orig': 0,  # Will be calculated
            'name_dest': f'{"M" if random.random() < 0.3 else "C"}{random.randint(100, 999)}',
            'oldbalance_dest': round(random.uniform(0, 50000), 2),
            'newbalance_dest': 0,  # Will be calculated
            'timestamp': datetime.utcnow().isoformat()
        })

        # Calculate new balances
        data[i]['newbalance_orig'] = max(0, data[i]['oldbalance_org'] - data[i]['amount'])
        if not data[i]['name_dest'].startswith('M'):
            data[i]['newbalance_dest'] = data[i]['oldbalance_dest'] + data[i]['amount']
        else:
            data[i]['newbalance_dest'] = 0  # Merchant accounts

    return data

def main():
    print("Starting sample data ingestion...")

    producer = create_producer()
    data = load_sample_data()

    print(f"Ingesting {len(data)} transactions...")

    for i, transaction in enumerate(data):
        # Add unique transaction ID if not present
        if 'transaction_id' not in transaction:
            transaction['transaction_id'] = str(uuid.uuid4())

        # Add timestamp if not present
        if 'timestamp' not in transaction:
            transaction['timestamp'] = datetime.utcnow().isoformat()

        # Send to Kafka
        producer.send('raw-transactions', value=transaction)

        print(f"Sent transaction {i+1}/{len(data)}: {transaction['transaction_id']}")

        # Small delay to simulate real-time flow
        time.sleep(0.1)

    producer.flush()
    producer.close()

    print("Sample data ingestion completed!")

if __name__ == "__main__":
    main()
EOF

    chmod +x scripts/ingest-sample-data.py

    print_success "Sample data ingestion script created"
}

# Main execution
main() {
    echo
    print_status "Starting Phase 2 setup process..."
    echo

    check_prerequisites
    create_directories
    create_env_file
    create_prometheus_config
    create_grafana_config
    start_services

    # Initialize Kafka topics after services are up
    init_kafka_topics

    verify_services
    create_sample_data_script

    echo
    print_success "Phase 2 setup completed successfully!"
    echo
    echo "🎉 Your FraudShield Phase 2 environment is ready!"
    echo
    echo "📊 Access points:"
    echo "  • Backend API: http://localhost:8000"
    echo "  • ML Service: http://localhost:8001"
    echo "  • Feature Store: http://localhost:8002"
    echo "  • Grafana Dashboard: http://localhost:3001 (admin/admin)"
    echo "  • Prometheus: http://localhost:9090"
    echo "  • InfluxDB: http://localhost:8086"
    echo
    echo "🔧 Next steps:"
    echo "  1. Run sample data ingestion: python scripts/ingest-sample-data.py"
    echo "  2. Check service logs: docker-compose logs -f [service-name]"
    echo "  3. Monitor data quality in Grafana dashboards"
    echo "  4. Test feature extraction via Feature Store API"
    echo
    echo "📚 Documentation: Check docs/ directory for detailed guides"
    echo
}

# Run main function
main "$@"
