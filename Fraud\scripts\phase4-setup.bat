@echo off
REM Phase 4 Setup Script - Backend Development with Real-Time Processing
REM This script sets up the complete Phase 4 backend infrastructure for Windows

echo 🚀 Starting Phase 4 Setup - Backend Development with Real-Time Processing
echo ==================================================================

REM Check if running from project root
if not exist "docker-compose.yml" (
    echo [ERROR] Please run this script from the project root directory
    exit /b 1
)

REM Create necessary directories
echo [INFO] Creating directory structure...
if not exist "logs" mkdir logs
if not exist "data\postgres" mkdir data\postgres
if not exist "data\redis" mkdir data\redis
if not exist "data\kafka" mkdir data\kafka
echo [SUCCESS] Directory structure created

REM Check for required tools
echo [INFO] Checking for required tools...

where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed or not in PATH
    exit /b 1
) else (
    echo [SUCCESS] Docker is installed
)

where docker-compose >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not installed or not in PATH
    exit /b 1
) else (
    echo [SUCCESS] Docker Compose is installed
)

where python >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    exit /b 1
) else (
    echo [SUCCESS] Python is installed
)

where pip >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Pip is not installed or not in PATH
    exit /b 1
) else (
    echo [SUCCESS] Pip is installed
)

REM Create environment file if it doesn't exist
echo [INFO] Setting up environment configuration...
if not exist ".env" (
    echo # Environment Configuration > .env
    echo ENVIRONMENT=development >> .env
    echo DEBUG=true >> .env
    echo. >> .env
    echo # Database Configuration >> .env
    echo DATABASE_URL=postgresql+asyncpg://fraudshield:fraudshield123@localhost:5432/fraudshield >> .env
    echo DATABASE_POOL_SIZE=10 >> .env
    echo DATABASE_MAX_OVERFLOW=20 >> .env
    echo. >> .env
    echo # Redis Configuration >> .env
    echo REDIS_URL=redis://localhost:6379 >> .env
    echo REDIS_CACHE_TTL=3600 >> .env
    echo. >> .env
    echo # Kafka Configuration >> .env
    echo KAFKA_BOOTSTRAP_SERVERS=localhost:9092 >> .env
    echo KAFKA_TOPIC_TRANSACTIONS=transactions >> .env
    echo KAFKA_TOPIC_ALERTS=fraud_alerts >> .env
    echo. >> .env
    echo # ML Service Configuration >> .env
    echo ML_SERVICE_URL=http://localhost:8001 >> .env
    echo ML_SERVICE_TIMEOUT=30 >> .env
    echo. >> .env
    echo # Feature Store Configuration >> .env
    echo FEATURE_STORE_URL=http://localhost:8002 >> .env
    echo. >> .env
    echo # Security Configuration >> .env
    echo SECRET_KEY=your-super-secret-key-change-this-in-production >> .env
    echo ACCESS_TOKEN_EXPIRE_MINUTES=30 >> .env
    echo REFRESH_TOKEN_EXPIRE_DAYS=7 >> .env
    echo. >> .env
    echo # API Configuration >> .env
    echo ALLOWED_HOSTS=["*"] >> .env
    echo CORS_ORIGINS=["*"] >> .env
    echo. >> .env
    echo # Fraud Detection Thresholds >> .env
    echo FRAUD_SCORE_THRESHOLD_HIGH=0.8 >> .env
    echo FRAUD_SCORE_THRESHOLD_MEDIUM=0.5 >> .env
    echo. >> .env
    echo # Rate Limiting >> .env
    echo RATE_LIMIT_REQUESTS=100 >> .env
    echo RATE_LIMIT_WINDOW=60 >> .env
    echo. >> .env
    echo # Notification Configuration (Optional) >> .env
    echo SLACK_WEBHOOK_URL= >> .env
    echo SMTP_HOST= >> .env
    echo SMTP_PORT=587 >> .env
    echo SMTP_USERNAME= >> .env
    echo SMTP_PASSWORD= >> .env
    echo. >> .env
    echo # Monitoring >> .env
    echo ENABLE_METRICS=true >> .env
    echo METRICS_PORT=9090 >> .env

    echo [SUCCESS] Environment file created (.env)
) else (
    echo [WARNING] Environment file already exists, skipping creation
)

REM Start infrastructure services
echo [INFO] Starting infrastructure services with Docker Compose...
docker-compose up -d postgres redis kafka zookeeper

REM Wait for services to be ready
echo [INFO] Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Check if PostgreSQL is ready
echo [INFO] Checking PostgreSQL connection...
for /l %%i in (1,1,30) do (
    docker-compose exec -T postgres pg_isready -U fraudshield >nul 2>nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] PostgreSQL is ready
        goto :postgres_ready
    )
    timeout /t 2 /nobreak >nul
)
echo [ERROR] PostgreSQL failed to start
exit /b 1
:postgres_ready

REM Check if Redis is ready
echo [INFO] Checking Redis connection...
for /l %%i in (1,1,30) do (
    docker-compose exec -T redis redis-cli ping >nul 2>nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] Redis is ready
        goto :redis_ready
    )
    timeout /t 2 /nobreak >nul
)
echo [ERROR] Redis failed to start
exit /b 1
:redis_ready

REM Set up Python virtual environment
echo [INFO] Setting up Python virtual environment...
if not exist "venv" (
    python -m venv venv
    echo [SUCCESS] Virtual environment created
) else (
    echo [WARNING] Virtual environment already exists
)

REM Activate virtual environment and install dependencies
echo [INFO] Installing Python dependencies...
call venv\Scripts\activate.bat

REM Upgrade pip
python -m pip install --upgrade pip

REM Install backend dependencies
cd backend
pip install -r requirements.txt
cd ..

REM Install ML service dependencies
cd ml-service
pip install -r requirements.txt
cd ..

REM Install feature store dependencies
cd feature-store
pip install -r requirements.txt
cd ..

REM Install stream processor dependencies
cd stream-processor
pip install -r requirements.txt
cd ..

echo [SUCCESS] Python dependencies installed

REM Initialize database
echo [INFO] Initializing database...
cd backend
set DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/fraudshield

REM Run database migrations (if alembic is set up)
if exist "alembic" (
    alembic upgrade head
    echo [SUCCESS] Database migrations completed
) else (
    echo [WARNING] Alembic not configured, skipping migrations
)

cd ..

REM Create Kafka topics
echo [INFO] Creating Kafka topics...
docker-compose exec -T kafka kafka-topics.sh --create --topic transactions --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
docker-compose exec -T kafka kafka-topics.sh --create --topic fraud_alerts --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
echo [SUCCESS] Kafka topics created

REM Start application services
echo [INFO] Starting application services...

REM Start ML service
echo [INFO] Starting ML service...
cd ml-service
start /b python main.py > ..\logs\ml-service.log 2>&1
cd ..
echo [SUCCESS] ML service started

REM Start feature store
echo [INFO] Starting feature store...
cd feature-store
start /b python main.py > ..\logs\feature-store.log 2>&1
cd ..
echo [SUCCESS] Feature store started

REM Start stream processor
echo [INFO] Starting stream processor...
cd stream-processor
start /b python main.py > ..\logs\stream-processor.log 2>&1
cd ..
echo [SUCCESS] Stream processor started

REM Wait for services to initialize
echo [INFO] Waiting for services to initialize...
timeout /t 5 /nobreak >nul

REM Start backend API
echo [INFO] Starting backend API...
cd backend
start /b uvicorn main:app --host 0.0.0.0 --port 8000 --reload > ..\logs\backend.log 2>&1
cd ..
echo [SUCCESS] Backend API started

REM Wait for backend to start
echo [INFO] Waiting for backend API to be ready...
for /l %%i in (1,1,30) do (
    curl -s http://localhost:8000/health >nul 2>nul
    if !errorlevel! equ 0 (
        echo [SUCCESS] Backend API is ready
        goto :backend_ready
    )
    timeout /t 2 /nobreak >nul
)
echo [ERROR] Backend API failed to start
exit /b 1
:backend_ready

REM Run health checks
echo [INFO] Running health checks...

REM Check backend health
curl -s http://localhost:8000/health | findstr "healthy" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Backend API health check passed
) else (
    echo [ERROR] Backend API health check failed
)

REM Check ML service health
curl -s http://localhost:8001/health | findstr "healthy" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] ML service health check passed
) else (
    echo [WARNING] ML service health check failed (may still be starting)
)

REM Check feature store health
curl -s http://localhost:8002/health | findstr "healthy" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Feature store health check passed
) else (
    echo [WARNING] Feature store health check failed (may still be starting)
)

REM Display service information
echo.
echo ==================================================================
echo [SUCCESS] Phase 4 Setup Complete!
echo ==================================================================
echo.
echo 🌐 Service URLs:
echo    Backend API:     http://localhost:8000
echo    API Docs:        http://localhost:8000/docs
echo    ML Service:      http://localhost:8001
echo    Feature Store:   http://localhost:8002
echo.
echo 📊 Infrastructure:
echo    PostgreSQL:      localhost:5432
echo    Redis:           localhost:6379
echo    Kafka:           localhost:9092
echo.
echo 📁 Log Files:
echo    Backend:         logs\backend.log
echo    ML Service:      logs\ml-service.log
echo    Feature Store:   logs\feature-store.log
echo    Stream Processor: logs\stream-processor.log
echo.
echo 🔧 Management Commands:
echo    Stop services:   docker-compose down
echo    View logs:       type logs\backend.log
echo    Check health:    curl http://localhost:8000/health
echo.
echo 📚 Next Steps:
echo    1. Visit http://localhost:8000/docs for API documentation
echo    2. Test the fraud detection API with sample transactions
echo    3. Monitor logs for any issues
echo    4. Configure notifications (Slack, email) in .env file
echo.
echo [SUCCESS] Phase 4 is ready for testing and development!

pause
