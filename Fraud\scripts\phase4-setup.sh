#!/bin/bash

# Phase 4 Setup Script - Backend Development with Real-Time Processing
# This script sets up the complete Phase 4 backend infrastructure

set -e

echo "🚀 Starting Phase 4 Setup - Backend Development with Real-Time Processing"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running from project root
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Create necessary directories
print_status "Creating directory structure..."
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p data/kafka
print_success "Directory structure created"

# Check for required tools
print_status "Checking for required tools..."

check_tool() {
    if command -v $1 &> /dev/null; then
        print_success "$1 is installed"
        return 0
    else
        print_warning "$1 is not installed"
        return 1
    fi
}

MISSING_TOOLS=0

if ! check_tool "docker"; then
    MISSING_TOOLS=1
fi

if ! check_tool "docker-compose"; then
    MISSING_TOOLS=1
fi

if ! check_tool "python3"; then
    MISSING_TOOLS=1
fi

if ! check_tool "pip"; then
    MISSING_TOOLS=1
fi

if [ $MISSING_TOOLS -eq 1 ]; then
    print_error "Please install missing tools before continuing"
    exit 1
fi

# Create environment file if it doesn't exist
print_status "Setting up environment configuration..."
if [ ! -f ".env" ]; then
    cat > .env << EOF
# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/fraudshield
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_CACHE_TTL=3600

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_TRANSACTIONS=transactions
KAFKA_TOPIC_ALERTS=fraud_alerts

# ML Service Configuration
ML_SERVICE_URL=http://localhost:8001
ML_SERVICE_TIMEOUT=30

# Feature Store Configuration
FEATURE_STORE_URL=http://localhost:8002

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
ALLOWED_HOSTS=["*"]
CORS_ORIGINS=["*"]

# Fraud Detection Thresholds
FRAUD_SCORE_THRESHOLD_HIGH=0.8
FRAUD_SCORE_THRESHOLD_MEDIUM=0.5

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Notification Configuration (Optional)
SLACK_WEBHOOK_URL=
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
EOF
    print_success "Environment file created (.env)"
else
    print_warning "Environment file already exists, skipping creation"
fi

# Start infrastructure services
print_status "Starting infrastructure services with Docker Compose..."
docker-compose up -d postgres redis kafka zookeeper

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check if PostgreSQL is ready
print_status "Checking PostgreSQL connection..."
for i in {1..30}; do
    if docker-compose exec -T postgres pg_isready -U fraudshield &> /dev/null; then
        print_success "PostgreSQL is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    sleep 2
done

# Check if Redis is ready
print_status "Checking Redis connection..."
for i in {1..30}; do
    if docker-compose exec -T redis redis-cli ping &> /dev/null; then
        print_success "Redis is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Redis failed to start"
        exit 1
    fi
    sleep 2
done

# Set up Python virtual environment
print_status "Setting up Python virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_success "Virtual environment created"
else
    print_warning "Virtual environment already exists"
fi

# Activate virtual environment and install dependencies
print_status "Installing Python dependencies..."
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install backend dependencies
cd backend
pip install -r requirements.txt
cd ..

# Install ML service dependencies
cd ml-service
pip install -r requirements.txt
cd ..

# Install feature store dependencies
cd feature-store
pip install -r requirements.txt
cd ..

# Install stream processor dependencies
cd stream-processor
pip install -r requirements.txt
cd ..

print_success "Python dependencies installed"

# Initialize database
print_status "Initializing database..."
cd backend
export DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/fraudshield"

# Run database migrations (if alembic is set up)
if [ -d "alembic" ]; then
    alembic upgrade head
    print_success "Database migrations completed"
else
    print_warning "Alembic not configured, skipping migrations"
fi

cd ..

# Create Kafka topics
print_status "Creating Kafka topics..."
docker-compose exec -T kafka kafka-topics.sh --create --topic transactions --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
docker-compose exec -T kafka kafka-topics.sh --create --topic fraud_alerts --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
print_success "Kafka topics created"

# Start application services
print_status "Starting application services..."

# Start ML service
print_status "Starting ML service..."
cd ml-service
nohup python main.py > ../logs/ml-service.log 2>&1 &
ML_PID=$!
echo $ML_PID > ../logs/ml-service.pid
cd ..
print_success "ML service started (PID: $ML_PID)"

# Start feature store
print_status "Starting feature store..."
cd feature-store
nohup python main.py > ../logs/feature-store.log 2>&1 &
FEATURE_PID=$!
echo $FEATURE_PID > ../logs/feature-store.pid
cd ..
print_success "Feature store started (PID: $FEATURE_PID)"

# Start stream processor
print_status "Starting stream processor..."
cd stream-processor
nohup python main.py > ../logs/stream-processor.log 2>&1 &
STREAM_PID=$!
echo $STREAM_PID > ../logs/stream-processor.pid
cd ..
print_success "Stream processor started (PID: $STREAM_PID)"

# Wait for services to initialize
print_status "Waiting for services to initialize..."
sleep 5

# Start backend API
print_status "Starting backend API..."
cd backend
nohup uvicorn main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../logs/backend.pid
cd ..
print_success "Backend API started (PID: $BACKEND_PID)"

# Wait for backend to start
print_status "Waiting for backend API to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health &> /dev/null; then
        print_success "Backend API is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend API failed to start"
        exit 1
    fi
    sleep 2
done

# Run health checks
print_status "Running health checks..."

# Check backend health
if curl -s http://localhost:8000/health | grep -q "healthy"; then
    print_success "Backend API health check passed"
else
    print_error "Backend API health check failed"
fi

# Check ML service health
if curl -s http://localhost:8001/health | grep -q "healthy"; then
    print_success "ML service health check passed"
else
    print_warning "ML service health check failed (may still be starting)"
fi

# Check feature store health
if curl -s http://localhost:8002/health | grep -q "healthy"; then
    print_success "Feature store health check passed"
else
    print_warning "Feature store health check failed (may still be starting)"
fi

# Create test data (optional)
print_status "Creating test data..."
python3 scripts/create_test_data.py 2>/dev/null || print_warning "Test data creation script not found"

# Display service information
echo ""
echo "=================================================================="
print_success "Phase 4 Setup Complete!"
echo "=================================================================="
echo ""
echo "🌐 Service URLs:"
echo "   Backend API:     http://localhost:8000"
echo "   API Docs:        http://localhost:8000/docs"
echo "   ML Service:      http://localhost:8001"
echo "   Feature Store:   http://localhost:8002"
echo ""
echo "📊 Infrastructure:"
echo "   PostgreSQL:      localhost:5432"
echo "   Redis:           localhost:6379"
echo "   Kafka:           localhost:9092"
echo ""
echo "📁 Log Files:"
echo "   Backend:         logs/backend.log"
echo "   ML Service:      logs/ml-service.log"
echo "   Feature Store:   logs/feature-store.log"
echo "   Stream Processor: logs/stream-processor.log"
echo ""
echo "🔧 Management Commands:"
echo "   Stop services:   docker-compose down"
echo "   View logs:       tail -f logs/backend.log"
echo "   Check health:    curl http://localhost:8000/health"
echo ""
echo "📚 Next Steps:"
echo "   1. Visit http://localhost:8000/docs for API documentation"
echo "   2. Test the fraud detection API with sample transactions"
echo "   3. Monitor logs for any issues"
echo "   4. Configure notifications (Slack, email) in .env file"
echo ""
print_success "Phase 4 is ready for testing and development!"
