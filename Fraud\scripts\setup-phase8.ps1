# Phase 8 Setup Script for Windows
# This script sets up the Phase 8 deployment and monitoring infrastructure

Write-Host "=== FraudShield Phase 8 Setup ===" -ForegroundColor Blue
Write-Host "Setting up deployment, monitoring, and go-live infrastructure..." -ForegroundColor Yellow

# Create necessary directories
$directories = @(
    "monitoring\prometheus",
    "monitoring\grafana\dashboards",
    "monitoring\grafana\datasources", 
    "monitoring\alertmanager",
    "monitoring\logstash\pipeline",
    "monitoring\logstash\config",
    "monitoring\logs",
    "secrets",
    "backup"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    }
}

# Set script permissions (Windows equivalent)
$scriptFiles = @(
    "scripts\backup\backup-database.sh",
    "scripts\backup\restore-database.sh",
    "scripts\deployment\health-check.sh",
    "scripts\deployment\deploy-production.sh",
    "scripts\deployment\setup-secrets.sh",
    "scripts\monitoring\setup-monitoring.sh",
    "scripts\go-live.sh"
)

foreach ($script in $scriptFiles) {
    if (Test-Path $script) {
        # On Windows, we don't need to set execute permissions for .sh files
        # They will be executed by bash/WSL
        Write-Host "Script ready: $script" -ForegroundColor Green
    }
}

# Verify all Phase 8 components
Write-Host "`nVerifying Phase 8 components..." -ForegroundColor Yellow

$components = @{
    "Production Docker Compose" = "docker-compose.prod.yml"
    "Prometheus Config" = "monitoring\prometheus\prometheus.yml"
    "Alert Rules" = "monitoring\prometheus\alert_rules.yml"
    "Grafana Datasources" = "monitoring\grafana\datasources\datasources.yml"
    "Alertmanager Config" = "monitoring\alertmanager\alertmanager.yml"
    "Health Check Script" = "scripts\deployment\health-check.sh"
    "Deployment Script" = "scripts\deployment\deploy-production.sh"
    "Go-Live Script" = "scripts\go-live.sh"
    "Go-Live Checklist" = "docs\PHASE8_GO_LIVE_CHECKLIST.md"
    "Phase 8 Documentation" = "docs\PHASE8_COMPLETION.md"
}

$allComponentsReady = $true
foreach ($component in $components.GetEnumerator()) {
    if (Test-Path $component.Value) {
        Write-Host "✓ $($component.Key)" -ForegroundColor Green
    } else {
        Write-Host "✗ $($component.Key) - Missing: $($component.Value)" -ForegroundColor Red
        $allComponentsReady = $false
    }
}

if ($allComponentsReady) {
    Write-Host "`n=== Phase 8 Setup Complete! ===" -ForegroundColor Green
    Write-Host "All components are ready for production deployment." -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Review the go-live checklist: docs\PHASE8_GO_LIVE_CHECKLIST.md"
    Write-Host "2. Set up production secrets: bash scripts/deployment/setup-secrets.sh"
    Write-Host "3. Configure monitoring: bash scripts/monitoring/setup-monitoring.sh"
    Write-Host "4. Execute go-live: bash scripts/go-live.sh production latest"
    
    Write-Host "`nAccess Points (after deployment):" -ForegroundColor Cyan
    Write-Host "• Frontend Dashboard: http://localhost:3000"
    Write-Host "• API Documentation: http://localhost:8000/docs"
    Write-Host "• Monitoring Dashboard: http://localhost:3001"
    Write-Host "• Prometheus Metrics: http://localhost:9090"
    Write-Host "• Log Analysis: http://localhost:5601"
    
} else {
    Write-Host "`n=== Setup Issues Detected ===" -ForegroundColor Red
    Write-Host "Please check the missing components above." -ForegroundColor Red
    exit 1
}

Write-Host "`n=== FraudShield is Ready for Production! ===" -ForegroundColor Magenta
