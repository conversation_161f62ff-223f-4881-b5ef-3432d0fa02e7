@echo off
REM FraudShield Setup Script for Windows
REM This script sets up the development environment on Windows

echo 🚀 Setting up FraudShield development environment...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo [SUCCESS] Docker and Docker Compose are installed

REM Create environment file
if not exist .env (
    copy .env.example .env
    echo [SUCCESS] Created .env file from .env.example
    echo [WARNING] Please update the .env file with your specific configuration
) else (
    echo [WARNING] .env file already exists, skipping creation
)

REM Create necessary directories
if not exist data\models mkdir data\models
if not exist logs mkdir logs
if not exist monitoring\grafana\dashboards mkdir monitoring\grafana\dashboards
if not exist monitoring\grafana\datasources mkdir monitoring\grafana\datasources
if not exist monitoring\prometheus mkdir monitoring\prometheus

echo [SUCCESS] Directories created

REM Copy model file if it exists
if exist fraud_detection_model.pkl (
    copy fraud_detection_model.pkl data\models\
    echo [SUCCESS] ML model copied to data\models\
) else (
    echo [WARNING] fraud_detection_model.pkl not found in root directory
    echo [WARNING] Please ensure the model file is available for ML service
)

REM Create Prometheus configuration
echo global: > monitoring\prometheus\prometheus.yml
echo   scrape_interval: 15s >> monitoring\prometheus\prometheus.yml
echo   evaluation_interval: 15s >> monitoring\prometheus\prometheus.yml
echo. >> monitoring\prometheus\prometheus.yml
echo scrape_configs: >> monitoring\prometheus\prometheus.yml
echo   - job_name: 'prometheus' >> monitoring\prometheus\prometheus.yml
echo     static_configs: >> monitoring\prometheus\prometheus.yml
echo       - targets: ['localhost:9090'] >> monitoring\prometheus\prometheus.yml
echo. >> monitoring\prometheus\prometheus.yml
echo   - job_name: 'fraudshield-backend' >> monitoring\prometheus\prometheus.yml
echo     static_configs: >> monitoring\prometheus\prometheus.yml
echo       - targets: ['backend:8000'] >> monitoring\prometheus\prometheus.yml
echo     metrics_path: '/metrics' >> monitoring\prometheus\prometheus.yml
echo. >> monitoring\prometheus\prometheus.yml
echo   - job_name: 'fraudshield-ml-service' >> monitoring\prometheus\prometheus.yml
echo     static_configs: >> monitoring\prometheus\prometheus.yml
echo       - targets: ['ml-service:8001'] >> monitoring\prometheus\prometheus.yml
echo     metrics_path: '/metrics' >> monitoring\prometheus\prometheus.yml

REM Create Grafana datasource configuration
echo apiVersion: 1 > monitoring\grafana\datasources\prometheus.yml
echo. >> monitoring\grafana\datasources\prometheus.yml
echo datasources: >> monitoring\grafana\datasources\prometheus.yml
echo   - name: Prometheus >> monitoring\grafana\datasources\prometheus.yml
echo     type: prometheus >> monitoring\grafana\datasources\prometheus.yml
echo     access: proxy >> monitoring\grafana\datasources\prometheus.yml
echo     url: http://prometheus:9090 >> monitoring\grafana\datasources\prometheus.yml
echo     isDefault: true >> monitoring\grafana\datasources\prometheus.yml

echo [SUCCESS] Monitoring configuration created

REM Build and start services
echo [INFO] Building and starting services...
docker-compose build
docker-compose up -d

echo [SUCCESS] Services started successfully

REM Wait for services to be ready
echo [INFO] Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo.
echo 🎉 FraudShield setup completed!
echo.
echo Service URLs:
echo   Frontend:    http://localhost:3000
echo   Backend API: http://localhost:8000
echo   ML Service:  http://localhost:8001
echo   Grafana:     http://localhost:3001 (admin/admin)
echo   Prometheus:  http://localhost:9090
echo.
echo API Documentation:
echo   Backend:     http://localhost:8000/docs
echo   ML Service:  http://localhost:8001/docs
echo.
echo To stop services: docker-compose down
echo To view logs: docker-compose logs -f [service-name]
echo.

pause
