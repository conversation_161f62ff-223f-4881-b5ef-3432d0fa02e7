#!/bin/bash

# FraudShield Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up FraudShield development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_warning "Node.js is not installed. Frontend development will not be available."
    else
        NODE_VERSION=$(node --version)
        print_success "Node.js $NODE_VERSION is installed"
    fi
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_warning "Python 3 is not installed. Backend development will not be available."
    else
        PYTHON_VERSION=$(python3 --version)
        print_success "$PYTHON_VERSION is installed"
    fi
    
    print_success "Requirements check completed"
}

# Create environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please update the .env file with your specific configuration"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p data/models
    mkdir -p logs
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p monitoring/prometheus
    
    print_success "Directories created"
}

# Copy model file
setup_model() {
    print_status "Setting up ML model..."
    
    if [ -f "fraud_detection_model.pkl" ]; then
        cp fraud_detection_model.pkl data/models/
        print_success "ML model copied to data/models/"
    else
        print_warning "fraud_detection_model.pkl not found in root directory"
        print_warning "Please ensure the model file is available for ML service"
    fi
}

# Setup monitoring configuration
setup_monitoring() {
    print_status "Setting up monitoring configuration..."
    
    # Create Prometheus configuration
    cat > monitoring/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'fraudshield-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'fraudshield-ml-service'
    static_configs:
      - targets: ['ml-service:8001']
    metrics_path: '/metrics'
EOF

    # Create Grafana datasource configuration
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    print_success "Monitoring configuration created"
}

# Install Python dependencies for development
setup_python_dev() {
    if command -v python3 &> /dev/null; then
        print_status "Setting up Python development environment..."
        
        # Create virtual environment
        if [ ! -d "venv" ]; then
            python3 -m venv venv
            print_success "Python virtual environment created"
        fi
        
        # Activate virtual environment and install dependencies
        source venv/bin/activate
        
        # Install backend dependencies
        if [ -f "backend/requirements.txt" ]; then
            pip install -r backend/requirements.txt
            print_success "Backend dependencies installed"
        fi
        
        # Install ML service dependencies
        if [ -f "ml-service/requirements.txt" ]; then
            pip install -r ml-service/requirements.txt
            print_success "ML service dependencies installed"
        fi
        
        deactivate
    fi
}

# Install Node.js dependencies for development
setup_node_dev() {
    if command -v node &> /dev/null; then
        print_status "Setting up Node.js development environment..."
        
        if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then
            cd frontend
            npm install
            cd ..
            print_success "Frontend dependencies installed"
        fi
    fi
}

# Build and start services
start_services() {
    print_status "Building and starting services..."
    
    # Build images
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    print_success "Services started successfully"
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_services_health
}

# Check service health
check_services_health() {
    print_status "Checking service health..."
    
    # Check backend
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Backend service is healthy"
    else
        print_warning "Backend service is not responding"
    fi
    
    # Check ML service
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        print_success "ML service is healthy"
    else
        print_warning "ML service is not responding"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_success "Frontend service is healthy"
    else
        print_warning "Frontend service is not responding"
    fi
}

# Display service URLs
show_urls() {
    print_success "🎉 FraudShield setup completed!"
    echo ""
    echo "Service URLs:"
    echo "  Frontend:    http://localhost:3000"
    echo "  Backend API: http://localhost:8000"
    echo "  ML Service:  http://localhost:8001"
    echo "  Grafana:     http://localhost:3001 (admin/admin)"
    echo "  Prometheus:  http://localhost:9090"
    echo ""
    echo "API Documentation:"
    echo "  Backend:     http://localhost:8000/docs"
    echo "  ML Service:  http://localhost:8001/docs"
    echo ""
    echo "To stop services: docker-compose down"
    echo "To view logs: docker-compose logs -f [service-name]"
    echo ""
}

# Main execution
main() {
    echo "🛡️  FraudShield Development Environment Setup"
    echo "=============================================="
    echo ""
    
    check_requirements
    setup_environment
    create_directories
    setup_model
    setup_monitoring
    setup_python_dev
    setup_node_dev
    start_services
    show_urls
}

# Run main function
main "$@"
