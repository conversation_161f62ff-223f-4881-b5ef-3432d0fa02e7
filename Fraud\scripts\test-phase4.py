#!/usr/bin/env python3
"""
Phase 4 Testing Script
Tests the complete backend API functionality including real-time processing
"""

import asyncio
import json
import time
from typing import Dict, Any, List
import aiohttp
import sys
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
ML_SERVICE_URL = "http://localhost:8001"
FEATURE_STORE_URL = "http://localhost:8002"

class Phase4Tester:
    """Test suite for Phase 4 backend functionality"""

    def __init__(self):
        self.session = None
        self.auth_token = None
        self.test_results = []

    async def setup(self):
        """Setup test environment"""
        self.session = aiohttp.ClientSession()
        print("🔧 Setting up Phase 4 test environment...")

    async def cleanup(self):
        """Cleanup test environment"""
        if self.session:
            await self.session.close()
        print("🧹 Test environment cleaned up")

    async def test_health_checks(self):
        """Test health endpoints for all services"""
        print("\n🏥 Testing Health Checks...")

        services = [
            ("Backend API", f"{BASE_URL}/health"),
            ("Backend Readiness", f"{BASE_URL}/health/ready"),
            ("ML Service", f"{ML_SERVICE_URL}/health"),
            ("Feature Store", f"{FEATURE_STORE_URL}/health")
        ]

        for service_name, url in services:
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"  ✅ {service_name}: {data.get('status', 'OK')}")
                        self.test_results.append(f"✅ {service_name} health check")
                    else:
                        print(f"  ❌ {service_name}: HTTP {response.status}")
                        self.test_results.append(f"❌ {service_name} health check")
            except Exception as e:
                print(f"  ❌ {service_name}: {str(e)}")
                self.test_results.append(f"❌ {service_name} health check")

    async def test_authentication(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication...")

        # Test user registration
        import os
        import uuid
        test_email = os.getenv("TEST_USER_EMAIL", f"test-{uuid.uuid4().hex[:8]}@example.com")
        test_password = os.getenv("TEST_USER_PASSWORD", f"TestPass{uuid.uuid4().hex[:8]}")

        register_data = {
            "email": test_email,
            "password": test_password,
            "full_name": "Test User",
            "role": "analyst"
        }

        try:
            async with self.session.post(
                f"{BASE_URL}/api/v1/auth/register",
                json=register_data
            ) as response:
                if response.status in [200, 201, 400]:  # 400 if user already exists
                    print("  ✅ User registration endpoint working")
                    self.test_results.append("✅ User registration")
                else:
                    print(f"  ❌ User registration failed: HTTP {response.status}")
                    self.test_results.append("❌ User registration")
        except Exception as e:
            print(f"  ❌ User registration error: {str(e)}")
            self.test_results.append("❌ User registration")

        # Test user login
        login_data = {
            "username": test_email,
            "password": test_password
        }

        try:
            async with self.session.post(
                f"{BASE_URL}/api/v1/auth/login",
                data=login_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    print("  ✅ User login successful")
                    self.test_results.append("✅ User login")
                else:
                    print(f"  ❌ User login failed: HTTP {response.status}")
                    self.test_results.append("❌ User login")
        except Exception as e:
            print(f"  ❌ User login error: {str(e)}")
            self.test_results.append("❌ User login")

    async def test_transaction_processing(self):
        """Test transaction processing endpoints"""
        print("\n💳 Testing Transaction Processing...")

        if not self.auth_token:
            print("  ⚠️ Skipping transaction tests - no auth token")
            return

        headers = {"Authorization": f"Bearer {self.auth_token}"}

        # Test single transaction scoring
        transaction_data = {
            "transaction_id": f"test_txn_{int(time.time())}",
            "type": "PAYMENT",
            "amount": 1500.00,
            "currency": "USD",
            "originator": {
                "account_id": "ACC123456",
                "current_balance": 5000.00
            },
            "beneficiary": {
                "account_id": "ACC789012",
                "current_balance": 1000.00
            },
            "metadata": {
                "device_id": "device_test_123",
                "ip_address": "*************",
                "location": {
                    "latitude": 40.7128,
                    "longitude": -74.0060,
                    "country": "US",
                    "city": "New York"
                },
                "channel": "web"
            }
        }

        try:
            start_time = time.time()
            async with self.session.post(
                f"{BASE_URL}/api/v1/transactions/score",
                json=transaction_data,
                headers=headers
            ) as response:
                processing_time = (time.time() - start_time) * 1000

                if response.status == 200:
                    data = await response.json()
                    fraud_score = data.get("fraud_result", {}).get("fraud_score", 0)
                    decision = data.get("fraud_result", {}).get("decision", "UNKNOWN")

                    print(f"  ✅ Transaction scoring successful")
                    print(f"     Fraud Score: {fraud_score:.3f}")
                    print(f"     Decision: {decision}")
                    print(f"     Processing Time: {processing_time:.1f}ms")
                    self.test_results.append("✅ Transaction scoring")
                else:
                    print(f"  ❌ Transaction scoring failed: HTTP {response.status}")
                    error_text = await response.text()
                    print(f"     Error: {error_text}")
                    self.test_results.append("❌ Transaction scoring")
        except Exception as e:
            print(f"  ❌ Transaction scoring error: {str(e)}")
            self.test_results.append("❌ Transaction scoring")

        # Test batch transaction processing
        batch_data = {
            "transactions": [
                {**transaction_data, "transaction_id": f"batch_txn_1_{int(time.time())}"},
                {**transaction_data, "transaction_id": f"batch_txn_2_{int(time.time())}", "amount": 500.00},
                {**transaction_data, "transaction_id": f"batch_txn_3_{int(time.time())}", "amount": 10000.00}
            ]
        }

        try:
            start_time = time.time()
            async with self.session.post(
                f"{BASE_URL}/api/v1/transactions/batch",
                json=batch_data,
                headers=headers
            ) as response:
                processing_time = (time.time() - start_time) * 1000

                if response.status == 200:
                    data = await response.json()
                    total_processed = data.get("total_processed", 0)

                    print(f"  ✅ Batch processing successful")
                    print(f"     Transactions Processed: {total_processed}")
                    print(f"     Batch Processing Time: {processing_time:.1f}ms")
                    self.test_results.append("✅ Batch transaction processing")
                else:
                    print(f"  ❌ Batch processing failed: HTTP {response.status}")
                    self.test_results.append("❌ Batch transaction processing")
        except Exception as e:
            print(f"  ❌ Batch processing error: {str(e)}")
            self.test_results.append("❌ Batch transaction processing")

    async def test_analytics_endpoints(self):
        """Test analytics and reporting endpoints"""
        print("\n📊 Testing Analytics Endpoints...")

        if not self.auth_token:
            print("  ⚠️ Skipping analytics tests - no auth token")
            return

        headers = {"Authorization": f"Bearer {self.auth_token}"}

        analytics_endpoints = [
            ("Dashboard Summary", "/api/v1/analytics/dashboard"),
            ("Fraud Trends", "/api/v1/analytics/fraud-trends"),
            ("Transaction Volume", "/api/v1/analytics/transaction-volume"),
            ("Risk Distribution", "/api/v1/analytics/risk-distribution"),
            ("Performance Metrics", "/api/v1/analytics/performance-metrics")
        ]

        for endpoint_name, endpoint_path in analytics_endpoints:
            try:
                async with self.session.get(
                    f"{BASE_URL}{endpoint_path}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"  ✅ {endpoint_name}: Data retrieved")
                        self.test_results.append(f"✅ {endpoint_name}")
                    else:
                        print(f"  ❌ {endpoint_name}: HTTP {response.status}")
                        self.test_results.append(f"❌ {endpoint_name}")
            except Exception as e:
                print(f"  ❌ {endpoint_name} error: {str(e)}")
                self.test_results.append(f"❌ {endpoint_name}")

    async def test_alert_management(self):
        """Test alert management endpoints"""
        print("\n🚨 Testing Alert Management...")

        if not self.auth_token:
            print("  ⚠️ Skipping alert tests - no auth token")
            return

        headers = {"Authorization": f"Bearer {self.auth_token}"}

        # Test listing alerts
        try:
            async with self.session.get(
                f"{BASE_URL}/api/v1/alerts/",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ Alert listing: {len(data)} alerts found")
                    self.test_results.append("✅ Alert listing")
                else:
                    print(f"  ❌ Alert listing failed: HTTP {response.status}")
                    self.test_results.append("❌ Alert listing")
        except Exception as e:
            print(f"  ❌ Alert listing error: {str(e)}")
            self.test_results.append("❌ Alert listing")

        # Test alert statistics
        try:
            async with self.session.get(
                f"{BASE_URL}/api/v1/alerts/stats/summary",
                headers=headers
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ Alert statistics: {data.get('total_alerts', 0)} total alerts")
                    self.test_results.append("✅ Alert statistics")
                else:
                    print(f"  ❌ Alert statistics failed: HTTP {response.status}")
                    self.test_results.append("❌ Alert statistics")
        except Exception as e:
            print(f"  ❌ Alert statistics error: {str(e)}")
            self.test_results.append("❌ Alert statistics")

    async def test_api_documentation(self):
        """Test API documentation endpoints"""
        print("\n📚 Testing API Documentation...")

        doc_endpoints = [
            ("OpenAPI Spec", "/openapi.json"),
            ("Swagger UI", "/docs"),
            ("ReDoc", "/redoc")
        ]

        for doc_name, doc_path in doc_endpoints:
            try:
                async with self.session.get(f"{BASE_URL}{doc_path}") as response:
                    if response.status == 200:
                        print(f"  ✅ {doc_name}: Available")
                        self.test_results.append(f"✅ {doc_name}")
                    else:
                        print(f"  ❌ {doc_name}: HTTP {response.status}")
                        self.test_results.append(f"❌ {doc_name}")
            except Exception as e:
                print(f"  ❌ {doc_name} error: {str(e)}")
                self.test_results.append(f"❌ {doc_name}")

    async def test_performance(self):
        """Test system performance"""
        print("\n⚡ Testing Performance...")

        if not self.auth_token:
            print("  ⚠️ Skipping performance tests - no auth token")
            return

        headers = {"Authorization": f"Bearer {self.auth_token}"}

        # Test concurrent requests
        transaction_data = {
            "type": "PAYMENT",
            "amount": 100.00,
            "currency": "USD",
            "originator": {"account_id": "PERF_TEST_ORIG", "current_balance": 10000.00},
            "beneficiary": {"account_id": "PERF_TEST_DEST", "current_balance": 1000.00}
        }

        async def single_request(session, request_id):
            data = {**transaction_data, "transaction_id": f"perf_test_{request_id}"}
            start_time = time.time()
            try:
                async with session.post(
                    f"{BASE_URL}/api/v1/transactions/score",
                    json=data,
                    headers=headers
                ) as response:
                    processing_time = (time.time() - start_time) * 1000
                    return response.status == 200, processing_time
            except:
                return False, time.time() - start_time

        # Run concurrent requests
        num_requests = 10
        start_time = time.time()

        tasks = [single_request(self.session, i) for i in range(num_requests)]
        results = await asyncio.gather(*tasks)

        total_time = time.time() - start_time
        successful_requests = sum(1 for success, _ in results if success)
        avg_response_time = sum(time for _, time in results) / len(results)

        print(f"  📈 Performance Test Results:")
        print(f"     Concurrent Requests: {num_requests}")
        print(f"     Successful Requests: {successful_requests}")
        print(f"     Success Rate: {successful_requests/num_requests*100:.1f}%")
        print(f"     Average Response Time: {avg_response_time:.1f}ms")
        print(f"     Total Test Time: {total_time:.1f}s")
        print(f"     Throughput: {num_requests/total_time:.1f} req/s")

        if successful_requests >= num_requests * 0.8:  # 80% success rate
            self.test_results.append("✅ Performance test")
        else:
            self.test_results.append("❌ Performance test")

    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("📋 PHASE 4 TEST SUMMARY")
        print("="*60)

        passed = sum(1 for result in self.test_results if result.startswith("✅"))
        failed = sum(1 for result in self.test_results if result.startswith("❌"))
        total = len(self.test_results)

        print(f"\nTotal Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {passed/total*100:.1f}%" if total > 0 else "No tests run")

        print("\nDetailed Results:")
        for result in self.test_results:
            print(f"  {result}")

        if failed == 0:
            print(f"\n🎉 All tests passed! Phase 4 is working correctly.")
        else:
            print(f"\n⚠️ {failed} test(s) failed. Please check the logs and configuration.")

        print("\n📚 Next Steps:")
        print("  1. Visit http://localhost:8000/docs for interactive API documentation")
        print("  2. Test with your own transaction data")
        print("  3. Configure notifications in the .env file")
        print("  4. Monitor logs for any issues")

async def main():
    """Main test function"""
    print("🧪 Starting Phase 4 Comprehensive Test Suite")
    print("=" * 60)

    tester = Phase4Tester()

    try:
        await tester.setup()

        # Run all tests
        await tester.test_health_checks()
        await tester.test_api_documentation()
        await tester.test_authentication()
        await tester.test_transaction_processing()
        await tester.test_analytics_endpoints()
        await tester.test_alert_management()
        await tester.test_performance()

        # Print summary
        tester.print_summary()

    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {str(e)}")
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest suite interrupted")
        sys.exit(1)
