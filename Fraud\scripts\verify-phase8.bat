@echo off
echo === FraudShield Phase 8 Verification ===
echo.

echo Checking Phase 8 components...
echo.

set "allReady=true"

if exist "docker-compose.prod.yml" (
    echo ✓ Production Docker Compose
) else (
    echo ✗ Production Docker Compose - Missing
    set "allReady=false"
)

if exist "monitoring\prometheus\prometheus.yml" (
    echo ✓ Prometheus Config
) else (
    echo ✗ Prometheus Config - Missing
    set "allReady=false"
)

if exist "monitoring\prometheus\alert_rules.yml" (
    echo ✓ Alert Rules
) else (
    echo ✗ Alert Rules - Missing
    set "allReady=false"
)

if exist "monitoring\grafana\datasources\datasources.yml" (
    echo ✓ Grafana Datasources
) else (
    echo ✗ Grafana Datasources - Missing
    set "allReady=false"
)

if exist "monitoring\alertmanager\alertmanager.yml" (
    echo ✓ Alertmanager Config
) else (
    echo ✗ Alertmanager Config - Missing
    set "allReady=false"
)

if exist "scripts\deployment\health-check.sh" (
    echo ✓ Health Check Script
) else (
    echo ✗ Health Check Script - Missing
    set "allReady=false"
)

if exist "scripts\deployment\deploy-production.sh" (
    echo ✓ Deployment Script
) else (
    echo ✗ Deployment Script - Missing
    set "allReady=false"
)

if exist "scripts\go-live.sh" (
    echo ✓ Go-Live Script
) else (
    echo ✗ Go-Live Script - Missing
    set "allReady=false"
)

if exist "docs\PHASE8_GO_LIVE_CHECKLIST.md" (
    echo ✓ Go-Live Checklist
) else (
    echo ✗ Go-Live Checklist - Missing
    set "allReady=false"
)

if exist "docs\PHASE8_COMPLETION.md" (
    echo ✓ Phase 8 Documentation
) else (
    echo ✗ Phase 8 Documentation - Missing
    set "allReady=false"
)

echo.
if "%allReady%"=="true" (
    echo === Phase 8 Setup Complete! ===
    echo All components are ready for production deployment.
    echo.
    echo Next Steps:
    echo 1. Review: docs\PHASE8_GO_LIVE_CHECKLIST.md
    echo 2. Setup secrets: bash scripts/deployment/setup-secrets.sh
    echo 3. Deploy: bash scripts/go-live.sh production latest
    echo.
    echo Access Points after deployment:
    echo • Frontend: http://localhost:3000
    echo • API: http://localhost:8000/docs
    echo • Monitoring: http://localhost:3001
    echo • Metrics: http://localhost:9090
    echo.
    echo === FraudShield Phase 8 Ready! ===
) else (
    echo Some components are missing. Please check the files above.
    exit /b 1
)

echo.
echo Phase 8 verification complete.
pause
