"""
Multi-Factor Authentication (MFA) Configuration
Implements TOTP, SMS, and backup codes for enhanced security
"""

import os
import qrcode
import pyotp
import secrets
import hashlib
import base64
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>
from twilio.rest import Client as TwilioClient
import boto3
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class MFAConfig:
    """MFA configuration settings"""
    issuer_name: str = "FraudShield"
    totp_window: int = 1  # Allow 1 window before/after current
    backup_codes_count: int = 10
    sms_enabled: bool = True
    totp_enabled: bool = True
    backup_codes_enabled: bool = True
    rate_limit_attempts: int = 5
    rate_limit_window: int = 300  # 5 minutes

class TOTPManager:
    """Time-based One-Time Password (TOTP) management"""
    
    def __init__(self, config: MFAConfig):
        self.config = config
        self.encryption_key = os.getenv('MFA_ENCRYPTION_KEY', Fernet.generate_key())
        self.fernet = Fernet(self.encryption_key)
    
    def generate_secret(self, user_id: str) -> str:
        """Generate a new TOTP secret for user"""
        secret = pyotp.random_base32()
        
        # Encrypt and store the secret
        encrypted_secret = self.fernet.encrypt(secret.encode())
        
        # In production, store encrypted_secret in database
        # For now, return the secret for setup
        return secret
    
    def generate_qr_code(self, user_email: str, secret: str) -> bytes:
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.config.issuer_name
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Convert to bytes
        import io
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        return img_buffer.getvalue()
    
    def verify_totp(self, secret: str, token: str) -> bool:
        """Verify TOTP token"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=self.config.totp_window)
        except Exception as e:
            logger.error(f"TOTP verification failed: {e}")
            return False
    
    def get_current_token(self, secret: str) -> str:
        """Get current TOTP token (for testing)"""
        totp = pyotp.TOTP(secret)
        return totp.now()

class SMSManager:
    """SMS-based MFA management"""
    
    def __init__(self, config: MFAConfig):
        self.config = config
        self.twilio_client = None
        self.aws_sns = None
        
        # Initialize SMS provider
        if os.getenv('TWILIO_ACCOUNT_SID'):
            self._init_twilio()
        elif os.getenv('AWS_SNS_REGION'):
            self._init_aws_sns()
    
    def _init_twilio(self):
        """Initialize Twilio SMS client"""
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        self.twilio_client = TwilioClient(account_sid, auth_token)
        self.twilio_from = os.getenv('TWILIO_FROM_NUMBER')
        logger.info("Initialized Twilio SMS client")
    
    def _init_aws_sns(self):
        """Initialize AWS SNS client"""
        self.aws_sns = boto3.client(
            'sns',
            region_name=os.getenv('AWS_SNS_REGION', 'us-east-1')
        )
        logger.info("Initialized AWS SNS client")
    
    def generate_sms_code(self) -> str:
        """Generate 6-digit SMS verification code"""
        return f"{secrets.randbelow(1000000):06d}"
    
    def send_sms_code(self, phone_number: str, code: str) -> bool:
        """Send SMS verification code"""
        message = f"Your FraudShield verification code is: {code}. Valid for 5 minutes."
        
        try:
            if self.twilio_client:
                return self._send_twilio_sms(phone_number, message)
            elif self.aws_sns:
                return self._send_aws_sns_sms(phone_number, message)
            else:
                logger.error("No SMS provider configured")
                return False
        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False
    
    def _send_twilio_sms(self, phone_number: str, message: str) -> bool:
        """Send SMS via Twilio"""
        try:
            message = self.twilio_client.messages.create(
                body=message,
                from_=self.twilio_from,
                to=phone_number
            )
            logger.info(f"SMS sent via Twilio: {message.sid}")
            return True
        except Exception as e:
            logger.error(f"Twilio SMS failed: {e}")
            return False
    
    def _send_aws_sns_sms(self, phone_number: str, message: str) -> bool:
        """Send SMS via AWS SNS"""
        try:
            response = self.aws_sns.publish(
                PhoneNumber=phone_number,
                Message=message,
                MessageAttributes={
                    'AWS.SNS.SMS.SMSType': {
                        'DataType': 'String',
                        'StringValue': 'Transactional'
                    }
                }
            )
            logger.info(f"SMS sent via AWS SNS: {response['MessageId']}")
            return True
        except Exception as e:
            logger.error(f"AWS SNS SMS failed: {e}")
            return False

class BackupCodesManager:
    """Backup codes management for MFA recovery"""
    
    def __init__(self, config: MFAConfig):
        self.config = config
        self.encryption_key = os.getenv('MFA_ENCRYPTION_KEY', Fernet.generate_key())
        self.fernet = Fernet(self.encryption_key)
    
    def generate_backup_codes(self, user_id: str) -> List[str]:
        """Generate backup codes for user"""
        codes = []
        for _ in range(self.config.backup_codes_count):
            # Generate 8-character alphanumeric code
            code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(8))
            codes.append(code)
        
        # Hash and encrypt codes for storage
        hashed_codes = [self._hash_code(code) for code in codes]
        encrypted_codes = [self.fernet.encrypt(hashed_code.encode()) for hashed_code in hashed_codes]
        
        # In production, store encrypted_codes in database
        # Return plain codes for user to save
        return codes
    
    def verify_backup_code(self, user_id: str, code: str, stored_codes: List[bytes]) -> bool:
        """Verify backup code and mark as used"""
        hashed_code = self._hash_code(code)
        
        for i, encrypted_code in enumerate(stored_codes):
            try:
                decrypted_code = self.fernet.decrypt(encrypted_code).decode()
                if decrypted_code == hashed_code:
                    # Mark code as used by removing it
                    stored_codes.pop(i)
                    logger.info(f"Backup code used for user {user_id}")
                    return True
            except Exception as e:
                logger.error(f"Failed to decrypt backup code: {e}")
                continue
        
        return False
    
    def _hash_code(self, code: str) -> str:
        """Hash backup code for secure storage"""
        return hashlib.sha256(code.encode()).hexdigest()

class MFAManager:
    """Main MFA management class"""
    
    def __init__(self, config: Optional[MFAConfig] = None):
        self.config = config or MFAConfig()
        self.totp_manager = TOTPManager(self.config)
        self.sms_manager = SMSManager(self.config)
        self.backup_codes_manager = BackupCodesManager(self.config)
        self.rate_limiter = MFARateLimiter(self.config)
    
    def setup_mfa_for_user(self, user_id: str, user_email: str, phone_number: Optional[str] = None) -> Dict[str, Any]:
        """Set up MFA for a user"""
        setup_data = {}
        
        # Generate TOTP secret and QR code
        if self.config.totp_enabled:
            totp_secret = self.totp_manager.generate_secret(user_id)
            qr_code = self.totp_manager.generate_qr_code(user_email, totp_secret)
            
            setup_data['totp'] = {
                'secret': totp_secret,
                'qr_code': base64.b64encode(qr_code).decode(),
                'manual_entry_key': totp_secret
            }
        
        # Generate backup codes
        if self.config.backup_codes_enabled:
            backup_codes = self.backup_codes_manager.generate_backup_codes(user_id)
            setup_data['backup_codes'] = backup_codes
        
        # SMS setup
        if self.config.sms_enabled and phone_number:
            setup_data['sms'] = {
                'phone_number': phone_number,
                'enabled': True
            }
        
        return setup_data
    
    def verify_mfa_token(self, user_id: str, token: str, method: str, **kwargs) -> bool:
        """Verify MFA token"""
        # Check rate limiting
        if not self.rate_limiter.check_rate_limit(user_id):
            logger.warning(f"Rate limit exceeded for user {user_id}")
            return False
        
        try:
            if method == 'totp':
                secret = kwargs.get('secret')
                if not secret:
                    return False
                return self.totp_manager.verify_totp(secret, token)
            
            elif method == 'sms':
                stored_code = kwargs.get('stored_code')
                if not stored_code:
                    return False
                return token == stored_code
            
            elif method == 'backup_code':
                stored_codes = kwargs.get('stored_codes', [])
                return self.backup_codes_manager.verify_backup_code(user_id, token, stored_codes)
            
            else:
                logger.error(f"Unknown MFA method: {method}")
                return False
        
        except Exception as e:
            logger.error(f"MFA verification failed for user {user_id}: {e}")
            return False
        
        finally:
            # Record attempt
            self.rate_limiter.record_attempt(user_id)
    
    def send_sms_code(self, user_id: str, phone_number: str) -> Optional[str]:
        """Send SMS verification code"""
        if not self.config.sms_enabled:
            return None
        
        # Check rate limiting
        if not self.rate_limiter.check_rate_limit(user_id, action='sms'):
            logger.warning(f"SMS rate limit exceeded for user {user_id}")
            return None
        
        code = self.sms_manager.generate_sms_code()
        
        if self.sms_manager.send_sms_code(phone_number, code):
            # Record SMS attempt
            self.rate_limiter.record_attempt(user_id, action='sms')
            return code
        
        return None

class MFARateLimiter:
    """Rate limiting for MFA attempts"""
    
    def __init__(self, config: MFAConfig):
        self.config = config
        self.attempts = {}  # In production, use Redis or database
    
    def check_rate_limit(self, user_id: str, action: str = 'verify') -> bool:
        """Check if user has exceeded rate limit"""
        key = f"{user_id}:{action}"
        now = datetime.utcnow()
        
        if key not in self.attempts:
            return True
        
        # Clean old attempts
        self.attempts[key] = [
            attempt_time for attempt_time in self.attempts[key]
            if (now - attempt_time).total_seconds() < self.config.rate_limit_window
        ]
        
        return len(self.attempts[key]) < self.config.rate_limit_attempts
    
    def record_attempt(self, user_id: str, action: str = 'verify'):
        """Record an MFA attempt"""
        key = f"{user_id}:{action}"
        now = datetime.utcnow()
        
        if key not in self.attempts:
            self.attempts[key] = []
        
        self.attempts[key].append(now)

# Factory function
def create_mfa_manager(environment: str = 'development') -> MFAManager:
    """Create MFA manager based on environment"""
    
    if environment == 'production':
        config = MFAConfig(
            issuer_name=os.getenv('MFA_ISSUER_NAME', 'FraudShield'),
            totp_window=int(os.getenv('MFA_TOTP_WINDOW', '1')),
            backup_codes_count=int(os.getenv('MFA_BACKUP_CODES_COUNT', '10')),
            sms_enabled=os.getenv('MFA_SMS_ENABLED', 'true').lower() == 'true',
            totp_enabled=os.getenv('MFA_TOTP_ENABLED', 'true').lower() == 'true',
            backup_codes_enabled=os.getenv('MFA_BACKUP_CODES_ENABLED', 'true').lower() == 'true',
            rate_limit_attempts=int(os.getenv('MFA_RATE_LIMIT_ATTEMPTS', '5')),
            rate_limit_window=int(os.getenv('MFA_RATE_LIMIT_WINDOW', '300'))
        )
    else:
        config = MFAConfig()
    
    return MFAManager(config)
