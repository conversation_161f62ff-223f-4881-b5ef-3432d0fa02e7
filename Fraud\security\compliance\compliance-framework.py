"""
Compliance Framework for Financial Data Protection
Implements PCI DSS, GDPR, SOX, and other regulatory requirements
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from pathlib import Path
import asyncio
from cryptography.fernet import Fernet
import uuid

logger = logging.getLogger(__name__)

class ComplianceStandard(Enum):
    """Supported compliance standards"""
    PCI_DSS = "pci_dss"
    GDPR = "gdpr"
    SOX = "sox"
    ISO_27001 = "iso_27001"
    NIST = "nist"
    CCPA = "ccpa"
    HIPAA = "hipaa"

class DataClassification(Enum):
    """Data classification levels"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"

class ProcessingLawfulBasis(Enum):
    """GDPR lawful basis for processing"""
    CONSENT = "consent"
    CONTRACT = "contract"
    LEGAL_OBLIGATION = "legal_obligation"
    VITAL_INTERESTS = "vital_interests"
    PUBLIC_TASK = "public_task"
    LEGITIMATE_INTERESTS = "legitimate_interests"

@dataclass
class DataSubject:
    """GDPR data subject information"""
    subject_id: str
    email: str
    consent_given: bool
    consent_date: Optional[datetime]
    lawful_basis: ProcessingLawfulBasis
    data_categories: List[str]
    retention_period: int  # days
    created_at: datetime
    updated_at: datetime

@dataclass
class DataProcessingRecord:
    """Record of data processing activities"""
    record_id: str
    data_subject_id: str
    processing_purpose: str
    data_categories: List[str]
    lawful_basis: ProcessingLawfulBasis
    processor: str
    recipients: List[str]
    retention_period: int
    security_measures: List[str]
    timestamp: datetime

@dataclass
class AuditEvent:
    """Audit event for compliance tracking"""
    event_id: str
    event_type: str
    user_id: Optional[str]
    resource: str
    action: str
    timestamp: datetime
    ip_address: str
    user_agent: str
    result: str
    details: Dict[str, Any]
    compliance_standards: List[ComplianceStandard]

class PCIDSSCompliance:
    """PCI DSS compliance implementation"""
    
    def __init__(self):
        self.requirements = {
            '1': 'Install and maintain a firewall configuration',
            '2': 'Do not use vendor-supplied defaults for system passwords',
            '3': 'Protect stored cardholder data',
            '4': 'Encrypt transmission of cardholder data',
            '5': 'Protect all systems against malware',
            '6': 'Develop and maintain secure systems and applications',
            '7': 'Restrict access to cardholder data by business need-to-know',
            '8': 'Identify and authenticate access to system components',
            '9': 'Restrict physical access to cardholder data',
            '10': 'Track and monitor all access to network resources',
            '11': 'Regularly test security systems and processes',
            '12': 'Maintain a policy that addresses information security'
        }
    
    def validate_card_data_storage(self, data: Dict[str, Any]) -> Dict[str, bool]:
        """Validate PCI DSS compliance for card data storage"""
        checks = {
            'no_full_pan_storage': True,
            'no_cvv_storage': True,
            'encrypted_storage': True,
            'access_logging': True,
            'secure_deletion': True
        }
        
        # Check for prohibited data
        prohibited_fields = ['cvv', 'cvc', 'cvv2', 'cid', 'full_pan']
        for field in prohibited_fields:
            if field in data:
                if field in ['cvv', 'cvc', 'cvv2', 'cid']:
                    checks['no_cvv_storage'] = False
                elif field == 'full_pan':
                    checks['no_full_pan_storage'] = False
        
        return checks
    
    def mask_pan(self, pan: str) -> str:
        """Mask PAN according to PCI DSS requirements"""
        if len(pan) < 6:
            return '*' * len(pan)
        
        # Show first 6 and last 4 digits
        return pan[:6] + '*' * (len(pan) - 10) + pan[-4:]
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate PCI DSS compliance report"""
        return {
            'standard': 'PCI DSS v3.2.1',
            'assessment_date': datetime.utcnow().isoformat(),
            'requirements': self.requirements,
            'compliance_status': 'compliant',  # This would be calculated
            'findings': [],
            'recommendations': []
        }

class GDPRCompliance:
    """GDPR compliance implementation"""
    
    def __init__(self, encryption_key: bytes):
        self.fernet = Fernet(encryption_key)
        self.data_subjects = {}
        self.processing_records = {}
        
        # Data retention policies (in days)
        self.retention_policies = {
            'user_data': 2555,  # 7 years
            'transaction_data': 2555,  # 7 years
            'audit_logs': 2190,  # 6 years
            'session_data': 30,  # 30 days
            'marketing_data': 1095,  # 3 years
        }
    
    def register_data_subject(self, subject_data: Dict[str, Any]) -> DataSubject:
        """Register a new data subject"""
        subject = DataSubject(
            subject_id=str(uuid.uuid4()),
            email=subject_data['email'],
            consent_given=subject_data.get('consent_given', False),
            consent_date=datetime.utcnow() if subject_data.get('consent_given') else None,
            lawful_basis=ProcessingLawfulBasis(subject_data.get('lawful_basis', 'consent')),
            data_categories=subject_data.get('data_categories', []),
            retention_period=subject_data.get('retention_period', self.retention_policies['user_data']),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.data_subjects[subject.subject_id] = subject
        return subject
    
    def record_processing_activity(self, activity_data: Dict[str, Any]) -> DataProcessingRecord:
        """Record data processing activity"""
        record = DataProcessingRecord(
            record_id=str(uuid.uuid4()),
            data_subject_id=activity_data['data_subject_id'],
            processing_purpose=activity_data['purpose'],
            data_categories=activity_data['data_categories'],
            lawful_basis=ProcessingLawfulBasis(activity_data['lawful_basis']),
            processor=activity_data['processor'],
            recipients=activity_data.get('recipients', []),
            retention_period=activity_data.get('retention_period', self.retention_policies['user_data']),
            security_measures=activity_data.get('security_measures', []),
            timestamp=datetime.utcnow()
        )
        
        self.processing_records[record.record_id] = record
        return record
    
    def handle_data_subject_request(self, request_type: str, subject_id: str) -> Dict[str, Any]:
        """Handle data subject rights requests"""
        if subject_id not in self.data_subjects:
            return {'status': 'error', 'message': 'Data subject not found'}
        
        subject = self.data_subjects[subject_id]
        
        if request_type == 'access':
            return self._handle_access_request(subject)
        elif request_type == 'rectification':
            return self._handle_rectification_request(subject)
        elif request_type == 'erasure':
            return self._handle_erasure_request(subject)
        elif request_type == 'portability':
            return self._handle_portability_request(subject)
        elif request_type == 'restriction':
            return self._handle_restriction_request(subject)
        else:
            return {'status': 'error', 'message': 'Unknown request type'}
    
    def _handle_access_request(self, subject: DataSubject) -> Dict[str, Any]:
        """Handle data access request (Article 15)"""
        # Collect all data for the subject
        subject_data = {
            'personal_data': asdict(subject),
            'processing_records': [
                asdict(record) for record in self.processing_records.values()
                if record.data_subject_id == subject.subject_id
            ],
            'retention_periods': self.retention_policies,
            'rights': [
                'access', 'rectification', 'erasure', 'restriction',
                'portability', 'objection'
            ]
        }
        
        return {
            'status': 'success',
            'data': subject_data,
            'format': 'json',
            'provided_at': datetime.utcnow().isoformat()
        }
    
    def _handle_erasure_request(self, subject: DataSubject) -> Dict[str, Any]:
        """Handle right to erasure request (Article 17)"""
        # Check if erasure is possible
        legal_obligations = self._check_legal_obligations(subject.subject_id)
        
        if legal_obligations:
            return {
                'status': 'denied',
                'reason': 'Legal obligations prevent erasure',
                'obligations': legal_obligations
            }
        
        # Perform erasure
        self._erase_subject_data(subject.subject_id)
        
        return {
            'status': 'success',
            'message': 'Data has been erased',
            'erased_at': datetime.utcnow().isoformat()
        }
    
    def _check_legal_obligations(self, subject_id: str) -> List[str]:
        """Check for legal obligations preventing erasure"""
        obligations = []
        
        # Check for financial regulations
        # This would integrate with actual business logic
        if self._has_financial_transactions(subject_id):
            obligations.append('Financial record retention requirements')
        
        if self._has_legal_proceedings(subject_id):
            obligations.append('Ongoing legal proceedings')
        
        return obligations
    
    def _has_financial_transactions(self, subject_id: str) -> bool:
        """Check if subject has financial transactions requiring retention"""
        # This would check actual transaction records
        return False
    
    def _has_legal_proceedings(self, subject_id: str) -> bool:
        """Check if subject is involved in legal proceedings"""
        # This would check legal case management system
        return False
    
    def _erase_subject_data(self, subject_id: str):
        """Erase all data for a subject"""
        # This would implement actual data erasure across all systems
        if subject_id in self.data_subjects:
            del self.data_subjects[subject_id]
        
        # Remove processing records
        to_remove = [
            record_id for record_id, record in self.processing_records.items()
            if record.data_subject_id == subject_id
        ]
        for record_id in to_remove:
            del self.processing_records[record_id]

class AuditLogger:
    """Comprehensive audit logging for compliance"""
    
    def __init__(self, storage_path: str):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Compliance requirements for different standards
        self.audit_requirements = {
            ComplianceStandard.PCI_DSS: {
                'retention_period': 365,  # 1 year minimum
                'required_events': [
                    'authentication', 'authorization', 'data_access',
                    'system_changes', 'privilege_changes'
                ]
            },
            ComplianceStandard.SOX: {
                'retention_period': 2555,  # 7 years
                'required_events': [
                    'financial_data_access', 'report_generation',
                    'system_changes', 'user_management'
                ]
            },
            ComplianceStandard.GDPR: {
                'retention_period': 2190,  # 6 years
                'required_events': [
                    'data_processing', 'consent_changes',
                    'data_subject_requests', 'data_breaches'
                ]
            }
        }
    
    def log_event(self, event: AuditEvent):
        """Log audit event"""
        # Store event
        event_file = self.storage_path / f"audit_{event.timestamp.strftime('%Y%m%d')}.jsonl"
        
        with open(event_file, 'a') as f:
            f.write(json.dumps(asdict(event), default=str) + '\n')
        
        # Check compliance requirements
        self._check_compliance_requirements(event)
        
        logger.info(f"Audit event logged: {event.event_id}")
    
    def _check_compliance_requirements(self, event: AuditEvent):
        """Check if event meets compliance requirements"""
        for standard in event.compliance_standards:
            requirements = self.audit_requirements.get(standard)
            if requirements and event.event_type in requirements['required_events']:
                # Event meets compliance requirements
                logger.debug(f"Event {event.event_id} meets {standard.value} requirements")
    
    def generate_audit_report(self, start_date: datetime, end_date: datetime,
                            standard: ComplianceStandard) -> Dict[str, Any]:
        """Generate audit report for compliance"""
        events = self._load_events(start_date, end_date)
        
        # Filter events by compliance standard
        relevant_events = [
            event for event in events
            if standard in event.get('compliance_standards', [])
        ]
        
        return {
            'standard': standard.value,
            'period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'total_events': len(relevant_events),
            'event_types': self._summarize_event_types(relevant_events),
            'compliance_status': self._assess_compliance(relevant_events, standard),
            'generated_at': datetime.utcnow().isoformat()
        }
    
    def _load_events(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Load audit events for date range"""
        events = []
        current_date = start_date.date()
        
        while current_date <= end_date.date():
            event_file = self.storage_path / f"audit_{current_date.strftime('%Y%m%d')}.jsonl"
            
            if event_file.exists():
                with open(event_file, 'r') as f:
                    for line in f:
                        try:
                            event = json.loads(line.strip())
                            event_time = datetime.fromisoformat(event['timestamp'])
                            if start_date <= event_time <= end_date:
                                events.append(event)
                        except (json.JSONDecodeError, KeyError, ValueError):
                            continue
            
            current_date += timedelta(days=1)
        
        return events
    
    def _summarize_event_types(self, events: List[Dict[str, Any]]) -> Dict[str, int]:
        """Summarize event types"""
        summary = {}
        for event in events:
            event_type = event.get('event_type', 'unknown')
            summary[event_type] = summary.get(event_type, 0) + 1
        return summary
    
    def _assess_compliance(self, events: List[Dict[str, Any]], 
                          standard: ComplianceStandard) -> Dict[str, Any]:
        """Assess compliance based on events"""
        requirements = self.audit_requirements.get(standard, {})
        required_events = requirements.get('required_events', [])
        
        found_events = set(event.get('event_type') for event in events)
        missing_events = set(required_events) - found_events
        
        return {
            'compliant': len(missing_events) == 0,
            'required_events': required_events,
            'found_events': list(found_events),
            'missing_events': list(missing_events),
            'coverage_percentage': (len(found_events) / len(required_events)) * 100 if required_events else 100
        }

class ComplianceManager:
    """Main compliance management system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.encryption_key = config.get('encryption_key', Fernet.generate_key())
        
        # Initialize compliance modules
        self.pci_dss = PCIDSSCompliance()
        self.gdpr = GDPRCompliance(self.encryption_key)
        self.audit_logger = AuditLogger(config.get('audit_log_path', './audit_logs'))
        
        # Active compliance standards
        self.active_standards = [
            ComplianceStandard(std) for std in config.get('active_standards', ['pci_dss', 'gdpr'])
        ]
    
    def validate_compliance(self, data: Dict[str, Any], operation: str) -> Dict[str, Any]:
        """Validate operation against all active compliance standards"""
        results = {}
        
        for standard in self.active_standards:
            if standard == ComplianceStandard.PCI_DSS:
                results['pci_dss'] = self.pci_dss.validate_card_data_storage(data)
            elif standard == ComplianceStandard.GDPR:
                results['gdpr'] = self._validate_gdpr_compliance(data, operation)
        
        return results
    
    def _validate_gdpr_compliance(self, data: Dict[str, Any], operation: str) -> Dict[str, bool]:
        """Validate GDPR compliance"""
        checks = {
            'lawful_basis_present': 'lawful_basis' in data,
            'consent_recorded': data.get('consent_given', False),
            'purpose_specified': 'processing_purpose' in data,
            'data_minimization': True,  # Would implement actual check
            'retention_period_defined': 'retention_period' in data
        }
        
        return checks
    
    def generate_compliance_report(self, standards: List[ComplianceStandard] = None) -> Dict[str, Any]:
        """Generate comprehensive compliance report"""
        if standards is None:
            standards = self.active_standards
        
        report = {
            'generated_at': datetime.utcnow().isoformat(),
            'standards': {}
        }
        
        for standard in standards:
            if standard == ComplianceStandard.PCI_DSS:
                report['standards']['pci_dss'] = self.pci_dss.generate_compliance_report()
            # Add other standards as needed
        
        return report

# Factory function
def create_compliance_manager(config: Dict[str, Any]) -> ComplianceManager:
    """Create compliance manager with configuration"""
    return ComplianceManager(config)
