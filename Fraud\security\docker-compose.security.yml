# Docker Compose for Security Infrastructure
# Includes Vault, monitoring, and security tools

version: '3.8'

services:
  # HashiCorp Vault for secrets management
  vault:
    image: vault:1.15.2
    container_name: fraudshield-vault
    restart: unless-stopped
    ports:
      - "8200:8200"
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: myroot
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
      VAULT_ADDR: http://0.0.0.0:8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault-data:/vault/data
      - vault-logs:/vault/logs
      - ./security/secrets/vault-config.yaml:/vault/config/vault.hcl
      - ./security/tls:/vault/tls
    command: vault server -config=/vault/config/vault.hcl
    networks:
      - fraudshield-security
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Consul for Vault backend (production setup)
  consul:
    image: consul:1.16.1
    container_name: fraudshield-consul
    restart: unless-stopped
    ports:
      - "8500:8500"
    environment:
      CONSUL_BIND_INTERFACE: eth0
    volumes:
      - consul-data:/consul/data
    command: >
      consul agent -server -bootstrap-expect=1 -ui -bind=0.0.0.0 
      -client=0.0.0.0 -data-dir=/consul/data
    networks:
      - fraudshield-security

  # Redis for security monitoring and rate limiting
  redis-security:
    image: redis:7.2-alpine
    container_name: fraudshield-redis-security
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis-security-data:/data
      - ./security/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - fraudshield-security
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for security logs
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: fraudshield-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=fraudshield-elastic-password
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
      - ./security/elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    networks:
      - fraudshield-security
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana for security dashboard
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: fraudshield-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=fraudshield-elastic-password
    volumes:
      - ./security/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml
    depends_on:
      - elasticsearch
    networks:
      - fraudshield-security
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for security metrics
  prometheus:
    image: prom/prometheus:v2.47.2
    container_name: fraudshield-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - prometheus-data:/prometheus
      - ./security/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./security/monitoring/alert-rules.yml:/etc/prometheus/alert-rules.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - fraudshield-security

  # Grafana for security dashboards
  grafana:
    image: grafana/grafana:10.2.0
    container_name: fraudshield-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=fraudshield-grafana-password
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_DISABLE_GRAVATAR=true
      - GF_SECURITY_COOKIE_SECURE=true
      - GF_SECURITY_COOKIE_SAMESITE=strict
    volumes:
      - grafana-data:/var/lib/grafana
      - ./security/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./security/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - fraudshield-security

  # AlertManager for security alerts
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: fraudshield-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - alertmanager-data:/alertmanager
      - ./security/monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    networks:
      - fraudshield-security

  # Falco for runtime security monitoring
  falco:
    image: falcosecurity/falco:0.36.2
    container_name: fraudshield-falco
    restart: unless-stopped
    privileged: true
    volumes:
      - /var/run/docker.sock:/host/var/run/docker.sock
      - /dev:/host/dev
      - /proc:/host/proc:ro
      - /boot:/host/boot:ro
      - /lib/modules:/host/lib/modules:ro
      - /usr:/host/usr:ro
      - /etc:/host/etc:ro
      - ./security/falco/falco.yaml:/etc/falco/falco.yaml
      - ./security/falco/rules:/etc/falco/rules.d
    networks:
      - fraudshield-security

  # OWASP ZAP for security testing
  zap:
    image: owasp/zap2docker-stable:2.14.0
    container_name: fraudshield-zap
    restart: "no"
    ports:
      - "8080:8080"
    volumes:
      - zap-data:/zap/wrk
    command: zap-webswing.sh
    networks:
      - fraudshield-security

  # Trivy for vulnerability scanning
  trivy:
    image: aquasec/trivy:0.47.0
    container_name: fraudshield-trivy
    restart: "no"
    volumes:
      - trivy-cache:/root/.cache/trivy
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - fraudshield-security

  # ClamAV for malware scanning
  clamav:
    image: clamav/clamav:1.2.1
    container_name: fraudshield-clamav
    restart: unless-stopped
    ports:
      - "3310:3310"
    volumes:
      - clamav-data:/var/lib/clamav
    environment:
      - CLAMAV_NO_FRESHCLAMD=false
      - CLAMAV_NO_CLAMD=false
    networks:
      - fraudshield-security

  # Nginx with security headers
  nginx-security:
    image: nginx:1.25.3-alpine
    container_name: fraudshield-nginx-security
    restart: unless-stopped
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./security/tls/nginx-ssl.conf:/etc/nginx/nginx.conf
      - ./security/tls/certs:/etc/ssl/certs
      - ./security/tls/private:/etc/ssl/private
      - nginx-logs:/var/log/nginx
    depends_on:
      - vault
    networks:
      - fraudshield-security
      - fraudshield-network

  # Filebeat for log shipping
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: fraudshield-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./security/monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - nginx-logs:/var/log/nginx:ro
      - vault-logs:/var/log/vault:ro
    depends_on:
      - elasticsearch
    networks:
      - fraudshield-security

volumes:
  vault-data:
    driver: local
  vault-logs:
    driver: local
  consul-data:
    driver: local
  redis-security-data:
    driver: local
  elasticsearch-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  alertmanager-data:
    driver: local
  zap-data:
    driver: local
  trivy-cache:
    driver: local
  clamav-data:
    driver: local
  nginx-logs:
    driver: local

networks:
  fraudshield-security:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  fraudshield-network:
    external: true
