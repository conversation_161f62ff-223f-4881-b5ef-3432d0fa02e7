# Security Hardening Configuration for Infrastructure
# Comprehensive security controls for containers, networks, and systems

# Container Security Configuration
apiVersion: v1
kind: SecurityContext
metadata:
  name: fraudshield-security-context
spec:
  # Run as non-root user
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000
  
  # Filesystem security
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  
  # Capabilities
  capabilities:
    drop:
      - ALL
    add:
      - NET_BIND_SERVICE  # Only if needed for port binding
  
  # SELinux/AppArmor
  seLinuxOptions:
    level: "s0:c123,c456"
  
  # Seccomp profile
  seccompProfile:
    type: RuntimeDefault

---
# Network Security Policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: fraudshield-network-policy
  namespace: fraudshield
spec:
  podSelector:
    matchLabels:
      app: fraudshield
  
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow traffic from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow traffic from frontend to backend
  - from:
    - podSelector:
        matchLabels:
          app: fraudshield-frontend
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090  # Metrics port
  
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
  
  # Allow HTTPS outbound (for external APIs)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow database connections
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow Redis connections
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379

---
# Pod Security Standards
apiVersion: v1
kind: Namespace
metadata:
  name: fraudshield
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
# Resource Quotas and Limits
apiVersion: v1
kind: ResourceQuota
metadata:
  name: fraudshield-quota
  namespace: fraudshield
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"

---
# Limit Ranges
apiVersion: v1
kind: LimitRange
metadata:
  name: fraudshield-limits
  namespace: fraudshield
spec:
  limits:
  - default:
      cpu: 500m
      memory: 512Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    type: Container
  
  - max:
      cpu: "2"
      memory: 4Gi
    min:
      cpu: 50m
      memory: 64Mi
    type: Container

---
# Service Account with minimal permissions
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fraudshield-service-account
  namespace: fraudshield
automountServiceAccountToken: false

---
# RBAC Configuration
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: fraudshield
  name: fraudshield-role
rules:
# Allow reading ConfigMaps and Secrets
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]

# Allow reading own pod information
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
  resourceNames: ["fraudshield-*"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: fraudshield-rolebinding
  namespace: fraudshield
subjects:
- kind: ServiceAccount
  name: fraudshield-service-account
  namespace: fraudshield
roleRef:
  kind: Role
  name: fraudshield-role
  apiGroup: rbac.authorization.k8s.io

---
# Security Scanning with Falco
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-rules
  namespace: fraudshield
data:
  fraudshield_rules.yaml: |
    - rule: Detect Privilege Escalation in FraudShield
      desc: Detect attempts to escalate privileges in FraudShield containers
      condition: >
        spawned_process and
        container.image.repository contains "fraudshield" and
        (proc.name in (su, sudo, setuid, setgid) or
         proc.args contains "chmod +s" or
         proc.args contains "chown root")
      output: >
        Privilege escalation attempt in FraudShield container
        (user=%user.name command=%proc.cmdline container=%container.name)
      priority: CRITICAL
      tags: [fraudshield, privilege_escalation]
    
    - rule: Detect Suspicious Network Activity
      desc: Detect suspicious network connections from FraudShield
      condition: >
        outbound and
        container.image.repository contains "fraudshield" and
        not fd.sport in (80, 443, 5432, 6379, 53) and
        not fd.sip in (10.0.0.0/8, **********/12, ***********/16)
      output: >
        Suspicious outbound connection from FraudShield
        (connection=%fd.name container=%container.name)
      priority: WARNING
      tags: [fraudshield, network]
    
    - rule: Detect File System Modifications
      desc: Detect unauthorized file system modifications
      condition: >
        open_write and
        container.image.repository contains "fraudshield" and
        fd.name startswith /etc and
        not proc.name in (dpkg, apt, yum, rpm)
      output: >
        Unauthorized file modification in FraudShield
        (file=%fd.name process=%proc.name container=%container.name)
      priority: HIGH
      tags: [fraudshield, filesystem]

---
# Image Security Policy
apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: fraudshield-image-policy
spec:
  validationFailureAction: enforce
  background: true
  rules:
  - name: check-image-signature
    match:
      any:
      - resources:
          kinds:
          - Pod
          namespaces:
          - fraudshield
    verifyImages:
    - imageReferences:
      - "fraudshield/*"
      attestors:
      - entries:
        - keys:
            publicKeys: |-
              -----BEGIN PUBLIC KEY-----
              # Your image signing public key here
              -----END PUBLIC KEY-----
  
  - name: disallow-latest-tag
    match:
      any:
      - resources:
          kinds:
          - Pod
          namespaces:
          - fraudshield
    validate:
      message: "Using latest tag is not allowed"
      pattern:
        spec:
          containers:
          - name: "*"
            image: "!*:latest"
  
  - name: require-security-context
    match:
      any:
      - resources:
          kinds:
          - Pod
          namespaces:
          - fraudshield
    validate:
      message: "Security context is required"
      pattern:
        spec:
          securityContext:
            runAsNonRoot: true
            runAsUser: ">0"
          containers:
          - name: "*"
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities:
                drop:
                - ALL

---
# Admission Controller Configuration
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: fraudshield-security-webhook
webhooks:
- name: security.fraudshield.example.com
  clientConfig:
    service:
      name: fraudshield-admission-webhook
      namespace: fraudshield
      path: "/validate"
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail

---
# Secrets Management
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vault-backend
  namespace: fraudshield
spec:
  provider:
    vault:
      server: "https://vault.fraudshield.example.com"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "fraudshield"
          serviceAccountRef:
            name: "fraudshield-service-account"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: fraudshield-secrets
  namespace: fraudshield
spec:
  refreshInterval: 15s
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: fraudshield-app-secrets
    creationPolicy: Owner
  data:
  - secretKey: database-password
    remoteRef:
      key: fraudshield/database
      property: password
  - secretKey: jwt-secret
    remoteRef:
      key: fraudshield/auth
      property: jwt_secret
  - secretKey: encryption-key
    remoteRef:
      key: fraudshield/encryption
      property: key

---
# Monitoring and Alerting
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: fraudshield-security-metrics
  namespace: fraudshield
spec:
  selector:
    matchLabels:
      app: fraudshield
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: fraudshield-security-alerts
  namespace: fraudshield
spec:
  groups:
  - name: fraudshield.security
    rules:
    - alert: HighFailedLoginRate
      expr: rate(fraudshield_login_failures_total[5m]) > 0.1
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: "High failed login rate detected"
        description: "Failed login rate is {{ $value }} per second"
    
    - alert: SuspiciousAPIActivity
      expr: rate(fraudshield_api_requests_total{status_code=~"4.."}[5m]) > 0.5
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "Suspicious API activity detected"
        description: "High rate of 4xx responses: {{ $value }} per second"
    
    - alert: SecurityEventCritical
      expr: increase(fraudshield_security_events_total{severity="critical"}[5m]) > 0
      for: 0m
      labels:
        severity: critical
      annotations:
        summary: "Critical security event detected"
        description: "{{ $value }} critical security events in the last 5 minutes"
