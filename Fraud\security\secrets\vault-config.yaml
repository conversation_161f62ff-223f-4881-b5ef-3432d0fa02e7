# HashiCorp Vault Configuration for Secrets Management
# Provides secure storage and access to sensitive configuration data

# Vault Server Configuration
storage "consul" {
  address = "127.0.0.1:8500"
  path    = "vault/"
}

# Alternative: File storage for development
# storage "file" {
#   path = "/vault/data"
# }

# Alternative: Cloud storage for production
# storage "s3" {
#   access_key = "AKIAIOSFODNN7EXAMPLE"
#   secret_key = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
#   bucket     = "my-vault-bucket"
#   region     = "us-west-2"
# }

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = false
  tls_cert_file = "/vault/tls/vault.crt"
  tls_key_file  = "/vault/tls/vault.key"
  tls_min_version = "tls12"
  tls_cipher_suites = "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"
}

# Seal configuration (auto-unseal with cloud KMS)
seal "awskms" {
  region     = "us-west-2"
  kms_key_id = "alias/vault-unseal-key"
}

# Alternative: Azure Key Vault auto-unseal
# seal "azurekeyvault" {
#   tenant_id      = "46646709-b63e-4747-be42-516edeaf1e14"
#   client_id      = "03dc33fc-16d9-4b77-8152-3ec568f8af6e"
#   client_secret  = "DUJDS3..."
#   vault_name     = "hc-vault"
#   key_name       = "vault_key"
# }

# Alternative: Google Cloud KMS auto-unseal
# seal "gcpckms" {
#   project     = "vault-project"
#   region      = "global"
#   key_ring    = "vault-keyring"
#   crypto_key  = "vault-key"
# }

# API address
api_addr = "https://vault.fraudshield.example.com:8200"

# Cluster address
cluster_addr = "https://vault.fraudshield.example.com:8201"

# UI configuration
ui = true

# Logging
log_level = "INFO"
log_format = "json"

# Telemetry
telemetry {
  prometheus_retention_time = "30s"
  disable_hostname = true
}

# High availability
ha_storage "consul" {
  address = "127.0.0.1:8500"
  path    = "vault/"
}

# Performance settings
default_lease_ttl = "168h"
max_lease_ttl = "720h"

# Plugin directory
plugin_directory = "/vault/plugins"

---
# Vault Policies for FraudShield Application

# Database secrets policy
path "database/creds/fraudshield-app" {
  capabilities = ["read"]
}

path "database/config/fraudshield-db" {
  capabilities = ["read"]
}

# KV secrets policy for application configuration
path "secret/data/fraudshield/*" {
  capabilities = ["read", "list"]
}

path "secret/metadata/fraudshield/*" {
  capabilities = ["read", "list"]
}

# PKI policy for certificate management
path "pki/issue/fraudshield-dot-com" {
  capabilities = ["create", "update"]
}

path "pki/cert/ca" {
  capabilities = ["read"]
}

# Transit encryption policy
path "transit/encrypt/fraudshield-key" {
  capabilities = ["create", "update"]
}

path "transit/decrypt/fraudshield-key" {
  capabilities = ["create", "update"]
}

path "transit/datakey/plaintext/fraudshield-key" {
  capabilities = ["create", "update"]
}

# AWS secrets policy
path "aws/creds/fraudshield-role" {
  capabilities = ["read"]
}

# Identity and access management
path "identity/oidc/token/fraudshield" {
  capabilities = ["read"]
}

---
# Vault Authentication Methods Configuration

# Kubernetes authentication
auth "kubernetes" {
  type = "kubernetes"
  config = {
    kubernetes_host = "https://kubernetes.default.svc.cluster.local"
    kubernetes_ca_cert = file("/var/run/secrets/kubernetes.io/serviceaccount/ca.crt")
    token_reviewer_jwt = file("/var/run/secrets/kubernetes.io/serviceaccount/token")
  }
}

# AWS IAM authentication
auth "aws" {
  type = "aws"
  config = {
    access_key = "AKIAIOSFODNN7EXAMPLE"
    secret_key = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    region = "us-west-2"
    iam_server_id_header_value = "vault.fraudshield.example.com"
  }
}

# LDAP authentication
auth "ldap" {
  type = "ldap"
  config = {
    url = "ldaps://ldap.fraudshield.example.com"
    userdn = "ou=Users,dc=fraudshield,dc=example,dc=com"
    userattr = "uid"
    groupdn = "ou=Groups,dc=fraudshield,dc=example,dc=com"
    groupfilter = "(&(objectClass=group)(member={{.UserDN}}))"
    groupattr = "cn"
    certificate = file("/vault/tls/ldap-ca.crt")
    insecure_tls = false
    starttls = true
  }
}

# JWT/OIDC authentication
auth "oidc" {
  type = "oidc"
  config = {
    oidc_discovery_url = "https://auth.fraudshield.example.com"
    oidc_client_id = "vault-fraudshield"
    oidc_client_secret = "vault-client-secret"
    default_role = "fraudshield-user"
  }
}

---
# Vault Secrets Engines Configuration

# Database secrets engine
secrets "database" {
  type = "database"
  config = {
    plugin_name = "postgres-database-plugin"
    connection_url = "postgresql://{{username}}:{{password}}@postgres.fraudshield.example.com:5432/fraudshield?sslmode=require"
    allowed_roles = "fraudshield-app,fraudshield-readonly"
    username = "vault"
    password = "vault-db-password"
  }
  
  roles = {
    "fraudshield-app" = {
      db_name = "fraudshield-db"
      creation_statements = [
        "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}';",
        "GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO \"{{name}}\";"
      ]
      default_ttl = "1h"
      max_ttl = "24h"
    }
    
    "fraudshield-readonly" = {
      db_name = "fraudshield-db"
      creation_statements = [
        "CREATE ROLE \"{{name}}\" WITH LOGIN PASSWORD '{{password}}' VALID UNTIL '{{expiration}}';",
        "GRANT SELECT ON ALL TABLES IN SCHEMA public TO \"{{name}}\";"
      ]
      default_ttl = "1h"
      max_ttl = "24h"
    }
  }
}

# KV v2 secrets engine
secrets "kv" {
  type = "kv-v2"
  path = "secret"
  config = {
    max_versions = 10
    cas_required = false
    delete_version_after = "3h"
  }
}

# PKI secrets engine for certificate management
secrets "pki" {
  type = "pki"
  config = {
    max_lease_ttl = "8760h"  # 1 year
  }
  
  roles = {
    "fraudshield-dot-com" = {
      allowed_domains = ["fraudshield.example.com"]
      allow_subdomains = true
      max_ttl = "720h"  # 30 days
      generate_lease = true
    }
  }
}

# Transit secrets engine for encryption as a service
secrets "transit" {
  type = "transit"
  
  keys = {
    "fraudshield-key" = {
      type = "aes256-gcm96"
      deletion_allowed = false
      exportable = false
    }
    
    "fraudshield-signing-key" = {
      type = "ecdsa-p256"
      deletion_allowed = false
      exportable = false
    }
  }
}

# AWS secrets engine
secrets "aws" {
  type = "aws"
  config = {
    access_key = "AKIAIOSFODNN7EXAMPLE"
    secret_key = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    region = "us-west-2"
  }
  
  roles = {
    "fraudshield-role" = {
      credential_type = "iam_user"
      policy_document = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Action = [
              "s3:GetObject",
              "s3:PutObject",
              "s3:DeleteObject"
            ]
            Resource = "arn:aws:s3:::fraudshield-data/*"
          }
        ]
      })
    }
  }
}
