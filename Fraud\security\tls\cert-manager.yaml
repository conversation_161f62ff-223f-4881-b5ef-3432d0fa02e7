# Certificate Manager Configuration for Kubernetes
# Automates SSL/TLS certificate provisioning and renewal

apiVersion: v1
kind: Namespace
metadata:
  name: cert-manager
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    # Production Let's Encrypt server
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
    - dns01:
        cloudflare:
          email: <EMAIL>
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
spec:
  acme:
    # Staging Let's Encrypt server for testing
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-staging
    solvers:
    - http01:
        ingress:
          class: nginx
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: fraudshield-tls
  namespace: fraudshield
spec:
  secretName: fraudshield-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - fraudshield.example.com
  - api.fraudshield.example.com
  - ws.fraudshield.example.com
  - admin.fraudshield.example.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fraudshield-ingress
  namespace: fraudshield
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
    nginx.ingress.kubernetes.io/ssl-ciphers: "ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - fraudshield.example.com
    - api.fraudshield.example.com
    - ws.fraudshield.example.com
    secretName: fraudshield-tls-secret
  rules:
  - host: fraudshield.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fraudshield-frontend
            port:
              number: 3000
  - host: api.fraudshield.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fraudshield-backend
            port:
              number: 8000
  - host: ws.fraudshield.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fraudshield-websocket
            port:
              number: 8000
---
# Certificate monitoring and alerting
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager-metrics
  namespace: cert-manager
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
  endpoints:
  - port: http-metrics
    interval: 30s
    path: /metrics
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: certificate-expiry-alerts
  namespace: cert-manager
spec:
  groups:
  - name: certificate-expiry
    rules:
    - alert: CertificateExpiringSoon
      expr: certmanager_certificate_expiration_timestamp_seconds - time() < 7 * 24 * 3600
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: "Certificate {{ $labels.name }} is expiring soon"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} will expire in less than 7 days"
    
    - alert: CertificateExpired
      expr: certmanager_certificate_expiration_timestamp_seconds - time() <= 0
      for: 0m
      labels:
        severity: critical
      annotations:
        summary: "Certificate {{ $labels.name }} has expired"
        description: "Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} has expired"
    
    - alert: CertificateRenewalFailed
      expr: increase(certmanager_certificate_renewal_total{result="failure"}[1h]) > 0
      for: 0m
      labels:
        severity: critical
      annotations:
        summary: "Certificate renewal failed for {{ $labels.name }}"
        description: "Certificate renewal has failed for {{ $labels.name }} in namespace {{ $labels.namespace }}"
