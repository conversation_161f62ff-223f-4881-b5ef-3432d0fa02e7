"""
Comprehensive Input Validation and Sanitization
Prevents injection attacks and ensures data integrity
"""

import re
import html
import json
import uuid
import ipaddress
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from email_validator import validate_email, EmailNotValidError
import bleach
from urllib.parse import urlparse
import phonenumbers
from phonenumbers import NumberParseException
import logging

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom validation error"""
    def __init__(self, field: str, message: str, value: Any = None):
        self.field = field
        self.message = message
        self.value = value
        super().__init__(f"Validation error in field '{field}': {message}")

class InputValidator:
    """Comprehensive input validation and sanitization"""
    
    # Common regex patterns
    PATTERNS = {
        'alphanumeric': re.compile(r'^[a-zA-Z0-9]+$'),
        'alpha': re.compile(r'^[a-zA-Z]+$'),
        'numeric': re.compile(r'^[0-9]+$'),
        'username': re.compile(r'^[a-zA-Z0-9_-]{3,30}$'),
        'password': re.compile(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$'),
        'transaction_id': re.compile(r'^[A-Z0-9]{8,20}$'),
        'account_number': re.compile(r'^[A-Z0-9]{8,20}$'),
        'sql_injection': re.compile(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)', re.IGNORECASE),
        'xss_script': re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
        'html_tags': re.compile(r'<[^>]+>'),
    }
    
    # Allowed HTML tags for rich text (if needed)
    ALLOWED_HTML_TAGS = ['b', 'i', 'u', 'em', 'strong', 'p', 'br']
    ALLOWED_HTML_ATTRIBUTES = {}
    
    def __init__(self):
        self.errors = []
    
    def validate_string(self, value: Any, field: str, min_length: int = 0, 
                       max_length: int = 1000, pattern: Optional[str] = None,
                       required: bool = True, allow_empty: bool = False) -> str:
        """Validate and sanitize string input"""
        
        # Type check
        if value is None:
            if required and not allow_empty:
                raise ValidationError(field, "Field is required")
            return ""
        
        if not isinstance(value, str):
            value = str(value)
        
        # Sanitize
        value = self._sanitize_string(value)
        
        # Length validation
        if len(value) < min_length:
            raise ValidationError(field, f"Minimum length is {min_length}")
        
        if len(value) > max_length:
            raise ValidationError(field, f"Maximum length is {max_length}")
        
        # Pattern validation
        if pattern and pattern in self.PATTERNS:
            if not self.PATTERNS[pattern].match(value):
                raise ValidationError(field, f"Invalid format for {pattern}")
        
        # Security checks
        self._check_sql_injection(value, field)
        self._check_xss(value, field)
        
        return value
    
    def validate_email(self, value: Any, field: str, required: bool = True) -> str:
        """Validate email address"""
        if value is None:
            if required:
                raise ValidationError(field, "Email is required")
            return ""
        
        if not isinstance(value, str):
            value = str(value)
        
        value = value.strip().lower()
        
        try:
            # Use email-validator library for comprehensive validation
            valid = validate_email(value)
            return valid.email
        except EmailNotValidError as e:
            raise ValidationError(field, f"Invalid email address: {str(e)}")
    
    def validate_phone(self, value: Any, field: str, country_code: str = 'US',
                      required: bool = True) -> str:
        """Validate phone number"""
        if value is None:
            if required:
                raise ValidationError(field, "Phone number is required")
            return ""
        
        if not isinstance(value, str):
            value = str(value)
        
        try:
            parsed = phonenumbers.parse(value, country_code)
            if not phonenumbers.is_valid_number(parsed):
                raise ValidationError(field, "Invalid phone number")
            
            return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
        except NumberParseException as e:
            raise ValidationError(field, f"Invalid phone number: {str(e)}")
    
    def validate_integer(self, value: Any, field: str, min_value: Optional[int] = None,
                        max_value: Optional[int] = None, required: bool = True) -> int:
        """Validate integer input"""
        if value is None:
            if required:
                raise ValidationError(field, "Field is required")
            return 0
        
        try:
            if isinstance(value, str):
                value = int(value)
            elif not isinstance(value, int):
                value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(field, "Must be a valid integer")
        
        if min_value is not None and value < min_value:
            raise ValidationError(field, f"Minimum value is {min_value}")
        
        if max_value is not None and value > max_value:
            raise ValidationError(field, f"Maximum value is {max_value}")
        
        return value
    
    def validate_decimal(self, value: Any, field: str, min_value: Optional[Decimal] = None,
                        max_value: Optional[Decimal] = None, decimal_places: int = 2,
                        required: bool = True) -> Decimal:
        """Validate decimal/monetary input"""
        if value is None:
            if required:
                raise ValidationError(field, "Field is required")
            return Decimal('0')
        
        try:
            if isinstance(value, str):
                # Remove common currency symbols and whitespace
                value = re.sub(r'[$,\s]', '', value)
                decimal_value = Decimal(value)
            elif isinstance(value, (int, float)):
                decimal_value = Decimal(str(value))
            else:
                decimal_value = Decimal(value)
        except (InvalidOperation, TypeError, ValueError):
            raise ValidationError(field, "Must be a valid decimal number")
        
        # Round to specified decimal places
        decimal_value = decimal_value.quantize(Decimal('0.' + '0' * decimal_places))
        
        if min_value is not None and decimal_value < min_value:
            raise ValidationError(field, f"Minimum value is {min_value}")
        
        if max_value is not None and decimal_value > max_value:
            raise ValidationError(field, f"Maximum value is {max_value}")
        
        return decimal_value
    
    def validate_datetime(self, value: Any, field: str, required: bool = True) -> Optional[datetime]:
        """Validate datetime input"""
        if value is None:
            if required:
                raise ValidationError(field, "Date/time is required")
            return None
        
        if isinstance(value, datetime):
            return value
        
        if isinstance(value, str):
            # Try common datetime formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d',
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except ValueError:
                    continue
            
            raise ValidationError(field, "Invalid date/time format")
        
        raise ValidationError(field, "Must be a valid date/time")
    
    def validate_uuid(self, value: Any, field: str, required: bool = True) -> Optional[str]:
        """Validate UUID input"""
        if value is None:
            if required:
                raise ValidationError(field, "UUID is required")
            return None
        
        if not isinstance(value, str):
            value = str(value)
        
        try:
            uuid_obj = uuid.UUID(value)
            return str(uuid_obj)
        except ValueError:
            raise ValidationError(field, "Invalid UUID format")
    
    def validate_ip_address(self, value: Any, field: str, required: bool = True) -> Optional[str]:
        """Validate IP address (IPv4 or IPv6)"""
        if value is None:
            if required:
                raise ValidationError(field, "IP address is required")
            return None
        
        if not isinstance(value, str):
            value = str(value)
        
        try:
            ip = ipaddress.ip_address(value)
            return str(ip)
        except ValueError:
            raise ValidationError(field, "Invalid IP address")
    
    def validate_url(self, value: Any, field: str, allowed_schemes: List[str] = None,
                    required: bool = True) -> Optional[str]:
        """Validate URL input"""
        if value is None:
            if required:
                raise ValidationError(field, "URL is required")
            return None
        
        if not isinstance(value, str):
            value = str(value)
        
        if allowed_schemes is None:
            allowed_schemes = ['http', 'https']
        
        try:
            parsed = urlparse(value)
            if not parsed.scheme or not parsed.netloc:
                raise ValidationError(field, "Invalid URL format")
            
            if parsed.scheme not in allowed_schemes:
                raise ValidationError(field, f"URL scheme must be one of: {allowed_schemes}")
            
            return value
        except Exception:
            raise ValidationError(field, "Invalid URL")
    
    def validate_json(self, value: Any, field: str, required: bool = True) -> Optional[Dict]:
        """Validate JSON input"""
        if value is None:
            if required:
                raise ValidationError(field, "JSON is required")
            return None
        
        if isinstance(value, dict):
            return value
        
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                raise ValidationError(field, f"Invalid JSON: {str(e)}")
        
        raise ValidationError(field, "Must be valid JSON")
    
    def validate_choice(self, value: Any, field: str, choices: List[Any],
                       required: bool = True) -> Any:
        """Validate choice from predefined options"""
        if value is None:
            if required:
                raise ValidationError(field, "Field is required")
            return None
        
        if value not in choices:
            raise ValidationError(field, f"Must be one of: {choices}")
        
        return value
    
    def _sanitize_string(self, value: str) -> str:
        """Sanitize string input"""
        # Remove null bytes
        value = value.replace('\x00', '')
        
        # Normalize whitespace
        value = re.sub(r'\s+', ' ', value).strip()
        
        # HTML escape
        value = html.escape(value)
        
        return value
    
    def _check_sql_injection(self, value: str, field: str):
        """Check for SQL injection patterns"""
        if self.PATTERNS['sql_injection'].search(value):
            logger.warning(f"Potential SQL injection attempt in field {field}: {value}")
            raise ValidationError(field, "Invalid characters detected")
    
    def _check_xss(self, value: str, field: str):
        """Check for XSS patterns"""
        if self.PATTERNS['xss_script'].search(value):
            logger.warning(f"Potential XSS attempt in field {field}: {value}")
            raise ValidationError(field, "Invalid content detected")
    
    def sanitize_html(self, value: str, allowed_tags: List[str] = None,
                     allowed_attributes: Dict[str, List[str]] = None) -> str:
        """Sanitize HTML content"""
        if allowed_tags is None:
            allowed_tags = self.ALLOWED_HTML_TAGS
        
        if allowed_attributes is None:
            allowed_attributes = self.ALLOWED_HTML_ATTRIBUTES
        
        return bleach.clean(value, tags=allowed_tags, attributes=allowed_attributes)

class TransactionValidator(InputValidator):
    """Specialized validator for transaction data"""
    
    def validate_transaction_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate complete transaction data"""
        validated = {}
        
        # Transaction ID
        validated['transaction_id'] = self.validate_string(
            data.get('transaction_id'), 'transaction_id',
            pattern='transaction_id', required=False
        )
        
        # Step
        validated['step'] = self.validate_integer(
            data.get('step'), 'step', min_value=1, max_value=1000000
        )
        
        # Type
        transaction_types = ['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT', 'CASH_IN']
        validated['type'] = self.validate_choice(
            data.get('type'), 'type', transaction_types
        )
        
        # Amount
        validated['amount'] = self.validate_decimal(
            data.get('amount'), 'amount', min_value=Decimal('0.01'),
            max_value=Decimal('1000000.00')
        )
        
        # Account names
        validated['name_orig'] = self.validate_string(
            data.get('name_orig'), 'name_orig',
            pattern='account_number', max_length=20
        )
        
        validated['name_dest'] = self.validate_string(
            data.get('name_dest'), 'name_dest',
            pattern='account_number', max_length=20
        )
        
        # Balances
        validated['oldbalance_orig'] = self.validate_decimal(
            data.get('oldbalance_orig'), 'oldbalance_orig',
            min_value=Decimal('0.00')
        )
        
        validated['newbalance_orig'] = self.validate_decimal(
            data.get('newbalance_orig'), 'newbalance_orig',
            min_value=Decimal('0.00')
        )
        
        validated['oldbalance_dest'] = self.validate_decimal(
            data.get('oldbalance_dest'), 'oldbalance_dest',
            min_value=Decimal('0.00')
        )
        
        validated['newbalance_dest'] = self.validate_decimal(
            data.get('newbalance_dest'), 'newbalance_dest',
            min_value=Decimal('0.00')
        )
        
        return validated

class UserValidator(InputValidator):
    """Specialized validator for user data"""
    
    def validate_user_data(self, data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """Validate user registration/update data"""
        validated = {}
        
        # Email
        validated['email'] = self.validate_email(
            data.get('email'), 'email'
        )
        
        # Password (required for new users)
        if not is_update or data.get('password'):
            validated['password'] = self.validate_string(
                data.get('password'), 'password',
                pattern='password', min_length=8, max_length=128
            )
        
        # Full name
        validated['full_name'] = self.validate_string(
            data.get('full_name'), 'full_name',
            min_length=2, max_length=100
        )
        
        # Role
        user_roles = ['user', 'operator', 'analyst', 'admin']
        validated['role'] = self.validate_choice(
            data.get('role'), 'role', user_roles
        )
        
        # Phone (optional)
        if data.get('phone'):
            validated['phone'] = self.validate_phone(
                data.get('phone'), 'phone', required=False
            )
        
        return validated

# Factory functions
def create_validator(validator_type: str = 'general') -> InputValidator:
    """Create appropriate validator instance"""
    if validator_type == 'transaction':
        return TransactionValidator()
    elif validator_type == 'user':
        return UserValidator()
    else:
        return InputValidator()
