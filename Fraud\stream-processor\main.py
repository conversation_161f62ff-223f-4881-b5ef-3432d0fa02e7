"""
Stream Processing Service for FraudShield
Processes real-time transaction streams and enriches data
"""

import asyncio
import json
import signal
import sys
from datetime import datetime
from typing import Dict, Any, Optional

import structlog
from kafka import KafkaConsumer, KafkaProducer
from kafka.errors import KafkaError

from core.config import settings
from core.logging import setup_logging
from services.transaction_processor import TransactionProcessor
from services.enrichment_service import EnrichmentService
from services.validation_service import ValidationService

# Setup logging
setup_logging()
logger = structlog.get_logger()


class StreamProcessor:
    """Main stream processing service"""
    
    def __init__(self):
        self.consumer: Optional[KafkaConsumer] = None
        self.producer: Optional[KafkaProducer] = None
        self.transaction_processor: Optional[TransactionProcessor] = None
        self.enrichment_service: Optional[EnrichmentService] = None
        self.validation_service: Optional[ValidationService] = None
        self.running = False
    
    async def initialize(self):
        """Initialize stream processor"""
        try:
            # Initialize Kafka consumer
            self.consumer = KafkaConsumer(
                settings.KAFKA_TOPIC_RAW_TRANSACTIONS,
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS.split(','),
                auto_offset_reset='latest',
                enable_auto_commit=True,
                group_id='stream-processor',
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                consumer_timeout_ms=1000
            )
            
            # Initialize Kafka producer
            self.producer = KafkaProducer(
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS.split(','),
                value_serializer=lambda x: json.dumps(x).encode('utf-8'),
                acks='all',
                retries=3
            )
            
            # Initialize services
            self.validation_service = ValidationService()
            await self.validation_service.initialize()
            
            self.enrichment_service = EnrichmentService()
            await self.enrichment_service.initialize()
            
            self.transaction_processor = TransactionProcessor(
                self.producer,
                self.enrichment_service,
                self.validation_service
            )
            await self.transaction_processor.initialize()
            
            logger.info("Stream processor initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize stream processor", error=str(e))
            raise
    
    async def start(self):
        """Start stream processing"""
        self.running = True
        logger.info("Starting stream processor")
        
        try:
            while self.running:
                # Poll for messages
                message_batch = self.consumer.poll(timeout_ms=1000)
                
                if message_batch:
                    await self._process_message_batch(message_batch)
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.01)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        except Exception as e:
            logger.error("Stream processing error", error=str(e))
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """Stop stream processing"""
        self.running = False
        logger.info("Stopping stream processor")
        
        if self.consumer:
            self.consumer.close()
        
        if self.producer:
            self.producer.flush()
            self.producer.close()
        
        if self.transaction_processor:
            await self.transaction_processor.close()
        
        if self.enrichment_service:
            await self.enrichment_service.close()
        
        if self.validation_service:
            await self.validation_service.close()
        
        logger.info("Stream processor stopped")
    
    async def _process_message_batch(self, message_batch: Dict):
        """Process a batch of messages"""
        for topic_partition, messages in message_batch.items():
            for message in messages:
                try:
                    await self._process_single_message(message)
                except Exception as e:
                    logger.error(
                        "Failed to process message",
                        topic=topic_partition.topic,
                        partition=topic_partition.partition,
                        offset=message.offset,
                        error=str(e)
                    )
    
    async def _process_single_message(self, message):
        """Process a single transaction message"""
        try:
            transaction_data = message.value
            
            # Add processing metadata
            transaction_data['processing_timestamp'] = datetime.utcnow().isoformat()
            transaction_data['kafka_offset'] = message.offset
            transaction_data['kafka_partition'] = message.partition
            
            # Process the transaction
            await self.transaction_processor.process_transaction(transaction_data)
            
            logger.debug(
                "Transaction processed",
                transaction_id=transaction_data.get('transaction_id'),
                offset=message.offset
            )
            
        except Exception as e:
            logger.error(
                "Transaction processing failed",
                transaction_id=transaction_data.get('transaction_id'),
                error=str(e)
            )
            
            # Send to dead letter queue
            await self._send_to_dlq(transaction_data, str(e))
    
    async def _send_to_dlq(self, transaction_data: Dict[str, Any], error_message: str):
        """Send failed transaction to dead letter queue"""
        try:
            dlq_message = {
                'original_data': transaction_data,
                'error_message': error_message,
                'failed_at': datetime.utcnow().isoformat(),
                'processor': 'stream-processor'
            }
            
            self.producer.send(
                settings.KAFKA_TOPIC_DLQ,
                value=dlq_message
            )
            
            logger.warning(
                "Transaction sent to DLQ",
                transaction_id=transaction_data.get('transaction_id'),
                error=error_message
            )
            
        except Exception as e:
            logger.error("Failed to send to DLQ", error=str(e))


async def main():
    """Main entry point"""
    processor = StreamProcessor()
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}")
        asyncio.create_task(processor.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await processor.initialize()
        await processor.start()
    except Exception as e:
        logger.error("Stream processor failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
