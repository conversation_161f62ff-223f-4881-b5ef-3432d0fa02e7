"""
Transaction Processing Service
Main service for processing individual transactions in the stream
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any, Optional

import structlog
from kafka import KafkaProducer

from core.config import settings
from services.enrichment_service import EnrichmentService
from services.validation_service import ValidationService

logger = structlog.get_logger()


class TransactionProcessor:
    """Processes individual transactions through the pipeline"""
    
    def __init__(self, producer: KafkaProducer, 
                 enrichment_service: EnrichmentService,
                 validation_service: ValidationService):
        self.producer = producer
        self.enrichment_service = enrichment_service
        self.validation_service = validation_service
        self.processing_stats = {
            "total_processed": 0,
            "total_validated": 0,
            "total_enriched": 0,
            "total_failed": 0,
            "avg_processing_time_ms": 0.0
        }
    
    async def initialize(self):
        """Initialize transaction processor"""
        logger.info("Transaction processor initialized")
    
    async def close(self):
        """Close transaction processor"""
        logger.info("Transaction processor closed")
    
    async def process_transaction(self, transaction_data: Dict[str, Any]) -> bool:
        """Process a single transaction through the pipeline"""
        start_time = time.time()
        transaction_id = transaction_data.get('transaction_id', 'unknown')
        
        try:
            # Step 1: Validation
            if settings.ENABLE_VALIDATION:
                validation_result = await self.validation_service.validate_transaction(transaction_data)
                if not validation_result.is_valid:
                    logger.warning(
                        "Transaction validation failed",
                        transaction_id=transaction_id,
                        errors=validation_result.errors
                    )
                    await self._send_validation_failure(transaction_data, validation_result.errors)
                    return False
                
                # Add validation metadata
                transaction_data['validation'] = {
                    'validated_at': datetime.utcnow().isoformat(),
                    'validation_time_ms': validation_result.processing_time_ms,
                    'warnings': validation_result.warnings
                }
                self.processing_stats["total_validated"] += 1
            
            # Step 2: Enrichment
            if settings.ENABLE_ENRICHMENT:
                enriched_data = await self.enrichment_service.enrich_transaction(transaction_data)
                transaction_data.update(enriched_data)
                self.processing_stats["total_enriched"] += 1
            
            # Step 3: Feature Extraction (if enabled)
            if settings.ENABLE_FEATURE_EXTRACTION:
                features = await self._extract_features(transaction_data)
                if features:
                    transaction_data['features'] = features
            
            # Step 4: Send to enriched transactions topic
            await self._send_enriched_transaction(transaction_data)
            
            # Update statistics
            processing_time = (time.time() - start_time) * 1000
            self._update_processing_stats(processing_time)
            
            logger.debug(
                "Transaction processed successfully",
                transaction_id=transaction_id,
                processing_time_ms=processing_time
            )
            
            return True
            
        except Exception as e:
            self.processing_stats["total_failed"] += 1
            logger.error(
                "Transaction processing failed",
                transaction_id=transaction_id,
                error=str(e)
            )
            raise
    
    async def _extract_features(self, transaction_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract features using the feature store service"""
        try:
            # This would call the feature store service
            # For now, we'll implement basic feature extraction
            features = {}
            
            # Basic transaction features
            amount = transaction_data.get('amount', 0)
            oldbalance_org = transaction_data.get('oldbalance_org', 0)
            
            features.update({
                'amount_to_balance_ratio': amount / max(oldbalance_org, 1.0),
                'is_round_amount': amount % 100 == 0,
                'transaction_hour': datetime.utcnow().hour,
                'is_weekend': datetime.utcnow().weekday() >= 5,
            })
            
            return features
            
        except Exception as e:
            logger.error(
                "Feature extraction failed",
                transaction_id=transaction_data.get('transaction_id'),
                error=str(e)
            )
            return None
    
    async def _send_enriched_transaction(self, transaction_data: Dict[str, Any]):
        """Send enriched transaction to the enriched transactions topic"""
        try:
            # Add final processing metadata
            transaction_data['enriched_at'] = datetime.utcnow().isoformat()
            transaction_data['processor_version'] = settings.VERSION
            
            # Send to Kafka
            future = self.producer.send(
                settings.KAFKA_TOPIC_ENRICHED_TRANSACTIONS,
                value=transaction_data
            )
            
            # Wait for send to complete (with timeout)
            future.get(timeout=5)
            
            logger.debug(
                "Enriched transaction sent",
                transaction_id=transaction_data.get('transaction_id'),
                topic=settings.KAFKA_TOPIC_ENRICHED_TRANSACTIONS
            )
            
        except Exception as e:
            logger.error(
                "Failed to send enriched transaction",
                transaction_id=transaction_data.get('transaction_id'),
                error=str(e)
            )
            raise
    
    async def _send_validation_failure(self, transaction_data: Dict[str, Any], errors: list):
        """Send validation failure to appropriate topic"""
        try:
            failure_data = {
                'original_transaction': transaction_data,
                'validation_errors': errors,
                'failed_at': datetime.utcnow().isoformat(),
                'failure_type': 'validation'
            }
            
            future = self.producer.send(
                settings.KAFKA_TOPIC_DLQ,
                value=failure_data
            )
            
            future.get(timeout=5)
            
            logger.info(
                "Validation failure sent to DLQ",
                transaction_id=transaction_data.get('transaction_id')
            )
            
        except Exception as e:
            logger.error(
                "Failed to send validation failure",
                transaction_id=transaction_data.get('transaction_id'),
                error=str(e)
            )
    
    def _update_processing_stats(self, processing_time_ms: float):
        """Update processing statistics"""
        self.processing_stats["total_processed"] += 1
        
        # Update average processing time
        total = self.processing_stats["total_processed"]
        current_avg = self.processing_stats["avg_processing_time_ms"]
        new_avg = ((current_avg * (total - 1)) + processing_time_ms) / total
        self.processing_stats["avg_processing_time_ms"] = new_avg
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return self.processing_stats.copy()
