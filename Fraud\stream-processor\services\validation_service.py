"""
Transaction Validation Service
Validates incoming transactions for data quality and business rules
"""

import time
from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

import structlog

logger = structlog.get_logger()


@dataclass
class ValidationResult:
    """Result of transaction validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    processing_time_ms: float


class ValidationService:
    """Service for validating transaction data"""
    
    def __init__(self):
        self.validation_rules = [
            self._validate_required_fields,
            self._validate_data_types,
            self._validate_business_rules,
            self._validate_amounts,
            self._validate_accounts,
            self._validate_balances
        ]
        self.validation_stats = {
            "total_validated": 0,
            "total_passed": 0,
            "total_failed": 0,
            "common_errors": {}
        }
    
    async def initialize(self):
        """Initialize validation service"""
        logger.info("Validation service initialized")
    
    async def close(self):
        """Close validation service"""
        logger.info("Validation service closed")
    
    async def validate_transaction(self, transaction_data: Dict[str, Any]) -> ValidationResult:
        """Validate a transaction"""
        start_time = time.time()
        errors = []
        warnings = []
        
        try:
            # Run all validation rules
            for rule in self.validation_rules:
                rule_errors, rule_warnings = rule(transaction_data)
                errors.extend(rule_errors)
                warnings.extend(rule_warnings)
            
            # Update statistics
            self.validation_stats["total_validated"] += 1
            if not errors:
                self.validation_stats["total_passed"] += 1
            else:
                self.validation_stats["total_failed"] += 1
                # Track common errors
                for error in errors:
                    self.validation_stats["common_errors"][error] = \
                        self.validation_stats["common_errors"].get(error, 0) + 1
            
            processing_time = (time.time() - start_time) * 1000
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error("Validation failed", error=str(e))
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation system error: {str(e)}"],
                warnings=[],
                processing_time_ms=(time.time() - start_time) * 1000
            )
    
    def _validate_required_fields(self, data: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Validate that all required fields are present"""
        errors = []
        warnings = []
        
        required_fields = [
            'transaction_id', 'type', 'amount', 'name_orig', 'name_dest',
            'oldbalance_org', 'newbalance_orig', 'oldbalance_dest', 'newbalance_dest'
        ]
        
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif data[field] is None:
                errors.append(f"Required field is null: {field}")
            elif isinstance(data[field], str) and not data[field].strip():
                errors.append(f"Required field is empty: {field}")
        
        return errors, warnings
    
    def _validate_data_types(self, data: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Validate data types"""
        errors = []
        warnings = []
        
        # Numeric fields
        numeric_fields = ['amount', 'oldbalance_org', 'newbalance_orig', 'oldbalance_dest', 'newbalance_dest']
        for field in numeric_fields:
            if field in data:
                try:
                    value = float(data[field])
                    if value < 0:
                        errors.append(f"Negative value not allowed for {field}: {value}")
                except (ValueError, TypeError):
                    errors.append(f"Invalid numeric value for {field}: {data[field]}")
        
        # String fields
        string_fields = ['transaction_id', 'type', 'name_orig', 'name_dest']
        for field in string_fields:
            if field in data and not isinstance(data[field], str):
                errors.append(f"Field {field} must be a string: {type(data[field])}")
        
        # Transaction type validation
        if 'type' in data:
            valid_types = ['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT', 'CASH_IN']
            if data['type'] not in valid_types:
                errors.append(f"Invalid transaction type: {data['type']}. Must be one of {valid_types}")
        
        return errors, warnings
    
    def _validate_business_rules(self, data: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Validate business rules"""
        errors = []
        warnings = []
        
        # Transaction ID format
        if 'transaction_id' in data:
            tx_id = data['transaction_id']
            if len(tx_id) < 5:
                errors.append(f"Transaction ID too short: {tx_id}")
            elif len(tx_id) > 100:
                errors.append(f"Transaction ID too long: {tx_id}")
        
        # Account ID format
        for field in ['name_orig', 'name_dest']:
            if field in data:
                account_id = data[field]
                if len(account_id) < 2:
                    errors.append(f"Account ID too short: {account_id}")
                elif len(account_id) > 50:
                    errors.append(f"Account ID too long: {account_id}")
        
        # Self-transaction check
        if 'name_orig' in data and 'name_dest' in data:
            if data['name_orig'] == data['name_dest']:
                errors.append("Self-transactions not allowed")
        
        return errors, warnings
    
    def _validate_amounts(self, data: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Validate transaction amounts"""
        errors = []
        warnings = []
        
        if 'amount' in data:
            try:
                amount = float(data['amount'])
                
                # Amount must be positive
                if amount <= 0:
                    errors.append(f"Transaction amount must be positive: {amount}")
                
                # Check for unreasonably large amounts
                if amount > 1000000:  # 1 million
                    warnings.append(f"Very large transaction amount: {amount}")
                
                # Check for very small amounts
                if amount < 0.01:
                    warnings.append(f"Very small transaction amount: {amount}")
                
                # Check for precision (max 2 decimal places for currency)
                if len(str(amount).split('.')[-1]) > 2:
                    warnings.append(f"Amount has more than 2 decimal places: {amount}")
                
            except (ValueError, TypeError):
                errors.append(f"Invalid amount format: {data['amount']}")
        
        return errors, warnings
    
    def _validate_accounts(self, data: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Validate account information"""
        errors = []
        warnings = []
        
        # Check account naming patterns
        for field in ['name_orig', 'name_dest']:
            if field in data:
                account_id = data[field]
                
                # Basic format checks
                if not account_id.replace('_', '').replace('-', '').isalnum():
                    warnings.append(f"Account ID contains special characters: {account_id}")
                
                # Check for merchant accounts (starting with 'M')
                if field == 'name_dest' and account_id.startswith('M'):
                    # Merchant accounts should have zero balances
                    if 'oldbalance_dest' in data and data['oldbalance_dest'] != 0:
                        warnings.append(f"Merchant account has non-zero old balance: {account_id}")
                    if 'newbalance_dest' in data and data['newbalance_dest'] != 0:
                        warnings.append(f"Merchant account has non-zero new balance: {account_id}")
        
        return errors, warnings
    
    def _validate_balances(self, data: Dict[str, Any]) -> tuple[List[str], List[str]]:
        """Validate balance consistency"""
        errors = []
        warnings = []
        
        try:
            # Extract balance fields
            amount = float(data.get('amount', 0))
            old_orig = float(data.get('oldbalance_org', 0))
            new_orig = float(data.get('newbalance_orig', 0))
            old_dest = float(data.get('oldbalance_dest', 0))
            new_dest = float(data.get('newbalance_dest', 0))
            
            # Check originator balance consistency
            expected_new_orig = old_orig - amount
            if abs(new_orig - expected_new_orig) > 0.01:  # Allow small floating point errors
                # This might be legitimate for some transaction types
                warnings.append(
                    f"Originator balance change inconsistent. "
                    f"Expected: {expected_new_orig}, Actual: {new_orig}"
                )
            
            # Check destination balance consistency (for non-merchant accounts)
            dest_account = data.get('name_dest', '')
            if not dest_account.startswith('M'):  # Not a merchant account
                expected_new_dest = old_dest + amount
                if abs(new_dest - expected_new_dest) > 0.01:
                    warnings.append(
                        f"Destination balance change inconsistent. "
                        f"Expected: {expected_new_dest}, Actual: {new_dest}"
                    )
            
            # Check for negative balances
            if new_orig < 0:
                warnings.append(f"Negative originator balance after transaction: {new_orig}")
            
            if new_dest < 0:
                warnings.append(f"Negative destination balance after transaction: {new_dest}")
            
        except (ValueError, TypeError, KeyError) as e:
            errors.append(f"Balance validation failed: {str(e)}")
        
        return errors, warnings
    
    def get_stats(self) -> Dict[str, Any]:
        """Get validation statistics"""
        stats = self.validation_stats.copy()
        
        # Calculate success rate
        total = stats["total_validated"]
        if total > 0:
            stats["success_rate"] = (stats["total_passed"] / total) * 100
        else:
            stats["success_rate"] = 0.0
        
        return stats
